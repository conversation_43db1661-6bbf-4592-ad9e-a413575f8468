<template>
  <DefineTemplate>
    <c-pro-table
      ref="tableRef"
      align="center"
      :columns="columns"
      :api="api.Products.ProductActivationLogsByQr_GetAsync" row-key="id"
      :get-params="{ qrnumber: code }"
    />
  </DefineTemplate>
  <c-modal v-if="isModal" v-model:open="open" :footer="null" title="核验二维码使用情况" width="1440px"><ReuseTemplate /></c-modal>
  <ReuseTemplate v-else />

  <ProductInfo v-if="Guid.isNotNull(productId)" :id="productId" v-model:open="openProductDetail" :read-only="true" title="产品信息" />
</template>

<script setup lang="ts">
import type { ProductActivationLog } from '@/api/models'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api'
import ProductInfo from '@/views/product-manager/components/ProductInfo.vue'
import { Tag } from 'ant-design-vue'
import TextEllipsis from 'ch2-components/lib/text-ellipsis/src/TextEllipsis.vue'

const { isModal = true, code } = defineProps<{ isModal?: boolean, code: string }>()

const open = defineModel<boolean>('open', { default: false })

const [DefineTemplate, ReuseTemplate] = createReusableTemplate()

const productId = ref(Guid.empty)

const openProductDetail = ref(false)

const boleComp = ({ text }: any) => h(Tag, { color: text ? 'success' : 'error' }, { default: () => text ? '是' : '否' })

const columns = reactive<ColumnProps<ProductActivationLog>[]>([
  {
    dataIndex: 'productName',
    title: '产品名称',
    key: 'productName',
    bodyCell: ({ record }) => h('a', { onClick() {
      productId.value = record.productId
      openProductDetail.value = true
    } }, { default: () => record.productName }),
  },
  { dataIndex: 'productBatchTime', title: '生成批次时间', key: 'productName', dateFormat: true, width: '160px' },
  { dataIndex: 'isActivation', key: 'isActivation', title: '激活时间', align: 'center', width: '160px', bodyCell: ({ record }) => {
    return h('span', { style: { color: record.isActivation ? 'green' : 'red' } }, { default: () => record.isActivation ? dateTime(record.activationTime) : '未激活' })
  } },
  { dataIndex: 'isReactivated', key: 'isActivation', title: '二次扫码', align: 'center', width: '60px', bodyCell: boleComp },
  { dataIndex: 'isError', key: 'isError', title: '是否过期', align: 'center', width: '60px', bodyCell: boleComp },
  { dataIndex: 'message', key: 'message', title: '异常信息', align: 'center' },
  { dataIndex: 'userName', key: 'userName', title: '激活用户昵称', align: 'center' },

  { dataIndex: 'userPhoneNumber', key: 'userName', title: '手机号码', align: 'center', width: '160px' },
  { dataIndex: 'province', key: 'userName', title: '激活位置', width: '160px', align: 'center', bodyCell: ({ record }) => {
    return h(TextEllipsis, { maxWidth: '160px', showIcon: true, value: record.streetAddress })
  } },
])

const tableRef = useTemplateRef('tableRef')

watch([() => code, open], () => {
  if (((isModal && open.value) || !isModal))
    nextTick(() => tableRef.value?.refresh())
}, { immediate: true })
</script>

<style scoped>

</style>
