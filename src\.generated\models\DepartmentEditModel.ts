export class DepartmentEditModel {
  id?: GUID = null;
  /**部门名称*/
  name?: string | null | undefined = null;
  /**是否合作  只给舆情组角色看*/
  isCooperate: boolean = false;
  /**备注信息 只给舆情组角色看*/
  remark?: string | null | undefined = null;
  /**合同信息 只给舆情组角色看*/
  contract?: string | null | undefined = null;
  /**合作开始时间 只给舆情组角色看*/
  contractStartTime?: Dayjs | null | undefined = null;
  /**合作结束期限 只给舆情组角色看*/
  contractEndTime?: Dayjs | null | undefined = null;
  /**联络人 只给舆情组角色看*/
  contact?: string | null | undefined = null;
  /**联络人号码 只给舆情组角色看*/
  contactPhone?: string | null | undefined = null;
  /**单位类型 标准dept-type*/
  type?: string | null | undefined = null;
  /**父节点ID*/
  parentId?: GUID = null;
  /**归属地*/
  address?: string | null | undefined = null;
  /**所属区县*/
  counties?: string | null | undefined = null;
}
