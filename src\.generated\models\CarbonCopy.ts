import { Department } from "./Department";
import { PublicEvent } from "./PublicEvent";
import { ReadingStatus } from "./ReadingStatus";
import { User } from "./User";
/**事件抄送（管理给合作单位查询）*/
export class CarbonCopy {
  /**抄送备注*/
  context?: string | null | undefined = null;
  /**抄送时间*/
  time: Dayjs = dayjs();
  /**抄送单位*/
  departmentId?: GUID = null;
  /**抄送单位*/
  department?: Department | null | undefined = null;
  eventId: GUID = "00000000-0000-0000-0000-000000000000";
  event?: PublicEvent | null | undefined = null;
  /**要求单位 阅读状态 (只用到 已读、未读)*/
  auditStatus?: ReadingStatus | null | undefined = null;
  auditAt?: Dayjs | null | undefined = null;
  auditBy?: GUID = null;
  /**用户*/
  auditUser?: User | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**创建者*/
  createdId?: GUID = null;
  /**创建者*/
  createdBy?: User | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
