<template>
  <div>
    <ViewFile :files="fileList" show-remove @remove="remove" />

    <a-button
      type="dashed" block class="mt2 size-16" @click="upload"
    >
      <template #icon>
        <c-icon-plus-outlined />
      </template>
      上传
    </a-button>
  </div>
</template>

<script setup lang="ts">
import type { UploadFileInfo } from '@/api/models'
import type FileManager from '@/components/FileManager/FileManager.vue'
import { FileAttribution, FileType } from '@/api/models'

const { config = { multiple: true, immediateReturn: true, menu: [FileType.文档, FileType.图片, FileType.视频], fileAttribution: FileAttribution.管理认证 } }
= defineProps<{ config?: InstanceType<typeof FileManager>['$props'] }>()

const fileList = defineModel<UploadFileInfo[]>('fileList', { default: () => [] })

const fileIds = defineModel<GUID | string | GUID[] | string[]>('value')

function remove(id: string) {
  fileList.value = fileList.value.filter(v => v.id !== id)
  if (config.multiple && Array.isArray(fileIds.value)) {
    fileIds.value = fileIds.value?.filter(v => v !== id)
  }
  else {
    fileIds.value = null
  }
}

function upload() {
  useFileMangerModal((files) => {
    if (config.multiple) {
      fileIds.value = [...fileIds.value || [], ...files.map(v => v.id)] as any
      fileList.value = [...fileList.value || [], ...files] as any
    }
    else {
      fileIds.value = files[0]?.id
      fileList.value = files
    }
  }, config)
}
</script>

<style scoped>

</style>
