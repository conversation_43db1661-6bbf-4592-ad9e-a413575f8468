import { DepartmentViewModel } from "./DepartmentViewModel";
export class UserBaseViewModel {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  name?: string | null | undefined = null;
  email?: string | null | undefined = null;
  phoneNumber?: string | null | undefined = null;
  userName?: string | null | undefined = null;
  /**外键关联部门*/
  departmentId?: GUID = null;
  /**要求对象单位*/
  department?: DepartmentViewModel | null | undefined = null;
  /**邮箱是否验证*/
  emailConfirmed: boolean = false;
  /**手机号是否验证*/
  phoneNumberConfirmed: boolean = false;
  /**账号过期时间*/
  expiration?: Dayjs | null | undefined = null;
  /**密码修改时限*/
  modifyPasswordEnd?: Dayjs | null | undefined = null;
  /**用户锁定*/
  lockoutEnabled: boolean = false;
}
