<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-02-17 15:03:35
 * @LastEditors: 景 彡
-->
<template>
  <div class="h-100% w-100% flex flex-col items-center justify-center bg-bg-container p-16px">
    <div class="h-160px text-32px c-text-secondary">{{ title }}</div>
    <div class="w-80%">
      <a-input-search v-model:value="textValue" class="c2-search-input" size="large" placeholder="请输入关键词" enter-button :loading="searchLoding" @search="onSearch" />
    </div>
    <div class="mt-56px">
      <img src="@/assets/images/search.png" alt="">
    </div>
  </div>
</template>

<script lang='ts' setup>
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'

definePage({
  meta: {
    title: '全文检索',
    icon: 'SearchOutlined',
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '全文检索',
        local: true,
        icon: 'SearchOutlined',
        order: 5,
      },
    },
  },
})

const title = import.meta.env.VITE_APP_TITLE

const searchLoding = ref(false)

const textValue = ref('')

const router = useRouter()

function onSearch() {
  if (!textValue.value)
    return message.warning('请输入关键词后进行搜索！')
  router.push({ path: '/full-text-search/list', query: { value: textValue.value } })
}
</script>

<style scoped lang="less">
.c2-search-input {
  :deep(.ant-input) {
    @apply rounded-l-30px  h-48px overflow-hidden;
  }
  :deep(.ant-btn.ant-btn-primary.ant-btn-lg.ant-input-search-button) {
    @apply rounded-r-30px h-48px;
  }
}
</style>
