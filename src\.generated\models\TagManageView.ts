import { TagType } from "./TagType";
/**标签管理视图模型，用于构建分类树返回前端*/
export class TagManageView {
  /**唯一标识*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**标签值（名称）*/
  value?: string | null | undefined = null;
  /**标签描述*/
  description?: string | null | undefined = null;
  /**上级标签ID（null 表示一级标签）*/
  parentId?: GUID = null;
  /**标签类型（枚举：一级分类、二级分类等）*/
  tagType?: TagType | null | undefined = null;
  /**是否显示涉及人数（仅二级分类有效）*/
  isShowNumberPeopleInvolved?: boolean | null | undefined = null;
  /**是否显示受伤人数（仅二级分类有效）*/
  isShowTheInjured?: boolean | null | undefined = null;
  /**是否显示死亡人数（仅二级分类有效）*/
  isShowNumberOfDeaths?: boolean | null | undefined = null;
  /**子标签集合，用于构建树结构*/
  children?: TagManageView[] | null | undefined = [];
  /**是否显示安全隐患*/
  isShowIsHiddenDanger?: boolean | null | undefined = null;
}
