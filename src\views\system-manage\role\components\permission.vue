<template>
  <a-spin :spinning="spinning">
    <a-tree
      v-model:expanded-keys="expandedKeys" v-model:selected-keys="selectedKeys" v-model:checked-keys="checkedKeys"
      checkable :tree-data="treeData" @expand="handleExpand"
    >
      <template #title="{ title, key, data }">
        <span v-if="key === '0-0-1-0'" style="color: #1890ff">{{ title }}</span>
        <template v-else>{{ title }} 【{{ data.key }}】</template>
      </template>
    </a-tree>
  </a-spin>
</template>

<script setup lang="ts">
import * as api from '@/api'
import * as models from '@/api/models'
import { message } from 'ant-design-vue'

const { roleName } = defineProps<{ roleName: string }>()

const treeData = ref()

const expandedKeys = ref<string[]>([])

const selectedKeys = ref<string[]>([])

const checkedKeys = ref<string[]>([])

const spinning = ref(false)

watch(() => roleName, () => {
  const controller = new AbortController()
  spinning.value = true
  api.Limits.GetByRolenameAsync({ roleName }, { signal: controller.signal }).then((res) => {
    selectedKeys.value = []
    spinning.value = false
    checkedKeys.value = res?.grants!.map(p => p.id as string) || []
  }).catch(() => {
    spinning.value = false
  })
  onWatcherCleanup(() => {
    spinning.value = false
    controller.abort()
  })
}, { immediate: true })

/**
 * 获取接口树
 */
async function getOptions() {
  treeData.value = []
  checkedKeys.value = []
  await api.Limits.Tree_GetAsync().then((res) => {
    if (res) {
      res.forEach((m) => {
        treeData.value?.push({
          title: m.displayName,
          key: m.id as string,
          disabled: roleName === '全部' || !roleName,
          children: m.children?.map((p) => {
            return ({
              title: p.displayName,
              key: p.id,
              disabled: roleName === '全部' || !roleName,
            })
          }),
        })
      })
    }
  })
}

/**
 * 展开节点
 */
function handleExpand(_keys: string[], node: any) {
  if (node.expanded) {
    // 仅展开一个节点
    expandedKeys.value = [node.node.key]
  }
}

/**
 * 保存权限
 */
async function savePermission() {
  if (!roleName) {
    message.error('请先选择角色')
    return
  }
  const data: models.ResourcePermission = new models.ResourcePermission()
  data.grants = checkedKeys.value.map((p) => {
    return ({
      id: p,
      noAfter: null,
      noBefore: null,
    })
  })

  await api.Limits.SetByRolename_PostAsync({ roleName }, data).then((res) => {
    if (res) {
      message.success('保存成功')
    }
  })
}

getOptions()

defineExpose({ savePermission })
</script>

<style scoped></style>
