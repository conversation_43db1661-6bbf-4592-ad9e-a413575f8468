<template>
  <div ref="chartRef" class="size-full" />
</template>

<script lang='ts' setup>
import * as echarts from 'echarts'

const props = defineProps<{
  options: echarts.EChartsOption
}>()

const chartRef = ref<HTMLElement | null>(null)

let chartInstance: echarts.ECharts | null = null

watch(
  () => props.options,
  (newVal) => {
    if (chartInstance) {
      chartInstance.setOption(newVal)
    }
  },
)

onMounted(async () => {
  await nextTick()
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    window.addEventListener('resize', () => {
      chartInstance?.resize()
    })
    chartInstance.setOption(props.options)
  }
})

onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
})
</script>

<style scoped>

</style>
