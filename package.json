{"name": "group-app", "type": "module", "version": "0.1.24", "private": true, "scripts": {"serve": "vite --config ./config/vite.config.dev.ts", "dev": "vite --config ./config/vite.config.dev.ts --debug hmr", "build": "vite build --config ./config/vite.config.prod.ts", "build:test": "pnpm size-check", "doc": "ch2-doc", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "lint-staged": "lint-staged", "check:env": "node ./config/checkLocalEnv.js", "cui": "pnpm add ch2-components", "size-check": "npx vite-bundle-visualizer -c ./config/vite.config.prod.ts", "postinstall": "npx simple-git-hooks & pnpm check:env"}, "dependencies": {"pdfjs-dist": "^5.3.93"}, "overrides": {"@babel/helper-module-imports": "~7.22.15"}, "pnpm": {"overrides": {"@babel/helper-module-imports": "~7.22.15"}}, "resolutions": {"@babel/helper-module-imports": "~7.22.15"}, "simple-git-hooks": {"pre-commit": "pnpm lint-staged"}, "lint-staged": {"*.{js,ts,vue,jsx,tsx}": "eslint --fix --cache"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}