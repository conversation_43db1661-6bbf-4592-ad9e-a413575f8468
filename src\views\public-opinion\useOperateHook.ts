import type { PublicOpinionViewModel } from '@/api/models'
import * as api from '@/api'
import { AuditType } from '@/api/models'
import { message } from 'ant-design-vue'

export function useOperateHook(onSearch: () => void) {
  async function copyToClipboard(text: string) {
    try {
      await navigator.clipboard.writeText(text)
      message.success('复制成功')
    }
    catch (err: any) {
      message.error(`复制失败: ${err.message}`)
    }
  }

  async function onDel(id: GUID) {
    await api.OpinionManage.Delete_PostAsync({ id })
    message.success('删除成功')
    onSearch()
  }

  async function onCopy(id: GUID) {
    const briefing = await api.OpinionManage.CopySimpleDailyReport_GetAsync({ id })
    copyToClipboard(briefing)
  }

  return { onDel, onCopy }
}

export function useAuditHook() {
  const auditOpen = ref(false)

  const jytOpinionId = ref(Guid.empty)

  const auditParams = ref({
    id: Guid.empty,
    audit: AuditType.需要处置,
  })

  function onAudit(record: PublicOpinionViewModel, audit: AuditType) {
    auditParams.value = { id: record.id, audit }
    jytOpinionId.value = record.jytEntityId
    auditOpen.value = true
  }

  return { auditOpen, jytOpinionId, auditParams, onAudit }
}
