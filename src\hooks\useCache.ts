const cacheStore = new Map<string, { value: any, expiration: number }>()
const cacheLoadStore = new Map<string, boolean>()

/**
 * 创建一个使用内存进行数据缓存的组合式函数。
 *
 * @template T - 缓存数据的类型。
 * @param {string} key - 用于标识缓存数据的键。
 * @param {number} [maxAge] - 数据的最大缓存时间（以毫秒为单位），默认为 8 小时。
 * @returns {{ data: ComputedRef<T | null>, setData: (newData: T) => void }} - 一个包含计算属性 `data` 和方法 `setData` 的对象。
 */
export function useCache<T>(key: string, maxAge = 8 * 60 * 60 * 1000) {
  const now = Date.now()

  if (!cacheStore.has(key) || now > cacheStore.get(key)!.expiration) {
    cacheStore.delete(key)
  }

  // 计算数据是否过期
  const getData = () => {
    if (!cacheStore.has(key) || !cacheStore.get(key)!.value || Date.now() > cacheStore.get(key)!.expiration) {
      cacheStore.delete(key)
      return null
    }
    return cacheStore.get(key)?.value as T
  }

  // 设置数据
  const setData = (newData: T) => {
    cacheStore.set(key, { value: newData, expiration: Date.now() + maxAge })
  }

  const setLoadState = (newData: boolean) => {
    cacheLoadStore.set(key, newData)
  }

  const loadState = () => cacheLoadStore.get(key)

  const getLoadState = () => {
    return new Promise((resolve) => {
      const t = setInterval(() => {
        if (loadState() === false) {
          resolve(false)
          clearInterval(t)
        }
      }, 200)
    })
  }

  return { getData, setData, setLoadState, loadState, getLoadState }
}
