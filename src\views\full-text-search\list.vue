<template>
  <div class="bg-bg-container p-16px">
    <a-input-search
      v-model:value="keyword"
      placeholder="请输入关键词"
      enter-button
      @search="onSearch"
    />
  </div>
  <div class="mt-16px bg-bg-container p-16px">
    <c-list ref="listRef" :api="api.OpinionManage.GetListAsync" :get-params="{ summary: keyword }" pagination>
      <template #renderItem="{ item, index }:{item:PublicOpinionViewModel, index: number }">
        <a-list-item :key="index">
          <div class="w-100%">
            <div class="ch2-list-title flex cursor-pointer items-center hover:c-primary" @click="onView(item)">
              <span class="mr-2 text-16px line-height-1.5" :class=" item.positive ? 'c-success' : 'c-error'">[{{ item.positive ? '正面' : '反面' }}]</span>
              <div class="text-4" v-html="highlightKeywords(item.summary!)" />
              <!-- <c-truncated-text :text="item.summary!" :max-lines="2" type="launch" /> -->
            </div>
            <div class="my-8px c-#A6A6A6">{{ item.content }}</div>
            <div>
              <a-space wrap :size="16">
                <span class="c-primary" size="8">
                  <UserOutlined />{{ item.publisher }}</span>
                <span class="c-#969595">发布时间：{{ dateTime(item.published) }}</span>
              </a-space>
            </div>
          </div>
        </a-list-item>
      </template>
    </c-list>
  </div>

  <c-modal v-model:open="open" title="舆情详情" width="1200px" :footer="null" destroy-on-close @close="open = false">
    <EditForm ref="editFormRef" v-model="currentObj" :read-only="true" />
  </c-modal>
</template>

<script lang='ts' setup>
import type { PublicOpinionViewModel } from '@/api/models'
import * as api from '@/api'
import { PublicOpinionEditModel } from '@/api/models'
import { UserOutlined } from '@ant-design/icons-vue'
import { useRoute } from 'vue-router'
import EditForm from '../public-opinion/components/EditForm.vue'

definePage({
  meta: {
    title: '全文检索-检索结果',
    icon: 'SearchOutlined',
    hidden: true,

  },
})

const { open, editFormRef, currentObj, onView } = useViewHook()

const listRef = useTemplateRef('listRef')

const keyword = ref('')

function escapeRegExp(str: string) {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
}

// 计算属性生成高亮后的HTML
function highlightKeywords(text: string) {
  if (!keyword.value.trim())
    return text
  const regex = new RegExp(
    `(${escapeRegExp(keyword.value)})`,
    'gi',
  )

  // 替换匹配项
  return text.replace(regex, '<span style="color: #ff0000">$1</span>')
}

function onSearch() {
  listRef.value?.refreshData()
}

const route = useRoute()

onMounted(() => {
  if (route.query?.value) {
    keyword.value = route.query?.value as string
    nextTick(() => {
      onSearch()
    })
  }
})

function useViewHook() {
  const open = ref(false)

  const editFormRef = useTemplateRef('editFormRef')

  const currentObj = ref(new PublicOpinionEditModel())

  function onView(record: PublicOpinionEditModel) {
    currentObj.value = viewModelToEditModel(record, new PublicOpinionEditModel())
    open.value = true
  }

  return { open, editFormRef, currentObj, onView }
}
</script>

<style scoped lang="less">
.ch2-list-title {
  .dp-text-ellipsis-wrapper {
    font-size: 16px;
    cursor: pointer;
    &:hover {
      color: @colorPrimary;
    }
  }
}
.dp-text-ellipsis-wrapper {
  margin-bottom: 8px;
}
</style>
