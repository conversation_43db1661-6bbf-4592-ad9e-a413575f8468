import { PushLog } from "./PushLog";
import { VerificationStatus } from "./VerificationStatus";
import { EventTeacherStudent } from "./EventTeacherStudent";
import { Department } from "./Department";
import { User } from "./User";
import { PublicOpinion } from "./PublicOpinion";
import { EventReport } from "./EventReport";
import { EventSpreadSituation } from "./EventSpreadSituation";
import { CarbonCopy } from "./CarbonCopy";
export class PublicEvent {
  jytEntityId?: GUID = null;
  pushLog?: PushLog[] | null | undefined = [];
  code?: string | null | undefined = null;
  name?: string | null | undefined = null;
  title?: string | null | undefined = null;
  content?: string | null | undefined = null;
  time?: Dayjs | null | undefined = null;
  level?: string | null | undefined = null;
  category?: string | null | undefined = null;
  subCategory?: string | null | undefined = null;
  contacts?: string | null | undefined = null;
  contactsPhone?: string | null | undefined = null;
  reviewer?: string | null | undefined = null;
  attachment?: Array<GUID> = [];
  isItUrgent?: boolean | null | undefined = null;
  isItReported?: boolean | null | undefined = null;
  reportedTime?: Dayjs | null | undefined = null;
  isTeachersAndStudent?: VerificationStatus | null | undefined = null;
  teachersAndStudentData?: EventTeacherStudent[] | null | undefined = [];
  isItInvolveSchools?: VerificationStatus | null | undefined = null;
  schoolId?: GUID = null;
  /**部门表*/
  school?: Department | null | undefined = null;
  province?: string | null | undefined = null;
  city?: string | null | undefined = null;
  counties?: string | null | undefined = null;
  address1?: string | null | undefined = null;
  isPublicOpinion?: VerificationStatus | null | undefined = null;
  publicOpinionLink?: string | null | undefined = null;
  publicOpinionSituation?: string | null | undefined = null;
  isDeath?: VerificationStatus | null | undefined = null;
  isGatherACrowd?: VerificationStatus | null | undefined = null;
  period?: string | null | undefined = null;
  trackContent?: string | null | undefined = null;
  createdBy?: GUID = null;
  /**用户*/
  createdUser?: User | null | undefined = null;
  createdAt: Dayjs = dayjs();
  modifiedBy?: GUID = null;
  /**用户*/
  modifiedUser?: User | null | undefined = null;
  modifiedAt?: Dayjs | null | undefined = null;
  version: number = 0;
  publicOpinions?: PublicOpinion[] | null | undefined = [];
  reports?: EventReport[] | null | undefined = [];
  spreadSituation?: EventSpreadSituation[] | null | undefined = [];
  carbonCopy?: CarbonCopy[] | null | undefined = [];
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
