import type { CountTip, EventCategoryStatisticsViewModel, EventHandlingRatioViewModel } from '@/api/models'
import * as api from '@/api'
import { StatisticalTimeType } from '@/api/models'
import * as echarts from 'echarts'

import { ref } from 'vue'

type EChartsOption = echarts.EChartsOption

const typeData = [
  {
    value: 60,
    name: '政治安全事件',
  },
  {
    value: 50,
    name: '社会治安事件',
  },
  {
    value: 40,
    name: '公共安全事件',
  },
  {
    value: 30,
    name: '反恐怖工作事件',
  },
  {
    value: 20,
    name: '禁毒工作事件',
  },
  {
    value: 20,
    name: '打击传销事件',
  },
  {
    value: 20,
    name: '电信网络诈骗违法犯罪事件',
  },
]

const colorList = ['#C467FF', '#2CAF70 ', '#FFA23F', '#625AFF', '#4B8BFF', '#59B8B1', '#EEE8A9']

const maxArr = Array.from({ length: typeData.length }).fill(100)

const regionData = [
  {
    name: '南宁市',
    value: 12,
  },
  {
    name: '桂林',
    value: 5,
  },
  {
    name: '玉林',
    value: 4,
  },
  {
    name: '百色',
    value: 2,
  },
  {
    name: '梧州',
    value: 1,
  },
  {
    name: '钦州',
    value: 1,
  },
  {
    name: '贺州',
    value: 3,
  },
  {
    name: '贵港',
    value: 2,
  },
  {
    name: '北海',
    value: 1,
  },
  {
    name: '崇左',
    value: 2,
  },
]

export function useChartData() {
  const majorPublicOpinion = ref(0)

  const potentialSafetyHazard = ref(0)

  const topicData = ref<CountTip[]>([])

  const pieChartOptions = ref<EChartsOption>({
    series: [
      {
        type: 'pie',
        data: [],
      },
    ],
  })

  const deptTypeChartOptions = ref<EChartsOption>({
    series: [
      {
        type: 'pie',
        data: [],
      },
    ],
  })

  const tagChartOptions = ref<EChartsOption>({
    series: [
      {
        type: 'pie',
        data: [],
      },
    ],
  })

  const lineChartOptions = ref<EChartsOption>({
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'line',
      },
    ],
  })

  const doubleLineChartOptions = ref<EChartsOption>({

    tooltip: {
      trigger: 'axis',
    },
    legend: {
      data: [],
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    series: [],
  })

  // 事件类别柱状图
  const typeBarChartOptions = ref<EChartsOption>({
    xAxis: {
      show: false,
      type: 'value',
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLabel: {
          show: true,
          align: 'right',
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
        data: typeData.map(item => item.name),
      },
      {
        type: 'category',
        inverse: true,
        axisTick: 'none',
        show: true,
        axisLabel: {
          color: '#3196fa',
          fontSize: '12',

          formatter: '{value}%',
        },
        data: typeData.map(item => item.value),
      },
    ],
    series: [],
  })

  // 地域统计柱状图
  const regionBarChartOptions = ref<EChartsOption>({
    xAxis: {
      data: regionData.map(item => item.name),
    },
    yAxis: {},
    series: [],
  })

  // 来源统计
  const sourceChartOption = ref<EChartsOption>({
    xAxis: {
      data: regionData.map(item => item.name),
    },
    yAxis: {},
    series: [],
  })

  // 专题统计
  const specialSubjectChartOption = ref<EChartsOption>({
    xAxis: {
      data: regionData.map(item => item.name),
    },
    yAxis: {},
    series: [],
  })

  // 地域统计
  const deptChartOption = ref<EChartsOption>({
    xAxis: {
      data: regionData.map(item => item.name),
    },
    yAxis: {},
    series: [],
  })

  const numberOfPeopleOption = ref<EChartsOption>({
    xAxis: {
      type: 'category',
      data: [],
    },
    yAxis: {
      type: 'value',
    },
    series: [
      {
        data: [],
        type: 'line',
      },
    ],
  })

  const params = ref({ type: StatisticalTimeType.月, count: 12 })

  function getType(val: string) {
    if (val === 'year')
      return { type: StatisticalTimeType.月, count: 12 }
    else if (val === 'month')
      return { type: StatisticalTimeType.天, count: 30 }
    else if (val === 'day')
      return { type: StatisticalTimeType.天, count: 7 }
    else return { type: StatisticalTimeType.月, count: 12 }
  }

  function convertToMatrix(data: Record<string, EventCategoryStatisticsViewModel[]>) {
    const dates = Object.keys(data).sort()
    const categorySet = new Set<string>()

    // 收集所有可能出现的分类
    for (const entries of Object.values(data)) {
      for (const item of entries) {
        categorySet.add(item.category!)
      }
    }

    const categories = Array.from(categorySet)
    const result: any[] = []

    // 每一行是一个分类
    for (const category of categories) {
      const row: (string | number)[] = []
      for (const date of dates) {
        const entry = data[date]?.find(item => item.category === category)
        row.push(entry ? entry.count : 0)
      }
      result.push({
        name: category,
        type: 'line',
        stack: 'Total',
        data: row,
      })
    }

    return [dates, categories, result]
  }

  const colorByIndex = (index: number) => {
    // 示例颜色数组，实际使用时可以从 echarts 主题获取或自定义
    const colors = [
      'rgba(255,100,97',
      'rgba(242,157,10',
      'rgba(139,170,44',
      'rgba(23, 255, 243',
      'rgba(57, 80, 253',
      'rgba(255, 199, 33',
      'rgba(230, 84, 65',
      'rgba(0, 203, 156',
      'rgba(154, 147, 255',
      'rgba(249, 249, 113',
      'rgba(0, 164, 115',
    ]
    return colors[index % colors.length]
  }

  function processDataForEcharts(originalData: CountTip[]) {
    const categories = ['涉及人数', '死亡人数', '受伤人数']
    const seriesData = categories.map(() => [] as number[])

    originalData.forEach((CountTip) => {
      const values = {
        涉及人数: 0,
        死亡人数: 0,
        受伤人数: 0,
      }

      CountTip.child?.forEach((child) => {
        if (categories.includes(child.label!)) {
          values[child.label as keyof typeof values] = child.value
        }
      })

      categories.forEach((cat, index) => {
        seriesData[index]?.push(values[cat as keyof typeof values])
      })
    })

    return categories.map((name, index) => ({
      name,
      type: 'line' as const,
      stack: 'Total',
      smooth: true,
      areaStyle: {
        color: new echarts.graphic.LinearGradient(
          0,
          0,
          0,
          1,
          [
            { offset: 0, color: `${colorByIndex(index)}, 0.5)` }, // 顶部半透明
            { offset: 1, color: `${colorByIndex(index)}, 0)` }, // 底部全透明
          ],
          false,
        ),
      },
      emphasis: { focus: 'series' },
      data: seriesData[index],
    }))
  }

  const loadData = async (eventHandData: EventHandlingRatioViewModel, disposeOfValue: string) => {
    params.value = getType(disposeOfValue)!
    const incidentHandlingData = await api.Statistics.IncidentHandlingTrends_GetAsync(params.value)
    const eventCategoryData = await api.Statistics.EventCategoryStatistics_GetAsync(params.value)
    const [dates, categories, series] = convertToMatrix(eventCategoryData)
    const categoryCountData = await api.Statistics.AllEventCategoryStatistics_GetAsync()

    const incidentHandlingXLabel = incidentHandlingData.map(item => dateTime(item.date, 'YYYY-MM'))

    const incidentHandlingYData = incidentHandlingData.map(item => item.count)

    pieChartOptions.value = {
      series: [
        {
          radius: ['44%', '74%'],
          name: 'Access From',
          type: 'pie',
          selectedMode: 'single',
          silent: true,
          label: {
            position: 'inner',
            fontSize: 14,
          },
          itemStyle: {
            color: '#f7f7f7',
            // 设置扇形的阴影
            shadowBlur: 10,
            shadowColor: '#f3f5fc',
            shadowOffsetX: 0,
            shadowOffsetY: 0,

          },
          labelLine: {
            show: false,
          },
          data: [
            { value: 0, name: '' },
          ],
        },
        {
          type: 'pie',
          radius: ['40%', '70%'],
          padAngle: 3,
          color: [{
            colorStops: [{ offset: 0, color: 'rgba(8,189,143,1)' }, {
              offset: 1,
              color: 'rgba(0,194,181,1)',
            }],
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            type: 'linear',
            global: false,
          }, {
            colorStops: [{ offset: 0, color: 'rgba(83,162,233,1)' }, {
              offset: 1,
              color: 'rgba(87,183,233,1)',
            }],
            x: 0,
            y: 0,
            x2: 1,
            y2: 0,
            type: 'linear',
            global: false,
          }],
          label: {
            alignTo: 'edge',
            formatter: '{name|{b}}\n{value|{c} 起}',
            lineHeight: 15,
            rich: {
              value: {
                fontSize: 14,
                // color: '#000',
              },
            },
          },
          data: [
            { name: '已处置', value: eventHandData.processingCount },
            { name: '待处置', value: eventHandData.allCount - eventHandData.processingCount },
          ],
        },
      ],
    }

    lineChartOptions.value = {
      tooltip: {
        trigger: 'axis',
      },
      grid: {
        left: '2%', // 左侧留白
        right: '0%', // 右侧留白
        top: '8%', // 顶部留白
        bottom: '0%', // 底部留白
        containLabel: true, // 确保坐标轴标签在 grid 区域内
      },
      xAxis: {
        type: 'category',
        data: incidentHandlingXLabel,
      },
      yAxis: {
        type: 'value',
      },
      series: [
        {
          data: incidentHandlingYData,
          type: 'line',
          smooth: true,
        },
      ],
    }

    doubleLineChartOptions.value = {

      tooltip: {
        trigger: 'axis',
      },
      legend: {
        data: categories,
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: dates?.map(item => dateTime(item, 'YYYY-MM')) as any,
      },
      yAxis: {
        type: 'value',
      },
      series,
    }

    typeBarChartOptions.value = {
      grid: {
        left: '16%',
        top: '8%',
        // right: '8%',
        bottom: 0,
      },
      xAxis: {
        show: false,
        type: 'value',
      },
      yAxis: [
        {
          type: 'category',
          inverse: true,
          axisLabel: {
            show: true,
            align: 'right',
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLine: {
            show: false,
          },
          data: categoryCountData.map(item => item.category),
        },
        {
          type: 'category',
          inverse: true,
          axisTick: 'none',
          axisLine: 'none',
          show: true,
          axisLabel: {
            color: '#3196fa',
            fontSize: '12',

            formatter: '{value}%',
          },
          data: categoryCountData.map(item => item.count),
        },
      ],
      series: [
        {
          name: '值',
          type: 'bar',
          zlevel: 1,
          itemStyle: {
            borderRadius: 30,
            color: (params: any) => {
              return colorList[params.dataIndex]
            },
          },
          barWidth: 20,
          data: typeData,
        },
        {
          name: '背景',
          type: 'bar',
          barWidth: 20,
          barGap: '-100%',
          data: maxArr,
          itemStyle: {
            color: '#ededed',
            borderRadius: 30,
          },
        },
      ] as any,
    }
  }

  const dimensionLoad = async (start: Date | undefined, end: Date | undefined) => {
    const resData = await api.Statistics.GetOpinionsDimensionAsync({ start, end })

    const sourceData = resData.find(item => item.label === '来源统计')?.child

    const deptData = resData.find(item => item.label === '所涉及单位数据')?.child

    const adressData = resData.find(item => item.label === '地域发生地统计')?.child

    const specialSubjectData = resData.find(item => item.label === '专题统计')?.child

    topicData.value = specialSubjectData || []

    const tagData = resData.find(item => item.label === '舆情标签')?.child?.map(item => ({ name: item.label, value: item.value }))

    const deptTypeData = resData.find(item => item.label === '单位类别舆情统计')?.child?.map(item => ({ name: item.label, value: item.value }))

    majorPublicOpinion.value = resData.find(item => item.label === '重大舆情')?.value || 0

    potentialSafetyHazard.value = resData.find(item => item.label === '安全隐患')?.value || 0

    const numberOfPeopleRes = resData.find(item => item.label === '人数统计')?.child

    const numberOfPeopleXData = numberOfPeopleRes?.map(item => item.label)

    sourceChartOption.value = {
      grid: {
        left: 0,
        top: '8%',
        right: 0,
        bottom: 20,
        containLabel: false,
      },
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'none',
        },
        formatter: (params: any) => {
          return `${params[0].name}:${params[0].value}`
        },
      },
      xAxis: {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisLine: {
          // y轴
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#ccc',
        },
        data: sourceData?.map(item => item.label),
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLine: {
          // y轴
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      series: [
        {
          itemStyle: { borderRadius: [12, 12, 0, 0] },
          data: sourceData?.map(item => item.value),
          type: 'bar',
          legendHoverLink: false,
          barMaxWidth: 20,
          color: '#5090F1',
        },
      ],
    }

    deptChartOption.value = {
      grid: {
        left: 0,
        top: '8%',
        right: 0,
        bottom: 20,
        containLabel: false,
      },
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'none',
        },
        formatter: (params: any) => {
          return `${params[0].name}:${params[0].value}`
        },
      },
      xAxis: {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisLine: {
          // y轴
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#ccc',
        },
        data: deptData?.map(item => item.label),
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLine: {
          // y轴
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      series: [
        {
          itemStyle: { borderRadius: [12, 12, 0, 0] },
          data: deptData?.map(item => item.value),
          type: 'bar',
          legendHoverLink: false,
          barMaxWidth: 20,
          color: '#C467FF',
        },
      ],
    }

    regionBarChartOptions.value = {
      grid: {
        left: 0,
        top: '8%',
        right: 0,
        bottom: 20,
        containLabel: false,
      },
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'none',
        },
        formatter: (params: any) => {
          return `${params[0].name}:${params[0].value}`
        },
      },
      xAxis: {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisLine: {
          // y轴
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#ccc',
        },
        data: adressData?.map(item => item.label),
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLine: {
          // y轴
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      series: [
        {
          itemStyle: { borderRadius: [12, 12, 0, 0] },
          data: adressData?.map(item => item.value),
          type: 'bar',
          legendHoverLink: false,
          barMaxWidth: 20,
          color: '#0AB389',
        },
      ],
    }

    specialSubjectChartOption.value = {
      grid: {
        left: 0,
        top: '8%',
        right: 0,
        bottom: 20,
        containLabel: false,
      },
      tooltip: {
        show: true,
        trigger: 'axis',
        axisPointer: {
          type: 'none',
        },
        formatter: (params: any) => {
          return `${params[0].name}:${params[0].value}`
        },
      },
      xAxis: {
        type: 'category',
        splitLine: {
          show: false,
        },
        axisLine: {
          // y轴
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          color: '#ccc',
        },
        data: specialSubjectData?.map(item => item.label),
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLine: {
          // y轴
          show: false,
        },
        axisLabel: {
          show: false,
        },
      },
      series: [
        {
          itemStyle: { borderRadius: [12, 12, 0, 0] },
          data: specialSubjectData?.map(item => item.value),
          type: 'bar',
          legendHoverLink: false,
          barMaxWidth: 20,
          color: '#fa8c16',
        },
      ],
    }

    tagChartOptions.value = {
      tooltip: {
        trigger: 'item',
      },
      legend: {
        top: '5%',
        left: 'center',
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            // borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 24,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: tagData,
        },
      ],
    }

    deptTypeChartOptions.value = {
      tooltip: {
        trigger: 'item',
      },
      legend: {
        top: '5%',
        left: 'center',
      },
      series: [
        {
          type: 'pie',
          radius: ['40%', '70%'],
          avoidLabelOverlap: false,
          itemStyle: {
            // borderRadius: 10,
            borderColor: '#fff',
            borderWidth: 2,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 24,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: deptTypeData,
        },
      ],
    }

    numberOfPeopleOption.value = {

      tooltip: {
        trigger: 'item',
      },
      legend: {
        data: processDataForEcharts(numberOfPeopleRes).map(item => item.name),
      },
      grid: {
        left: '1%',
        right: '1%',
        bottom: '2%',
        containLabel: true,
      },
      xAxis: [
        {
          type: 'category',
          boundaryGap: false,
          data: numberOfPeopleXData,
        },
      ],
      yAxis: [
        {
          type: 'value',
        },
      ],
      series: processDataForEcharts(numberOfPeopleRes),
    }
  }

  return { pieChartOptions, lineChartOptions, doubleLineChartOptions, typeBarChartOptions, regionBarChartOptions, sourceChartOption, deptChartOption, tagChartOptions, deptTypeChartOptions, majorPublicOpinion, specialSubjectChartOption, potentialSafetyHazard, numberOfPeopleOption, topicData, loadData, dimensionLoad } as const
}
