<template>
  <c-pro-table
    ref="proTableRef"
    :row-class-name="(_record: any, index: number) => (index % 2 === 1 ? 'c2-table-striped' : 'c2-table-prototype')"
    :row-key="(record) => record.id"
    size="small" :columns="columns"
    :api="api.PublicOpinionSubmissions.GetSubmissionList_PostAsync"
    immediate
    :show-search="false"
    :show-tool-btn="false"
    :post-params="params"
  >
    <template #header>
      <div class="w-full flex justify-between">
        <a-radio-group v-model:value="params.auditStatus" @change="proTableRef?.search()">
          <a-radio-button :value="null">全部</a-radio-button>
          <a-radio-button :value="PublicOpinionSubmissionAuditType.入库">入库</a-radio-button>
          <a-radio-button :value="PublicOpinionSubmissionAuditType.忽略">忽略</a-radio-button>
        </a-radio-group>
        <a-input-search
          v-model:value="params.keyword"
          placeholder="请输入关键词"
          style="width: 280px"
          size="large"
          @search="proTableRef?.search()"
        />
      </div>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'riskLevel'">
        <a-tag :color="record.riskLevel === RiskLevel.高风险 ? 'error' : record.riskLevel === RiskLevel.中风险 ? 'warning' : 'processing'">{{ RiskLevel[record.riskLevel] }}</a-tag>
      </template>
      <template v-if="column.dataIndex === 'auditStatus'">
        <a-tag :color="record.auditStatus === PublicOpinionSubmissionAuditType.入库 ? 'success' : 'warning'">{{ PublicOpinionSubmissionAuditType[record.auditStatus] }}</a-tag>
      </template>
    </template>
  </c-pro-table>
</template>

<script lang='ts' setup>
import * as api from '@/api'

import { PublicOpinionSubmissionAuditType, RiskLevel, SubmissionQueryRequest } from '@/api/models'

definePage({
  meta: {
    title: '审核历史',
    icon: 'HistoryOutlined',
    hidden: true,
  },
})

const proTableRef = useTemplateRef('proTableRef')

const params = ref({ ...new SubmissionQueryRequest(), auditStatus: null, audited: true })

const columns = ref([
  { title: '舆情原文', dataIndex: 'content' },
  { title: '分类', dataIndex: 'mainCategory', width: 120 },
  { title: '风险等级', dataIndex: 'riskLevel', width: 120 },
  { title: '发布时间', dataIndex: 'published', width: 180, dateFormat: 'YYYY-MM-DD HH:mm:ss' },
  { title: '审核状态', dataIndex: 'auditStatus', width: 120, align: 'center' },

])
</script>
