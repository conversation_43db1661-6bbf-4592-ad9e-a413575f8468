<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-06-25 15:31:14
 * @LastEditors: 景 彡
-->
<template>
  <div class="flex flex-wrap gap-4">
    <div v-for="item in tags" :key="item.id!" class="flex items-center border border-text rounded-md border-solid px-2 py-1 text-base">
      <span class="c-text">{{ item.value }}</span>
      <c-icon-close-outlined class="ml-2 size-3 cursor-pointer c-error" @click="onDel(item.id)" />
    </div>
  </div>
</template>

<script lang='ts' setup>
import type { TagManageView } from '@/api/models'
import * as api from '@/api'
import { message } from 'ant-design-vue'

const tags = defineModel<TagManageView[]>('tags')

async function onDel(id: string | GUID) {
  try {
    await api.TagManages.DeleteTag_PostAsync({ id })
    tags.value = tags.value?.filter(item => item.id !== id)
    message.success('删除成功')
  }
  catch (error: any) {
    message.error(`删除失败${error.message}`)
  }
}
</script>

<style scoped>

</style>
