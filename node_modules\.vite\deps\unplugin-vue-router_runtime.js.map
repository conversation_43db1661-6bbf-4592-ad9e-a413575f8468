{"version": 3, "sources": ["../../../../../node_modules/.pnpm/unplugin-vue-router@0.10.9_rollup@4.40.2_vue-router@4.5.1_vue@3.5.10_typescript@5.8.3___vue@3.5.10_typescript@5.8.3_/node_modules/unplugin-vue-router/dist/runtime.js"], "sourcesContent": ["// src/data-loaders/createDataLoader.ts\nvar toLazyValue = (lazy, to, from) => typeof lazy === \"function\" ? lazy(to, from) : lazy;\n\n// src/data-loaders/navigation-guard.ts\nimport { isNavigationFailure } from \"vue-router\";\nimport { effectScope } from \"vue\";\n\n// src/data-loaders/symbols.ts\nvar LOADER_SET_KEY = Symbol(\"loaders\");\nvar LOADER_ENTRIES_KEY = Symbol(\"loaderEntries\");\nvar IS_USE_DATA_LOADER_KEY = Symbol();\nvar PENDING_LOCATION_KEY = Symbol();\nvar STAGED_NO_VALUE = Symbol();\nvar APP_KEY = Symbol();\nvar ABORT_CONTROLLER_KEY = Symbol();\nvar NAVIGATION_RESULTS_KEY = Symbol();\nvar IS_SSR_KEY = Symbol();\n\n// src/data-loaders/utils.ts\nfunction isDataLoader(loader) {\n  return loader && loader[IS_USE_DATA_LOADER_KEY];\n}\nvar currentContext;\nfunction getCurrentContext() {\n  return currentContext || [];\n}\nfunction setCurrentContext(context) {\n  currentContext = context ? context.length ? context : null : null;\n}\nfunction withLoaderContext(promise) {\n  const context = currentContext;\n  return promise.finally(() => currentContext = context);\n}\nvar assign = Object.assign;\nfunction trackRoute(route) {\n  const [params, paramReads] = trackObjectReads(route.params);\n  const [query, queryReads] = trackObjectReads(route.query);\n  let hash = { v: null };\n  return [\n    {\n      ...route,\n      // track the hash\n      get hash() {\n        return hash.v = route.hash;\n      },\n      params,\n      query\n    },\n    paramReads,\n    queryReads,\n    hash\n  ];\n}\nfunction trackObjectReads(obj) {\n  const reads = {};\n  return [\n    new Proxy(obj, {\n      get(target, p, receiver) {\n        const value = Reflect.get(target, p, receiver);\n        reads[p] = value;\n        return value;\n      }\n    }),\n    reads\n  ];\n}\nfunction isSubsetOf(inner, outer) {\n  for (const key in inner) {\n    const innerValue = inner[key];\n    const outerValue = outer[key];\n    if (typeof innerValue === \"string\") {\n      if (innerValue !== outerValue) return false;\n    } else if (!innerValue || !outerValue) {\n      if (innerValue !== outerValue) return false;\n    } else {\n      if (!Array.isArray(outerValue) || outerValue.length !== innerValue.length || innerValue.some((value, i) => value !== outerValue[i]))\n        return false;\n    }\n  }\n  return true;\n}\n\n// src/data-loaders/navigation-guard.ts\nfunction setupLoaderGuard({\n  router,\n  app,\n  effect,\n  isSSR,\n  errors: globalErrors = [],\n  selectNavigationResult = (results) => results[0].value\n}) {\n  if (router[LOADER_ENTRIES_KEY] != null) {\n    if (process.env.NODE_ENV !== \"production\") {\n      console.warn(\n        \"[vue-router]: Data Loader was setup twice. Make sure to setup only once.\"\n      );\n    }\n    return () => {\n    };\n  }\n  if (process.env.NODE_ENV === \"development\" && !isSSR) {\n    console.warn(\n      \"[vue-router]: Data Loader is experimental and subject to breaking changes in the future.\"\n    );\n  }\n  router[LOADER_ENTRIES_KEY] = /* @__PURE__ */ new WeakMap();\n  router[APP_KEY] = app;\n  router[IS_SSR_KEY] = !!isSSR;\n  const removeLoaderGuard = router.beforeEach((to) => {\n    if (router[PENDING_LOCATION_KEY]) {\n      router[PENDING_LOCATION_KEY].meta[ABORT_CONTROLLER_KEY]?.abort();\n    }\n    router[PENDING_LOCATION_KEY] = to;\n    to.meta[LOADER_SET_KEY] = /* @__PURE__ */ new Set();\n    to.meta[ABORT_CONTROLLER_KEY] = new AbortController();\n    to.meta[NAVIGATION_RESULTS_KEY] = [];\n    const lazyLoadingPromises = [];\n    for (const record of to.matched) {\n      if (!record.meta[LOADER_SET_KEY]) {\n        record.meta[LOADER_SET_KEY] = new Set(record.meta.loaders || []);\n        for (const componentName in record.components) {\n          const component = record.components[componentName];\n          const promise = (isAsyncModule(component) ? component() : (\n            // we also support __loaders exported as an option to get around some temporary limitations\n            Promise.resolve(\n              component\n            )\n          )).then((viewModule) => {\n            if (typeof viewModule === \"function\") return;\n            for (const exportName in viewModule) {\n              const exportValue = viewModule[exportName];\n              if (isDataLoader(exportValue)) {\n                record.meta[LOADER_SET_KEY].add(exportValue);\n              }\n            }\n            if (Array.isArray(viewModule.__loaders)) {\n              for (const loader of viewModule.__loaders) {\n                if (isDataLoader(loader)) {\n                  record.meta[LOADER_SET_KEY].add(loader);\n                }\n              }\n            }\n          });\n          lazyLoadingPromises.push(promise);\n        }\n      }\n    }\n    return Promise.all(lazyLoadingPromises).then(() => {\n      for (const record of to.matched) {\n        for (const loader of record.meta[LOADER_SET_KEY]) {\n          to.meta[LOADER_SET_KEY].add(loader);\n        }\n      }\n    });\n  });\n  const removeDataLoaderGuard = router.beforeResolve((to, from) => {\n    const loaders = Array.from(to.meta[LOADER_SET_KEY]);\n    setCurrentContext([]);\n    return Promise.all(\n      loaders.map((loader) => {\n        const { server, lazy, errors } = loader._.options;\n        if (!server && isSSR) {\n          return;\n        }\n        const ret = effect.run(\n          () => app.runWithContext(\n            () => loader._.load(to, router, from)\n          )\n        );\n        return !isSSR && toLazyValue(lazy, to, from) ? void 0 : (\n          // return the non-lazy loader to commit changes after all loaders are done\n          ret.catch((reason) => {\n            if (!errors) throw reason;\n            if (errors === true) {\n              if (Array.isArray(globalErrors) ? globalErrors.some((Err) => reason instanceof Err) : globalErrors(reason))\n                return;\n            } else if (\n              // use local error option if it exists first and then the global one\n              Array.isArray(errors) ? errors.some((Err) => reason instanceof Err) : errors(reason)\n            ) {\n              return;\n            }\n            throw reason;\n          })\n        );\n      })\n    ).then(() => {\n      if (to.meta[NAVIGATION_RESULTS_KEY].length) {\n        return selectNavigationResult(to.meta[NAVIGATION_RESULTS_KEY]);\n      }\n    }).catch(\n      (error) => error instanceof NavigationResult ? error.value : (\n        // let the error propagate to router.onError()\n        // we use never because the rejection means we never resolve a value and using anything else\n        // will not be valid from the navigation guard's perspective\n        Promise.reject(error)\n      )\n    ).finally(() => {\n      setCurrentContext([]);\n    });\n  });\n  const removeAfterEach = router.afterEach((to, from, failure) => {\n    if (failure) {\n      to.meta[ABORT_CONTROLLER_KEY]?.abort(failure);\n      if (\n        // NOTE: using a smaller version to cutoff some bytes\n        isNavigationFailure(\n          failure,\n          16\n          /* NavigationFailureType.duplicated */\n        )\n      ) {\n        for (const loader of to.meta[LOADER_SET_KEY]) {\n          const entry = loader._.getEntry(router);\n          entry.resetPending();\n        }\n      }\n    } else {\n      for (const loader of to.meta[LOADER_SET_KEY]) {\n        const { commit, lazy } = loader._.options;\n        if (commit === \"after-load\") {\n          const entry = loader._.getEntry(router);\n          if (entry && (!toLazyValue(lazy, to, from) || !entry.isLoading.value)) {\n            entry.commit(to);\n          }\n        }\n      }\n    }\n    if (router[PENDING_LOCATION_KEY] === to) {\n      router[PENDING_LOCATION_KEY] = null;\n    }\n  });\n  const removeOnError = router.onError((error, to) => {\n    to.meta[ABORT_CONTROLLER_KEY]?.abort(error);\n    if (router[PENDING_LOCATION_KEY] === to) {\n      router[PENDING_LOCATION_KEY] = null;\n    }\n  });\n  return () => {\n    delete router[LOADER_ENTRIES_KEY];\n    delete router[APP_KEY];\n    removeLoaderGuard();\n    removeDataLoaderGuard();\n    removeAfterEach();\n    removeOnError();\n  };\n}\nfunction isAsyncModule(asyncMod) {\n  return typeof asyncMod === \"function\" && // vue functional components\n  !(\"displayName\" in asyncMod) && !(\"props\" in asyncMod) && !(\"emits\" in asyncMod) && !(\"__vccOpts\" in asyncMod);\n}\nvar NavigationResult = class {\n  constructor(value) {\n    this.value = value;\n  }\n};\nfunction DataLoaderPlugin(app, options) {\n  const effect = effectScope(true);\n  const removeGuards = setupLoaderGuard(assign({ app, effect }, options));\n  const { unmount } = app;\n  app.unmount = () => {\n    effect.stop();\n    removeGuards();\n    unmount.call(app);\n  };\n}\n\n// src/runtime.ts\nvar _definePage = (route) => route;\nvar definePage = (route) => route;\nfunction _mergeRouteRecord(main, ...routeRecords) {\n  return routeRecords.reduce((acc, routeRecord) => {\n    const meta = Object.assign({}, acc.meta, routeRecord.meta);\n    const alias = [].concat(\n      acc.alias || [],\n      routeRecord.alias || []\n    );\n    Object.assign(acc, routeRecord);\n    acc.meta = meta;\n    acc.alias = alias;\n    return acc;\n  }, main);\n}\nexport {\n  ABORT_CONTROLLER_KEY,\n  APP_KEY,\n  DataLoaderPlugin,\n  IS_SSR_KEY,\n  IS_USE_DATA_LOADER_KEY,\n  LOADER_ENTRIES_KEY,\n  LOADER_SET_KEY,\n  NAVIGATION_RESULTS_KEY,\n  NavigationResult,\n  PENDING_LOCATION_KEY,\n  STAGED_NO_VALUE,\n  _definePage,\n  _mergeRouteRecord,\n  assign,\n  currentContext,\n  definePage,\n  getCurrentContext,\n  isSubsetOf,\n  setCurrentContext,\n  toLazyValue,\n  trackRoute,\n  withLoaderContext\n};\n"], "mappings": ";;;;;;;;;;;AACA,IAAI,cAAc,CAAC,MAAM,IAAI,SAAS,OAAO,SAAS,aAAa,KAAK,IAAI,IAAI,IAAI;AAOpF,IAAI,iBAAiB,OAAO,SAAS;AACrC,IAAI,qBAAqB,OAAO,eAAe;AAC/C,IAAI,yBAAyB,OAAO;AACpC,IAAI,uBAAuB,OAAO;AAClC,IAAI,kBAAkB,OAAO;AAC7B,IAAI,UAAU,OAAO;AACrB,IAAI,uBAAuB,OAAO;AAClC,IAAI,yBAAyB,OAAO;AACpC,IAAI,aAAa,OAAO;AAGxB,SAAS,aAAa,QAAQ;AAC5B,SAAO,UAAU,OAAO,sBAAsB;AAChD;AACA,IAAI;AACJ,SAAS,oBAAoB;AAC3B,SAAO,kBAAkB,CAAC;AAC5B;AACA,SAAS,kBAAkB,SAAS;AAClC,mBAAiB,UAAU,QAAQ,SAAS,UAAU,OAAO;AAC/D;AACA,SAAS,kBAAkB,SAAS;AAClC,QAAM,UAAU;AAChB,SAAO,QAAQ,QAAQ,MAAM,iBAAiB,OAAO;AACvD;AACA,IAAI,SAAS,OAAO;AACpB,SAAS,WAAW,OAAO;AACzB,QAAM,CAAC,QAAQ,UAAU,IAAI,iBAAiB,MAAM,MAAM;AAC1D,QAAM,CAAC,OAAO,UAAU,IAAI,iBAAiB,MAAM,KAAK;AACxD,MAAI,OAAO,EAAE,GAAG,KAAK;AACrB,SAAO;AAAA,IACL;AAAA,MACE,GAAG;AAAA;AAAA,MAEH,IAAI,OAAO;AACT,eAAO,KAAK,IAAI,MAAM;AAAA,MACxB;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,KAAK;AAC7B,QAAM,QAAQ,CAAC;AACf,SAAO;AAAA,IACL,IAAI,MAAM,KAAK;AAAA,MACb,IAAI,QAAQ,GAAG,UAAU;AACvB,cAAM,QAAQ,QAAQ,IAAI,QAAQ,GAAG,QAAQ;AAC7C,cAAM,CAAC,IAAI;AACX,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAAA,IACD;AAAA,EACF;AACF;AACA,SAAS,WAAW,OAAO,OAAO;AAChC,aAAW,OAAO,OAAO;AACvB,UAAM,aAAa,MAAM,GAAG;AAC5B,UAAM,aAAa,MAAM,GAAG;AAC5B,QAAI,OAAO,eAAe,UAAU;AAClC,UAAI,eAAe,WAAY,QAAO;AAAA,IACxC,WAAW,CAAC,cAAc,CAAC,YAAY;AACrC,UAAI,eAAe,WAAY,QAAO;AAAA,IACxC,OAAO;AACL,UAAI,CAAC,MAAM,QAAQ,UAAU,KAAK,WAAW,WAAW,WAAW,UAAU,WAAW,KAAK,CAAC,OAAO,MAAM,UAAU,WAAW,CAAC,CAAC;AAChI,eAAO;AAAA,IACX;AAAA,EACF;AACA,SAAO;AACT;AAGA,SAAS,iBAAiB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ,eAAe,CAAC;AAAA,EACxB,yBAAyB,CAAC,YAAY,QAAQ,CAAC,EAAE;AACnD,GAAG;AACD,MAAI,OAAO,kBAAkB,KAAK,MAAM;AACtC,QAAI,MAAuC;AACzC,cAAQ;AAAA,QACN;AAAA,MACF;AAAA,IACF;AACA,WAAO,MAAM;AAAA,IACb;AAAA,EACF;AACA,MAA8C,CAAC,OAAO;AACpD,YAAQ;AAAA,MACN;AAAA,IACF;AAAA,EACF;AACA,SAAO,kBAAkB,IAAoB,oBAAI,QAAQ;AACzD,SAAO,OAAO,IAAI;AAClB,SAAO,UAAU,IAAI,CAAC,CAAC;AACvB,QAAM,oBAAoB,OAAO,WAAW,CAAC,OAAO;AA5GtD;AA6GI,QAAI,OAAO,oBAAoB,GAAG;AAChC,mBAAO,oBAAoB,EAAE,KAAK,oBAAoB,MAAtD,mBAAyD;AAAA,IAC3D;AACA,WAAO,oBAAoB,IAAI;AAC/B,OAAG,KAAK,cAAc,IAAoB,oBAAI,IAAI;AAClD,OAAG,KAAK,oBAAoB,IAAI,IAAI,gBAAgB;AACpD,OAAG,KAAK,sBAAsB,IAAI,CAAC;AACnC,UAAM,sBAAsB,CAAC;AAC7B,eAAW,UAAU,GAAG,SAAS;AAC/B,UAAI,CAAC,OAAO,KAAK,cAAc,GAAG;AAChC,eAAO,KAAK,cAAc,IAAI,IAAI,IAAI,OAAO,KAAK,WAAW,CAAC,CAAC;AAC/D,mBAAW,iBAAiB,OAAO,YAAY;AAC7C,gBAAM,YAAY,OAAO,WAAW,aAAa;AACjD,gBAAM,WAAW,cAAc,SAAS,IAAI,UAAU;AAAA;AAAA,YAEpD,QAAQ;AAAA,cACN;AAAA,YACF;AAAA,aACC,KAAK,CAAC,eAAe;AACtB,gBAAI,OAAO,eAAe,WAAY;AACtC,uBAAW,cAAc,YAAY;AACnC,oBAAM,cAAc,WAAW,UAAU;AACzC,kBAAI,aAAa,WAAW,GAAG;AAC7B,uBAAO,KAAK,cAAc,EAAE,IAAI,WAAW;AAAA,cAC7C;AAAA,YACF;AACA,gBAAI,MAAM,QAAQ,WAAW,SAAS,GAAG;AACvC,yBAAW,UAAU,WAAW,WAAW;AACzC,oBAAI,aAAa,MAAM,GAAG;AACxB,yBAAO,KAAK,cAAc,EAAE,IAAI,MAAM;AAAA,gBACxC;AAAA,cACF;AAAA,YACF;AAAA,UACF,CAAC;AACD,8BAAoB,KAAK,OAAO;AAAA,QAClC;AAAA,MACF;AAAA,IACF;AACA,WAAO,QAAQ,IAAI,mBAAmB,EAAE,KAAK,MAAM;AACjD,iBAAW,UAAU,GAAG,SAAS;AAC/B,mBAAW,UAAU,OAAO,KAAK,cAAc,GAAG;AAChD,aAAG,KAAK,cAAc,EAAE,IAAI,MAAM;AAAA,QACpC;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,QAAM,wBAAwB,OAAO,cAAc,CAAC,IAAI,SAAS;AAC/D,UAAM,UAAU,MAAM,KAAK,GAAG,KAAK,cAAc,CAAC;AAClD,sBAAkB,CAAC,CAAC;AACpB,WAAO,QAAQ;AAAA,MACb,QAAQ,IAAI,CAAC,WAAW;AACtB,cAAM,EAAE,QAAQ,MAAM,OAAO,IAAI,OAAO,EAAE;AAC1C,YAAI,CAAC,UAAU,OAAO;AACpB;AAAA,QACF;AACA,cAAM,MAAM,OAAO;AAAA,UACjB,MAAM,IAAI;AAAA,YACR,MAAM,OAAO,EAAE,KAAK,IAAI,QAAQ,IAAI;AAAA,UACtC;AAAA,QACF;AACA,eAAO,CAAC,SAAS,YAAY,MAAM,IAAI,IAAI,IAAI;AAAA;AAAA,UAE7C,IAAI,MAAM,CAAC,WAAW;AACpB,gBAAI,CAAC,OAAQ,OAAM;AACnB,gBAAI,WAAW,MAAM;AACnB,kBAAI,MAAM,QAAQ,YAAY,IAAI,aAAa,KAAK,CAAC,QAAQ,kBAAkB,GAAG,IAAI,aAAa,MAAM;AACvG;AAAA,YACJ;AAAA;AAAA,cAEE,MAAM,QAAQ,MAAM,IAAI,OAAO,KAAK,CAAC,QAAQ,kBAAkB,GAAG,IAAI,OAAO,MAAM;AAAA,cACnF;AACA;AAAA,YACF;AACA,kBAAM;AAAA,UACR,CAAC;AAAA;AAAA,MAEL,CAAC;AAAA,IACH,EAAE,KAAK,MAAM;AACX,UAAI,GAAG,KAAK,sBAAsB,EAAE,QAAQ;AAC1C,eAAO,uBAAuB,GAAG,KAAK,sBAAsB,CAAC;AAAA,MAC/D;AAAA,IACF,CAAC,EAAE;AAAA,MACD,CAAC,UAAU,iBAAiB,mBAAmB,MAAM;AAAA;AAAA;AAAA;AAAA,QAInD,QAAQ,OAAO,KAAK;AAAA;AAAA,IAExB,EAAE,QAAQ,MAAM;AACd,wBAAkB,CAAC,CAAC;AAAA,IACtB,CAAC;AAAA,EACH,CAAC;AACD,QAAM,kBAAkB,OAAO,UAAU,CAAC,IAAI,MAAM,YAAY;AAzMlE;AA0MI,QAAI,SAAS;AACX,eAAG,KAAK,oBAAoB,MAA5B,mBAA+B,MAAM;AACrC;AAAA;AAAA,QAEE;AAAA,UACE;AAAA,UACA;AAAA;AAAA,QAEF;AAAA,QACA;AACA,mBAAW,UAAU,GAAG,KAAK,cAAc,GAAG;AAC5C,gBAAM,QAAQ,OAAO,EAAE,SAAS,MAAM;AACtC,gBAAM,aAAa;AAAA,QACrB;AAAA,MACF;AAAA,IACF,OAAO;AACL,iBAAW,UAAU,GAAG,KAAK,cAAc,GAAG;AAC5C,cAAM,EAAE,QAAQ,KAAK,IAAI,OAAO,EAAE;AAClC,YAAI,WAAW,cAAc;AAC3B,gBAAM,QAAQ,OAAO,EAAE,SAAS,MAAM;AACtC,cAAI,UAAU,CAAC,YAAY,MAAM,IAAI,IAAI,KAAK,CAAC,MAAM,UAAU,QAAQ;AACrE,kBAAM,OAAO,EAAE;AAAA,UACjB;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,oBAAoB,MAAM,IAAI;AACvC,aAAO,oBAAoB,IAAI;AAAA,IACjC;AAAA,EACF,CAAC;AACD,QAAM,gBAAgB,OAAO,QAAQ,CAAC,OAAO,OAAO;AAxOtD;AAyOI,aAAG,KAAK,oBAAoB,MAA5B,mBAA+B,MAAM;AACrC,QAAI,OAAO,oBAAoB,MAAM,IAAI;AACvC,aAAO,oBAAoB,IAAI;AAAA,IACjC;AAAA,EACF,CAAC;AACD,SAAO,MAAM;AACX,WAAO,OAAO,kBAAkB;AAChC,WAAO,OAAO,OAAO;AACrB,sBAAkB;AAClB,0BAAsB;AACtB,oBAAgB;AAChB,kBAAc;AAAA,EAChB;AACF;AACA,SAAS,cAAc,UAAU;AAC/B,SAAO,OAAO,aAAa;AAAA,EAC3B,EAAE,iBAAiB,aAAa,EAAE,WAAW,aAAa,EAAE,WAAW,aAAa,EAAE,eAAe;AACvG;AACA,IAAI,mBAAmB,MAAM;AAAA,EAC3B,YAAY,OAAO;AACjB,SAAK,QAAQ;AAAA,EACf;AACF;AACA,SAAS,iBAAiB,KAAK,SAAS;AACtC,QAAM,SAAS,YAAY,IAAI;AAC/B,QAAM,eAAe,iBAAiB,OAAO,EAAE,KAAK,OAAO,GAAG,OAAO,CAAC;AACtE,QAAM,EAAE,QAAQ,IAAI;AACpB,MAAI,UAAU,MAAM;AAClB,WAAO,KAAK;AACZ,iBAAa;AACb,YAAQ,KAAK,GAAG;AAAA,EAClB;AACF;AAGA,IAAI,cAAc,CAAC,UAAU;AAC7B,IAAI,aAAa,CAAC,UAAU;AAC5B,SAAS,kBAAkB,SAAS,cAAc;AAChD,SAAO,aAAa,OAAO,CAAC,KAAK,gBAAgB;AAC/C,UAAM,OAAO,OAAO,OAAO,CAAC,GAAG,IAAI,MAAM,YAAY,IAAI;AACzD,UAAM,QAAQ,CAAC,EAAE;AAAA,MACf,IAAI,SAAS,CAAC;AAAA,MACd,YAAY,SAAS,CAAC;AAAA,IACxB;AACA,WAAO,OAAO,KAAK,WAAW;AAC9B,QAAI,OAAO;AACX,QAAI,QAAQ;AACZ,WAAO;AAAA,EACT,GAAG,IAAI;AACT;", "names": []}