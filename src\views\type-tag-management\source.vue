<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-06-26 16:22:53
 * @LastEditors: 景 彡
-->
<template>
  <c-pro-table ref="proTableRef" size="small" :row-key="(record) => record.id" :columns="columns" immediate operation :api="api.WebSources.ListPage_GetAsync">
    <template #header>
      <a-button
        type="primary" ghost :icon="h(PlusOutlined)"
        @click="onAdd(new WebSource())"
      >
        新增
      </a-button>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'iconId'">
        <c-image
          :src="joinFilePathById(record.iconId)" alt="avatar" :preview="true"
          style="height: 40px; width:40px ; object-fit:cover"
        />
      </template>
    </template>
    <template #operation="{ record }">
      <a @click="onAdd(record)">编辑</a>
      <a-popconfirm
        title="确认删除吗?"
        @confirm="onDelete(record.id)"
      >
        <a-button danger type="link">
          删除
        </a-button>
      </a-popconfirm>
    </template>
  </c-pro-table>

  <a-drawer v-model:open="open" destroy-on-close title="新增国内热点/重大舆情" width="860px" @close="open = false">
    <c-pro-form
      v-model:value="form" :fields="fields" :descriptions="{ column: 1, bordered: true }"
      @finish="onSave"
    >
      <template #iconId>
        <div v-if="form.iconId" class="coverBox size-140px overflow-hidden">
          <c-image
            :src="joinFilePathById(form.iconId)" alt="avatar" :preview="true" :del-ico="true"
            style="height: 140px; width:140px ; object-fit:cover" @del-image="() => form.iconId = ''"
          />
        </div>
        <a-button v-else type="dashed" block style="width: 100px; height: 100px" @click="avatarUpload">
          <template #icon>
            <c-icon-plus-outlined />
          </template>
          上传
        </a-button>
      </template>
      <template #footer>
        <a-descriptions-item label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>
</template>

<script lang='ts' setup>
import type { FormField } from 'ch2-components/types/pro-form/types'
import type { ColumnProps } from 'ch2-components/types/pro-table/types'
import * as api from '@/api'
import { FileAttribution, FileType, WebSource } from '@/api/models'
import { PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

definePage({
  meta: {
    title: '舆情来源管理',
    local: true,
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '系统标签管理',
        icon: 'TagsOutlined',
        order: 4,
      },
    },
  },
})

const { open, form, fields, avatarUpload, onAdd, onSave, onDelete } = useAddHook()

const proTableRef = useTemplateRef('proTableRef')

const columns = ref<ColumnProps<WebSource>[]>([
  {
    title: '图标',
    dataIndex: 'iconId',
    key: 'iconId',
    width: '80',
  },
  {
    title: '网站名称',
    dataIndex: 'name',
    key: 'name',
    search: {
      el: 'input',
      method: 'GET',
      attrs: {},
    },
  },
  {
    title: '域名',
    dataIndex: 'domain',
    key: 'domain',
    search: {
      el: 'input',
      method: 'GET',
      attrs: {},
    },
  },
])

function useAddHook() {
  const open = ref(false)
  const form = ref<WebSource>(new WebSource())

  const fields: FormField[] = [
    {
      label: '网站名称',
      prop: 'name',
      el: 'input',
      required: true,
      attrs: { },
    },
    {
      label: '网站域名',
      prop: 'domain',
      el: 'input',
      required: true,
      attrs: { },
    },
    {
      label: '图标',
      prop: 'iconId',
      el: 'input',
      attrs: { },
    },
  ]

  function avatarUpload() {
    useFileMangerModal((files) => {
      form.value.iconId = files[0]?.id
    }, { multiple: false, immediateReturn: true, menu: [FileType.图片], fileAttribution: FileAttribution.管理认证 })
  }

  function onAdd(record: WebSource) {
    form.value = record
    open.value = true
  }

  async function onDelete(id: string) {
    await api.WebSources.Delete_PostAsync({ id })
    proTableRef.value?.search()
    message.success('删除成功')
  }

  async function onSave() {
    if (form.value.domain) {
      try {
        const url = new URL(form.value.domain.startsWith('http') ? form.value.domain : `http://${form.value.domain}`)
        form.value.domain = url.host
      }
      catch (error: any) {
        message.error(`请输入正确的url${error.message}`)
        return
      }
    }

    await api.WebSources.Save_PostAsync(form.value)
    message.success('保存成功')
    open.value = false
    proTableRef.value?.search()
  }

  return { open, form, fields, avatarUpload, onAdd, onSave, onDelete }
}
</script>

<style scoped lang="less">
:deep(.c2-table-striped) td {
  background-color: @colorPrimaryBg;
}

:deep(.ant-table-thead tr th) {
  background: @colorPrimaryBgHover !important;
}
</style>
