import { DeptPushChannel } from "./DeptPushChannel";
import { PushStatus } from "./PushStatus";
import { DeptPushLog } from "./DeptPushLog";
/**发送渠道表*/
export class DeptPushChannelLog {
  createdTime: Dayjs = dayjs();
  /**推送渠道*/
  channel: DeptPushChannel = 0;
  status: PushStatus = 0;
  /**响应信息（错误信息）*/
  responseMessage?: string | null | undefined = null;
  deptPushLogId: GUID = "00000000-0000-0000-0000-000000000000";
  /**合作单位推送记录*/
  deptPushLog?: DeptPushLog | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
