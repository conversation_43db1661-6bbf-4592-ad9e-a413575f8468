<template>
  <a-card :bordered="false" style="width: 100%">
    <template #title>
      <div class="flex items-center">
        分类标签选项管理
        <a-alert message="点击新增后请在右边填写标签信息" type="info" show-icon style="padding: 2px 6px; margin-left: 8px">
          <template #icon><c-icon-smile-outlined /></template>
        </a-alert>
      </div>
    </template>
    <div class="grid grid-cols-[2fr_1fr] gap-4">
      <div>
        <a-card style="width: 100%">
          <div class="mb-4 text-base font-bold">舆情分类</div>
          <div class="flex items-center">
            <span>一级分类：</span>
            <c-select
              v-model:value="firstId"
              selected-first
              style="width: 280px"
              :options="firstOptions"
              :params="firstTag"
              @change="firstChange"
            />
            <a-popover title="提示">
              <template #content>
                <p>点击可删除当前选中分类标签（如存在子标签则禁止删除）</p>
              </template>
              <a-button class="ml-2" :disabled="!currentlevel1.value" type="link" :icon="h(CloseOutlined)" @click="onDel" />
            </a-popover>

            <a-button class="ml-2" type="primary" :icon="h(PlusOutlined)" @click="onAdd(TagType.一级分类)">新增标签</a-button>
          </div>
          <div class="mt-2 flex items-center">
            <span>二级分类：</span>
            <c-select
              v-model:value="form.parentId"
              selected-first
              :field-names="{ label: 'value', value: 'id' }"
              style="width: 280px"
              :options="typeTree.children!"
              :loading="getTypeTreeSpinner"
              :params="{ firstTag: currentlevel1, tagType: TagType.二级分类 }"
              @change="selectChange"
            />
            <a-popover title="提示">
              <template #content>
                <p>点击可删除当前选中分类标签（如存在子标签则禁止删除）</p>
              </template>
              <a-button class="ml-2" :disabled="!currentlevel2.value" type="link" :icon="h(CloseOutlined)" @click="onDel" />
            </a-popover>
            <a-button class="ml-2" type="primary" :icon="h(PlusOutlined)" @click="onAdd(TagType.二级分类)">新增标签</a-button>
          </div>
        </a-card>
        <a-divider />
        <a-card style="width: 100%">
          <div class="mb-4 text-base font-bold">事故原因</div>
          <div class="flex items-center">
            <C2Tag v-model:tags="reasonTags" />
            <a-button class="ml-4" type="primary" :icon="h(PlusOutlined)" @click="onAdd(TagType.事故原因)" />
          </div>
        </a-card>
        <a-card style="width: 100%; margin-top: 16px">
          <div class="mb-4 text-base font-bold">事故类型（教育厅）</div>
          <div class="flex items-center">
            <C2Tag v-model:tags="typeTags" />
            <a-button class="ml-4" type="primary" :icon="h(PlusOutlined)" @click="onAdd(TagType.事故类型)" />
          </div>
        </a-card>
        <a-card style="width: 100%; margin-top: 16px">
          <div class="mb-4 text-base font-bold">事发地点</div>
          <div class="flex items-center">
            <C2Tag v-model:tags="locationTags" class="h-100%" />
            <a-button class="ml-2" type="primary" :icon="h(PlusOutlined)" @click="onAdd(TagType.事发地点)" />
          </div>
        </a-card>
        <a-card style="width: 100%; margin-top: 16px">
          <a-checkbox :checked="currentTag.isShowIsHiddenDanger!" @change="checkChange($event, 'isShowIsHiddenDanger')"><span class="text-base font-bold">是否显示安全隐患</span></a-checkbox>
          <div v-if="currentTag.isShowIsHiddenDanger" class="mt-4 flex items-center">
            <C2Tag v-model:tags="dangerTags" />
            <a-button class="ml-4" type="primary" :icon="h(PlusOutlined)" @click="onAdd(TagType.隐患类别)" />
          </div>
        </a-card>
        <a-card style="width: 100%; margin-top: 16px">
          <a-checkbox :checked="currentTag.isShowNumberPeopleInvolved!" @change="checkChange($event, 'isShowNumberPeopleInvolved')"><span class="text-base font-bold">是否显示涉及人数</span></a-checkbox>
        </a-card>
        <a-card style="width: 100%; margin-top: 16px">
          <a-checkbox :checked="currentTag.isShowTheInjured!" @change="checkChange($event, 'isShowTheInjured')"><span class="text-base font-bold">是否显示受伤人数</span></a-checkbox>
        </a-card>
        <a-card style="width: 100%; margin-top: 16px">
          <a-checkbox :checked="currentTag.isShowNumberOfDeaths!" @change="checkChange($event, 'isShowNumberOfDeaths')"><span class="text-base font-bold">是否显示死亡人数</span></a-checkbox>
        </a-card>
      </div>
      <a-card :class="{ 'form-highlight': highlightForm }" style="width: 100%; min-width: 420px">
        <div class="mb-4 text-base font-bold">
          新增【 <span class="c-primary" :class="{ 'font-highlight': highlightFont }">{{ form.tagType ? TagType[form.tagType] : '一级分类' }}</span> 】标签
        </div>

        <c-pro-form
          v-model:value="form" :fields="fields" :descriptions="{ column: 1, bordered: true }"
          @finish="onSave"
        >
          <template #footer>
            <a-descriptions-item label="操作">
              <a-button type="primary" html-type="submit">
                保存
              </a-button>
            </a-descriptions-item>
          </template>
        </c-pro-form>
      </a-card>
    </div>
  </a-card>
</template>

<script lang='ts' setup>
import type { SelectProps } from 'ant-design-vue'
import type { FormField } from 'ch2-components/types/pro-form/types'
import * as api from '@/api'
import { TagManage, TagManageView, TagType } from '@/api/models'
import { CloseOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { nextTick } from 'vue'

// 定义props
const props = defineProps<{
  firstValue?: string
  level2Value?: string
  type?: TagType
}>()

// 定义事件
const emit = defineEmits<{
  tagAdded: []
}>()

const firstId = ref<string | undefined | null>()

const firstOptions = ref<SelectProps['options']>([])

const reasonTags = ref<TagManageView[]>([]) // 事故原因

const typeTags = ref<TagManageView[]>([]) // 事故类型

const locationTags = ref<TagManageView[]>([]) // 事发地点

const dangerTags = ref<TagManageView[]>([]) // 隐患类别

const { form, fields, onSave, onAdd } = useFormHook()

const firstTag = { tagType: TagType.一级分类 }

const typeTree = ref<TagManageView>(new TagManageView())

const currentlevel1 = ref({ label: '', value: '' })

const currentlevel2 = ref({ label: '', value: '' })

const { run: getTypeTree, spinner: getTypeTreeSpinner } = useLoading(() => api.TagManages.GetTagTreeAsync({ firstTag: currentlevel1.value.label }).then((res) => {
  typeTree.value = res?.[0] ?? new TagManageView()
}))

function findChildrenByTagType(
  data: TagManageView[],
  targetId: string,
  tagType: TagType,
): TagManageView[] {
  // 递归查找指定 id 的节点
  function findNodeById(nodes: TagManageView[], id: string): TagManageView | null {
    for (const node of nodes) {
      if (node.id === id)
        return node
      const found = findNodeById(node.children || [], id)
      if (found)
        return found
    }
    return null
  }

  const targetNode = findNodeById(data, targetId)
  if (!targetNode)
    return []

  return targetNode.children?.filter(child => child.tagType === tagType) || []
}

async function selectChange(_: string, record: { label: string, value: string }) {
  currentlevel2.value = record
  await getDataById()
  convertData()
}

function convertData() {
  const id = currentlevel2.value.value
  reasonTags.value = findChildrenByTagType(typeTree.value.children || [], id, TagType.事故原因)
  typeTags.value = findChildrenByTagType(typeTree.value.children || [], id, TagType.事故类型)
  locationTags.value = findChildrenByTagType(typeTree.value.children || [], id, TagType.事发地点)
  dangerTags.value = findChildrenByTagType(typeTree.value.children || [], id, TagType.隐患类别)
}

function firstChange(_: string, record: { label: string, value: string }) {
  currentlevel1.value = record
  getTypeTree()
}

const currentTag = ref<TagManage>(new TagManage())

async function checkChange(val: any, lable: string) {
  const checked = val.target.checked
  if (!form.value.parentId)
    return message.warning('请选择一级和二级分类后再操作')
  currentTag.value[lable as keyof TagManage] = checked
  editSave()
}

async function getDataById() {
  currentTag.value = await api.TagManages.FindById_GetAsync({ id: form.value.parentId })
}

/** 只使用涉及人数、受伤人数、死亡人数 */
async function editSave() {
  await api.TagManages.Save_PostAsync(currentTag.value)
  message.success('保存成功')
}

async function onDel() {
  const { tagType } = form.value

  // 获取删除 ID（根据分类级别）
  const id = tagType === TagType.一级分类
    ? currentlevel1.value?.value
    : currentlevel2.value?.value

  if (!id) {
    message.warning('请选择要删除的分类')
    return
  }

  // 删除前端缓存数据
  if (tagType === TagType.一级分类) {
    firstOptions.value = firstOptions.value?.filter(item => item.value !== id)
    currentlevel1.value = { label: '', value: '' }
    firstId.value = ''
  }
  else if (tagType === TagType.二级分类) {
    if (Array.isArray(typeTree.value.children)) {
      typeTree.value.children = typeTree.value.children.filter(item => item.id !== id)
    }
    form.value.parentId = ''
  }

  // 调用接口
  await api.TagManages.DeleteTag_PostAsync({ id })
  message.success('删除成功')
}

async function getFirstData() {
  const res = await api.TagManages.GetTagTreeAsync({}) || []
  firstOptions.value = res.map(item => ({ label: item.value, value: item.id }))
  if (props.firstValue && props.level2Value && props.type != null) {
    const first = res.find(v => v.value === props.firstValue)
    if (first) {
      currentlevel1.value = { value: first.id!, label: first.value! }
      firstId.value = first?.id as any
      const level2 = first?.children?.find(v => v.value === props.level2Value)
      if (level2) {
        form.value.parentId = level2?.id
        currentlevel2.value = { value: level2.id!, label: level2.value! }
        typeTree.value = first!
        console.log(form.value.parentId, typeTree.value)
        convertData()
        onAdd(props.type)
      }
    }
  }
}

onMounted(() => {
  getFirstData()
})

const highlightForm = ref(false)
const highlightFont = ref(false)

function triggerFormHighlight() {
  highlightForm.value = false
  highlightFont.value = false
  nextTick(() => {
    highlightForm.value = true
    highlightFont.value = true
    setTimeout(() => {
      highlightForm.value = false
      highlightFont.value = false
    }, 1000)
  })
}

function useFormHook() {
  const form = ref({ ...new TagManage(), tagType: TagType.一级分类 })

  const fields: FormField[] = [
    {
      label: '名称',
      prop: 'value',
      el: 'input',
      required: true,
      attrs: { },
    },
    {
      label: '描述',
      prop: 'description',
      el: 'textarea',
      attrs: { rows: 4, showCount: true, maxlength: 100 },
    },
  ]

  function onAdd(type: TagType) {
    message.info('请在右边表单填写标签信息！')
    form.value.tagType = type
    triggerFormHighlight()
  }

  async function onSave() {
    if (form.value.tagType === TagType.二级分类) {
      if (!firstId.value)
        return message.warning('请选择一级分类后再保存')
      form.value.parentId = firstId.value
    }
    if ([TagType.事发地点, TagType.事故原因, TagType.事故类型].includes(form.value.tagType) && !form.value.parentId) {
      return message.warning('请选择二级分类后再保存')
    }

    try {
      await api.TagManages.AddTag_PostAsync(form.value)

      if (form.value.tagType === TagType.一级分类)
        getFirstData()
      message.success('保存成功')
      form.value.value = ''
      form.value.description = ''
      await getTypeTree()
      convertData()
      eventBus.emit('update-opinion-type')
      // 触发标签添加完成事件
      emit('tagAdded')
    }
    catch (error: any) {
      message.error(`保存失败${error.message}`)
    }
  }
  return { form, fields, onSave, onAdd }
}
</script>

<style scoped>
@keyframes highlight-border {
  0% {
    box-shadow: 0 0 0 0 #409eff;
    border-color: #409eff;
  }
  50% {
    box-shadow: 0 0 16px 4px #409eff88;
    border-color: #409eff;
  }
  100% {
    box-shadow: 0 0 0 0 #409eff;
    border-color: #d9d9d9;
  }
}

.form-highlight {
  animation: highlight-border 1s;
  border: 2px solid #409eff !important;
}
@keyframes font-highlight {
  0% {
    color: #409eff;
    text-shadow: 0 0 0 #409eff;
  }
  50% {
    color: #409eff;
    text-shadow: 0 0 8px #409eff88;
  }
  100% {
    color: inherit;
    text-shadow: none;
  }
}
.font-highlight {
  animation: font-highlight 1s;
}
</style>
