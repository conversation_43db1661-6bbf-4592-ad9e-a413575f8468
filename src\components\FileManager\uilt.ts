/*
 * @Description:
 * @Author: 景 彡
 * @Date: 2024-10-30 10:35:28
 * @LastEditors: 景 彡
 */
import type { UploadFileInfo, UploadFileInfoResult } from '@/api/models'
import type { CancelToken } from 'axios'
import type { Ref } from 'vue'
import { FileAttribution } from '@/api/models'
import { notification } from 'ant-design-vue'
import axios from 'axios'

/**
 * 根据带宽和传入的文件大小来实时模拟上传进度，并且给progressRes.value赋值
 */
export function uploadFile(file: File | File[], percentCompleted?: Ref<number>, cancelToken?: CancelToken, fileAttribution?: FileAttribution) {
  return new Promise<{ success: UploadFileInfoResult[], error: UploadFileInfoResult[] }>((resolve, reject) => {
    const formData = new FormData()
    const files = Array.isArray(file) ? file : [file]

    files.forEach((item) => {
      formData.append('file', item)
    })

    const { token } = useUserStore()

    axios.post(fileAttribution === FileAttribution.管理认证 ? '/api/FileManage/UploadCertifiedFile' : '/api/FileManage/UploadPortalFile', formData, {
      cancelToken,
      onUploadProgress: (progressEvent) => {
        if (percentCompleted)
          percentCompleted.value = Math.round((progressEvent.loaded * 100) / progressEvent.total!)
      },
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
      .then((response) => {
        const success = response.data.data.filter((v: { success: any }) => v.success) as UploadFileInfoResult[]
        const error = response.data.data.filter((v: { success: any }) => !v.success) as UploadFileInfoResult[]
        if (success.length) {
          if (error.length > 0) {
            notification.error({
              message: '部分文件上传失败',
              description:
                `${error.length}个文件上传失败;\n${error.map(v => `${v.originalName}  ${v.messge}`).join(';\n')}`,
              duration: null,
            })
          }
          resolve({ success, error })
          console.log('文件上传成功!')
        }
        else {
          reject(new Error(`${error.length}个文件上传失败;\n${error.map(v => `${v.originalName}  ${v.messge}`).join(';\n')}`))
        }
      })
      .catch((error) => {
        reject(error)
      })
  })
}

export function joinFilePath(file: UploadFileInfo | null | undefined, _type: 'manage' | 'p' = 'manage') {
  if (!file)
    return ''

  return joinFilePathById(file.id)
}
