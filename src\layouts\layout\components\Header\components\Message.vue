<!--
 * @Description:
 * @Author: luckymiaow
 * @Date: 2023-05-15 14:50:21
 * @LastEditors: 景 彡
-->
<!-- 通知消息 -->
<template>
  <a-badge>
    <BellOutlined class="text-1.6em c-#fff" @click="open = true" />
  </a-badge>

  <a-drawer
    v-model:open="open"
    title="系统消息通知"
    placement="right"
    width="800px"
  >
    <a-radio-group v-model:value="readingStatus" button-style="solid" class="c2-radio-group" @change="onSearch">
      <a-radio-button :value="ReadingStatus.未读">未读</a-radio-button>
      <a-radio-button :value="ReadingStatus.已读">已读</a-radio-button>
    </a-radio-group>
    <div class="mt-4 space-y-4">
      <div v-for="item in newsList" :key="item.id?.toString()" class="rounded-md bg-primary-bg p-4">
        <div class="flex justify-between">
          <div>
            <span class="text-base font-bold">{{ item.abstract }}</span>
            <span v-if="item.readingStatus === ReadingStatus.未读" class="inline-block rounded-full bg-error px-2.4 py-0.4 text-3.5 c-#fff"><i>新</i></span>
          </div>
          <div class="min-w-160px text-right text-base c-text-secondary">{{ dateTime(item.createdTime) }}</div>
        </div>
        <div class="mt-4 text-base c-text-secondary" v-html="item.text" />
        <div v-if="item.readingStatus === ReadingStatus.未读" class="mt-4 text-right">
          <a-button type="primary" size="small" :loading="markLoading" @click="onMark(item.id)">标记已读</a-button>
        </div>
      </div>
    </div>
    <div v-if="newsList.length > 0" class="mt-4 text-right">
      <a-pagination
        v-model:current="pageData.current"
        v-model:page-size="pageData.pageSize"
        :page-size-options="pageData.pageSizeOptions"
        :total="pageData.total"
        show-size-changer
        :show-total="total => `共 ${total} 条`"
        @change="onPageChange"
      />
    </div>
  </a-drawer>
</template>

<script setup lang="ts">
import type { DeptPushLog } from '@/api/models'
import * as api from '@/api'
import { ReadingStatus } from '@/api/models'
import { BellOutlined } from '@ant-design/icons-vue'
import { message, notification } from 'ant-design-vue'

const open = ref(false)

const readingStatus = ref(ReadingStatus.未读)

const newsList = ref<DeptPushLog[]>([])

const pageData = ref({
  current: 1,
  total: 0,
  pageSizeOptions: ['10', '20', '30', '40', '50'],
  pageSize: 10,
})

function onPageChange(page: number, pageSize: number) {
  pageData.value.current = page
  pageData.value.pageSize = pageSize
  getData()
}

function onSearch() {
  pageData.value.current = 1
  getData()
}

async function getData() {
  // 兼容 offset/limit
  const offset = (pageData.value.current - 1) * pageData.value.pageSize
  const res = await api.DeptPushLogs.GetInternalPushNotificationsAsync({
    readingStatus: readingStatus.value,
    offset,
    limit: pageData.value.pageSize,
  })

  newsList.value = res.items || []
  pageData.value.total = res.totals || 0
}

const markLoading = ref(false)

async function onMark(id: GUID) {
  markLoading.value = true

  try {
    await api.DeptPushLogs.SetInternalPushNotificationReadingStatus_PostAsync({ deptPushLogId: id })
    message.success('标记成功')
    getData()
    markLoading.value = false
  }
  catch (error: any) {
    console.log(error)

    markLoading.value = false
  }
}

onMounted(async () => {
  await getData()
  const data = newsList.value.filter(item => item.readingStatus === ReadingStatus.未读)
  data.forEach((v) => {
    notification.info({
      message: v.abstract,
      description: v.text,
      placement: 'bottomRight',
    })
  })
})
</script>

<style scoped>
.c2-radio-group {
  width: 100%;
  display: flex;
  label {
    flex: 1;
    text-align: center;
  }
}
</style>
