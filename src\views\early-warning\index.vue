<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-06-26 16:35:47
 * @LastEditors: 景 彡
-->
<template>
  <a-spin :spinning="spinning">
    <div ref="scrollContainer" class="ch2-scroll-container scrollbar-thumb-gray-400 flex gap-16px overflow-auto bg-bg-container p-16px">
      <div
        v-for="item in listData" :key="item.id" class="group relative box-border h-200px min-w-300px cursor-pointer border-(3px border solid) text-center text-base"
        :class="{ ' border-primary c-primary': item.id === currentHot.id }" @click="topicClick(item)"
      >
        <div class="size-full flex flex-col items-center justify-center text-18px font-bold">
          <div>{{ dateTime(item.alertTime, 'YYYY-MM') }}</div>
          <div class="mt-2">{{ item.title }}</div>
        </div>
        <div class="absolute bottom-4 right-2 hidden group-hover:block">
          <c-icon-edit-outlined class="ml-16px text-6 c-primary font-bold" @click="onEdit(item)" />
          <c-icon-delete-outlined class="ml-16px text-6 c-error font-bold" @click="onDel(item)" />
        </div>
      </div>
    </div>

    <div class="mt-4">
      <a-card :bordered="false" style="width: 100%">
        <a-tabs v-model:active-key="activeKey" @change="tabClick">
          <template #leftExtra>
            <a-button v-if="$auth([_Role.舆情监测中心领导, _Role.舆情监测人员])" type="primary" class="mr-4" @click="onHotAdd">新增预警</a-button>
          </template>
          <a-tab-pane key="1">
            <template #tab>
              {{ currentHot.title }} 舆情预警
            </template>
            <div ref="pdfWrapperRef" class="bg-info-bg p-4" />
          </a-tab-pane>
          <a-tab-pane key="2" tab="国内教育系统 · 热点/重大舆情">
            <c-pro-table
              ref="proTableRef" size="small"
              :row-key="(record) => record.id"
              :columns="columns"
              :api="api.HotPublicOpinionAlerts.HotPublicOpinions_GetAsync"
              operation :show-search="false"
              :show-tool-btn="false"
              :row-selection="rowSelection"
              :get-params="{ hotPublicOpinionAlertId: currentHot.id, ...params }"
            >
              <template #header>
                <div class="w-full flex justify-between">
                  <div class="flex items-center">
                    <div>
                      <span class="inline-block min-w-70px">发生时间：</span>
                      <c-range-picker
                        v-model:start-time="params.startTime" v-model:end-time="params.endTime" :show-time="{ format: 'HH:mm' }"
                        format="YYYY-MM-DD HH:mm" style="width: 320px;" @change="proTableRef?.search"
                      />
                    </div>
                    <a-button type="primary" :icon="h(PlusOutlined)" class="ml-4" @click="onAdd">
                      录入
                    </a-button>
                  </div>
                  <a-button type="primary">
                    加入AI分析
                  </a-button>
                </div>
              </template>
              <template #operation="{ record }">
                <a-popconfirm
                  title="确认删除此舆情吗?"
                  ok-text="确认"
                  cancel-text="取消"
                  @confirm="onDelOption(record.id)"
                >
                  <span class="cursor-pointer c-error">删除</span>
                </a-popconfirm>
              </template>
            </c-pro-table>
          </a-tab-pane>
        </a-tabs>
      </a-card>
    </div>

    <a-drawer v-model:open="open" destroy-on-close title="新增国内热点/重大舆情" width="860px" @close="open = false">
      <c-pro-form
        v-model:value="form" :fields="fields" :descriptions="{ column: 1, bordered: true }"
        @finish="onSave"
      >
        <template #footer>
          <a-descriptions-item label="操作">
            <a-button type="primary" html-type="submit">
              保存
            </a-button>
          </a-descriptions-item>
        </template>
      </c-pro-form>
    </a-drawer>

    <a-drawer v-model:open="hotOpen" destroy-on-close title="添加/编辑镜鉴预警" width="860px" @close="hotOpen = false">
      <c-pro-form v-model:value="hotForm" :fields="hotFields" :descriptions="{ column: 1, bordered: true }" @finish="onHotSave">
        <template #footer>
          <a-descriptions-item label="操作">
            <a-button type="primary" html-type="submit">
              保存
            </a-button>
          </a-descriptions-item>
        </template>
      </c-pro-form>
    </a-drawer>
  </a-spin>
</template>

<script lang='ts' setup>
import type { FormField } from 'ch2-components/types/pro-form/types'
import * as api from '@/api'
import { FileAttribution, FileType, HotPublicOpinion, HotPublicOpinionAlert, TagType } from '@/api/models'
import Upload from '@/components/FileManager/Upload.vue'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import { ExclamationCircleOutlined, PlusOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import modal from 'ant-design-vue/es/modal'
import * as pdfjsLib from 'pdfjs-dist'
import PdfWorker from 'pdfjs-dist/build/pdf.worker?worker'

definePage({
  meta: {
    title: '镜鉴预警',
    local: true,
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '镜鉴预警',
        icon: 'AlertOutlined',
        order: 4,
      },
    },
  },
})

pdfjsLib.GlobalWorkerOptions.workerPort = new PdfWorker()

const { currentHot, listData, pdfWrapperRef, getList, onDel, onEdit, topicClick } = useHotHook()

const { hotOpen, hotForm, hotFields, onHotAdd, onHotSave } = useAddHotHook()

const { open, form, fields, rowSelection, onAdd, onSave, onDelOption } = useOpinionHook()

const activeKey = ref('1')

const spinning = ref(false)

const params = ref({
  startTime: null,
  endTime: null,

})

const proTableRef = useTemplateRef('proTableRef')

const columns = ref([
  {
    title: '热点/重大舆情事件',
    dataIndex: 'summary',
  },
  { title: '微博最高热度榜', dataIndex: 'weiboHotSearch' },
  { title: '舆情分类', dataIndex: 'category' },
  { title: '发生时间', dataIndex: 'occurTime', dateFormat: 'YYYY-MM-DD' },
])

const scrollContainer = useTemplateRef('scrollContainer')

function tabClick(e: string | number) {
  nextTick(() => {
    if (e === '2' || e === 2)
      proTableRef.value?.search()
  })
}

onMounted(() => {
  getList()
  if (scrollContainer.value) {
    scrollContainer.value.addEventListener('wheel', (e) => {
      // 如果有垂直滚动，转换为横向滚动
      if (e.deltaY !== 0) {
        e.preventDefault()
        scrollContainer.value!.scrollLeft += e.deltaY
      }
    }, { passive: false }) // passive 必须为 false 才能阻止默认滚动行为
  }
})

function useHotHook() {
  const listData = ref<HotPublicOpinionAlert[]>([])

  const currentHot = ref(new HotPublicOpinionAlert())

  const pdfWrapperRef = useTemplateRef('pdfWrapperRef')

  function onDel(record: HotPublicOpinionAlert) {
    modal.confirm({
      title: `确定删除【${record.title}】专题吗`,
      icon: h(ExclamationCircleOutlined),
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      async onOk() {
        try {
          await api.HotPublicOpinionAlerts.Delete_PostAsync({ id: record.id! })
          getList()
          message.success('删除成功')
        }
        catch (error: any) {
          message.error(`删除失败：${error.message}`)
        }
      },
    })
  }

  function onEdit(record: HotPublicOpinionAlert) {
    hotForm.value = deepCopy(record)
    hotForm.value.hotPublicOpinions = []
    hotOpen.value = true
  }

  async function renderPdf(url: string) {
    if (!pdfWrapperRef.value)
      return
    pdfWrapperRef.value.innerHTML = '' // 清空旧的内容

    const loadingTask = pdfjsLib.getDocument(url)
    const pdf = await loadingTask.promise

    for (let pageNum = 1; pageNum <= pdf.numPages; pageNum++) {
      const page = await pdf.getPage(pageNum)

      const viewport = page.getViewport({ scale: 1.5 })
      const canvas = document.createElement('canvas')
      const context = canvas.getContext('2d')!
      canvas.height = viewport.height
      canvas.width = viewport.width

      await page.render({ canvasContext: context, viewport }).promise
      pdfWrapperRef.value.appendChild(canvas)
    }
  }

  function topicClick(item: any) {
    console.log('item', item)
    if (currentHot.value.id === item.id)
      return
    currentHot.value = deepCopy(item)
    const url = joinFilePathById(currentHot.value.attachmentId!)
    renderPdf(url)
    proTableRef.value?.search()
  }

  async function getList() {
    spinning.value = true
    listData.value = await api.HotPublicOpinionAlerts.HotPublicOpinionAlerts_GetAsync({})
    currentHot.value = listData.value[0] || new HotPublicOpinionAlert()
    const url = joinFilePathById(currentHot.value.attachmentId!)
    renderPdf(url)
    if (currentHot.value.id)
      proTableRef.value?.search()
    spinning.value = false
  }

  return { currentHot, listData, pdfWrapperRef, getList, onDel, onEdit, topicClick }
}

function useAddHotHook() {
  const hotOpen = ref(false)

  const hotForm = ref(new HotPublicOpinionAlert())

  const hotFields = computed(() => [
    {
      label: '标题',
      prop: 'title',
      el: 'input',
      formItem: {
        rules: [{ required: true, message: '标题必填!' }],
      },
      attrs: {},
    },
    {
      label: '时间',
      prop: 'alertTime',
      el: 'date-picker',
      formItem: {
        rules: [{ required: true, message: '时间必填!' }],
      },
      attrs: {
        style: { width: '100%' },
      },
    },
    {
      label: '附件',
      prop: 'attachmentId',
      el: () => h(Upload, { 'fileList': [hotForm.value.attachment!].filter(Boolean), 'value': hotForm.value.attachmentId, 'onUpdate:fileList': (v) => {
        hotForm.value.attachment = v[0]!
      }, 'onUpdate:value': (v) => {
        hotForm.value.attachmentId = v
      }, 'config': { accept: '.pdf', multiple: false, menu: [FileType.文档], immediateReturn: true, fileAttribution: FileAttribution.管理认证 } }),
      descriptionsItem: { span: 2 },
      attrs: {},
    },
  ])

  function onHotAdd() {
    hotForm.value = new HotPublicOpinionAlert()
    hotOpen.value = true
  }

  async function onHotSave() {
    await api.HotPublicOpinionAlerts.Save_PostAsync(hotForm.value)
    message.success('保存成功')
    getList()
    hotOpen.value = false
  }

  return { hotOpen, hotForm, hotFields, onHotAdd, onHotSave }
}

/**
 * 热点/重大舆情
 */
function useOpinionHook() {
  const open = ref(false)

  const form = ref(new HotPublicOpinion())

  const fields: FormField[] = [
    {
      label: '舆情摘要',
      prop: 'summary',
      el: 'textarea',
      required: true,
      attrs: { rows: 3, showCount: true, maxlength: 100 },
    },
    {
      label: '发生时间',
      prop: 'occurTime',
      el: 'date-picker',
      required: true,
      attrs: { format: 'YYYY-MM-DD HH:mm', showTime: { format: 'HH:mm' } },
    },
    {
      label: '微博热搜榜',
      prop: 'weiboHotSearch',
      el: 'input-number',
      required: true,
      attrs: {},
    },
    {
      label: '舆情分类',
      prop: 'category',
      el: 'select',
      required: true,
      attrs: {
        api: api.TagManages.GetTagTreeAsync,
        params: { tagType: TagType.一级分类 },
        fieldNames: { label: 'value', value: 'value' },
      },
    },
    {
      label: '正面报道',
      prop: 'isPositive',
      el: 'boolean-select',
      required: true,
      attrs: {},
    },
  ]

  const rowSelection = ref({
    selectedRowKeys: [] as string[],
    onChange: (selectedRowKeys: string[]) => {
      rowSelection.value.selectedRowKeys = selectedRowKeys
    },
  })

  function onAdd() {
    open.value = true
  }

  async function onDelOption(id: GUID) {
    await api.HotPublicOpinionAlerts.DeleteHotPublicOpinion_PostAsync({ id })
    message.success('删除成功')
  }

  async function onSave() {
    form.value.hotPublicOpinionAlertId = currentHot.value.id
    form.value.occurTime = dayjs(form.value.occurTime)

    try {
      await api.HotPublicOpinionAlerts.SaveHotPublicOpinion_PostAsync(form.value)
      open.value = false
      message.success('保存成功')
      proTableRef.value?.search()
    }
    catch (error: any) {
      console.log(error)

      open.value = false
    }
  }

  return { open, form, fields, rowSelection, onAdd, onSave, onDelOption }
}
</script>

<style scoped lang="less">
.ch2-scroll-container::-webkit-scrollbar {
  height: 6px; /* 滚动条高度 */
}

:deep(.pro-table .table-main) {
  margin-top: 0;
  padding: 0;
}
:deep(.header-button-lf) {
  width: 100%;
}

:deep(.table-main) {
  margin-top: 0 !important;
  border: none !important;
  padding: 0 !important;
}
</style>
