import { FileType } from "./FileType";
import { FileAttribution } from "./FileAttribution";
/**文件信息*/
export class DeletedFileInfo {
  /**文件名*/
  fileName?: string | null | undefined = null;
  /**访问路径*/
  path?: string | null | undefined = null;
  /**文件大小*/
  length: number = 0;
  /**文件ContentType*/
  contentType?: string | null | undefined = null;
  /**文件类型*/
  fileType: FileType = 0;
  /**原始文件名称*/
  originalName?: string | null | undefined = null;
  /**删除人员*/
  deletedUserId: number = 0;
  /**删除时间*/
  deletedTime: Dayjs = dayjs();
  /**上传者Id*/
  uploadUserId: GUID = "00000000-0000-0000-0000-000000000000";
  /**上传时间*/
  uploadTime: Dayjs = dayjs();
  /**文件类型*/
  fileAttribution: FileAttribution = 0;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
