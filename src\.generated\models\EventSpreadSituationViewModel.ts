import { UserBaseViewModel } from "./UserBaseViewModel";
export class EventSpreadSituationViewModel {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  /**教育厅系统id*/
  jytEntityId?: GUID = null;
  /**内容*/
  context?: string | null | undefined = null;
  eventId: GUID = "00000000-0000-0000-0000-000000000000";
  /**创建时间*/
  created: Dayjs = dayjs();
  /**创建者*/
  createdId?: GUID = null;
  /**创建者*/
  createdBy?: UserBaseViewModel | null | undefined = null;
  /**修改人*/
  modifiedBy?: GUID = null;
  /**修改人*/
  modifiedUser?: UserBaseViewModel | null | undefined = null;
  /**修改时间*/
  modifiedAt?: Dayjs | null | undefined = null;
}
