import { PublicOpinionSubmissionAuditType } from "./PublicOpinionSubmissionAuditType";
import { RiskLevel } from "./RiskLevel";
export class PublicOpinionSubmissionListItem {
  id: GUID = "00000000-0000-0000-0000-000000000000";
  summary?: string | null | undefined = null;
  /**记录相关内容或描述*/
  content?: string | null | undefined = null;
  /**事件一级类别*/
  mainCategory?: string | null | undefined = null;
  /**二级类别*/
  category?: string | null | undefined = null;
  auditStatus: PublicOpinionSubmissionAuditType = 0;
  createdAt: Dayjs = dayjs();
  /**发布时间*/
  published?: Dayjs | null | undefined = null;
  /**风险等级*/
  riskLevel: RiskLevel = 0;
}
