<template>
  <a-button
    v-bind="$attrs"
    :loading="loading"
    @click="handleClick"
  >
    <slot />
  </a-button>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps<{
  handler: () => Promise<any>
}>()
const emit = defineEmits<{
  (e: 'success'): void
  (e: 'error', error: any): void
}>()

const loading = ref(false)

async function handleClick() {
  if (loading.value)
    return

  loading.value = true
  try {
    await props.handler()
    emit('success')
  }
  catch (error) {
    emit('error', error)
  }
  finally {
    loading.value = false
  }
}
</script>
