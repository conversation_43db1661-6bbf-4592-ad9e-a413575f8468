<template>
  <span v-if="getBindValue.readOnly">
    <template v-if="Array.isArray(modeValue)">
      <a-tag
        v-for="p in modeValue"
        :key="p"
        class="mb-2 mr-2 transition-all duration-300 hover:scale-105"
      >
        {{ p }}
      </a-tag>
    </template>

    <div v-else class="rounded-lg bg-gray-50 p-2">
      <div class="font-medium">{{ groupValue }}/{{ modeValue }}</div>
      <div class="mt-2 rounded bg-orange-50 p-2 text-sm text-gray-500">{{ remark }}</div>
    </div>
  </span>

  <div v-else class="group-select-wrapper">
    <span class="mb-2 block text-base font-medium">{{ groupValue }}</span>
    <a-select
      v-model:value="modeValue"
      style="width: 100%"
      :options="options"
      v-bind="$attrs"
      :field-names="{ label: 'value', value: 'value', options: 'children' }"
      option-label-prop="label"
      :dropdown-match-select-width="false"
      class="custom-select"
      @change="changeValue"
    >
      <template #option="{ value, description }">
        <div class="option-item">
          <div class="font-medium">{{ value }}</div>
          <div class="mt-1 text-sm text-gray-500">{{ description }}</div>
        </div>
      </template>
    </a-select>

    <div class="mt-2 rounded bg-orange-50 p-2 text-sm text-gray-500 transition-all duration-300">
      {{ remark }}
    </div>
  </div>
</template>

<script lang='ts' setup>
import type { TagManageView } from '@/api/models'
import type { FormConfig } from 'ch2-components/lib/form/types'
import * as api from '@/api'
import { useInjectFormConfig } from 'ch2-components/lib/form/src/useFormConfig'
import { computed, onMounted, ref, useAttrs } from 'vue'

const emit = defineEmits<{
  getChildrenData: [TagManageView | null]
}>()

const modeValue = defineModel<string | null>('value')
const groupValue = defineModel<string | null>('group')

const { getBindValue } = useInjectFormConfig<FormConfig>({ name: 'select', attrs: useAttrs() })

const options = ref<TagManageView[]>([])

function safeChildren(children: TagManageView[] | null | undefined): TagManageView[] {
  return Array.isArray(children) ? children : []
}

function getSelectedChild(): TagManageView | null {
  const group = options.value.find(v => v.value === groupValue.value)
  if (!group)
    return null
  return safeChildren(group.children).find(v => v.value === modeValue.value) ?? null
}

const remark = computed(() => getSelectedChild()?.description ?? '')

function changeValue(_v: any, data: any) {
  if (data?.parent?.value)
    groupValue.value = data.parent.value
  emit('getChildrenData', data ?? null)
}

function addParentReference(nodes: TagManageView[], parent: TagManageView | null = null): TagManageView[] {
  return nodes.map((node: any) => {
    node.parent = parent
    node.children = safeChildren(node.children)
    if (node.children.length) {
      node.children = addParentReference(node.children, node)
    }
    return node
  })
}

async function getOptions() {
  const res = await api.TagManages.GetTagTreeAsync({})
  options.value = addParentReference(res)
  const val = getSelectedChild()
  val && emit('getChildrenData', val)
}

onUnmounted(() => {
  eventBus.off('update-opinion-type', getOptions)
})

onMounted(() => {
  getOptions()
  eventBus.on('update-opinion-type', getOptions)
})
</script>

<style scoped>
.group-select-wrapper {
  @apply w-full;
}

.custom-select {
  @apply transition-all duration-300;
}

.custom-select:hover {
  @apply shadow-md;
}

.option-item {
  @apply p-2 hover:bg-gray-50 transition-colors duration-200;
}

:deep(.ant-select-dropdown) {
  @apply shadow-lg rounded-lg;
}

:deep(.ant-select-item) {
  @apply transition-colors duration-200;
}

:deep(.ant-select-item-option-selected) {
  @apply bg-blue-50;
}
</style>
