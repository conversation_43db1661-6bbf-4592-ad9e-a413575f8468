<template>
  <div
    v-if="queryObj.fileAttribution === FileAttribution.门户站点" class="flex b-1px b-border b-solid bg-white"
    :style="{ height: 'calc(100vh - 200px)' }"
  >
    <a-modal v-model:visible="upVisible" :mask-closable="false" title="文件上传中" :footer="null" :closable="false">
      <a-progress
        :stroke-color="{
          '0%': '#108ee9',
          '100%': '#87d068',
        }" :percent="progressRes"
      />
    </a-modal>
    <div class="menu w180px b-r-1px b-r-border b-r-solid bg-bg-elevated">
      <div
        v-for="(icon, value) in menu" :key="value" :class="{
          '!bg-primary !c-white !hover:bg-primary-active':
            Number(currentType) === Number(value),
        }" class="w-full cursor-pointer p8px c-text hover:(bg-primary-hover c-white)"
        @click="selectType(Number(value))"
      >
        <component :is="icon" />
        {{ FileType[value] }}
      </div>
      <a-divider orientation-margin="4px" class="!my8px" />

      <div
        class="w-full cursor-pointer p8px c-text hover:c-bg" :class="{
          '!bg-primary c-#fff !hover:bg-primary-bg-hover': currentType === 'time',
        }" @click="selectType('time')"
      >
        <c-icon-field-time-outlined /> 回收站
      </div>
    </div>

    <div class="file-list w0 flex flex-1 flex-col">
      <div class="px-16px py-8px c-#ff0000">提示：单次最多只能删除10个文件</div>
      <div class="tools relative z-9 w-full bg-bg-elevated">
        <div class="w-full flex items-center p4px pr8px shadow-[0_2px_4px_rgba(46,49,56,0.15)]">
          <a-button v-if="fileList.length" type="link" @click="onSelectAll">
            <template #icon>
              <c-icon-appstore-add-outlined />
            </template>
            {{ selectItemIds.length === fileList.length ? "取消选择" : "全选" }}
          </a-button>
          <a-popconfirm
            :title="currentType === 'time'
              ? '该操作将彻底删除所选文件，请确认操作！'
              : '确认删除所选文件?'
            " ok-text="确认删除" @confirm="onRemove"
          >
            <a-button
              v-if="fileList.length" danger type="link"
              :disabled="selectItemIds.length >= 10 || selectItemIds.length === 0" title="单次最多只能删除10个文件"
            >
              <template #icon>
                <c-icon-delete-outlined />
              </template>
              删除文件
            </a-button>
          </a-popconfirm>
          <a-input-search v-model:value="fileName" placeholder="请输入文件名查询" @search="onSearch" />

          <a-divider type="vertical" />
          <div class="ml-auto">
            <a-upload :before-upload="beforeUpload" :multiple="multiple" :accept="accept" :show-upload-list="false">
              <a-button v-if="currentType !== 'time'" type="primary">
                <template #icon>
                  <c-icon-upload-outlined />
                </template>
                上传
              </a-button>
            </a-upload>
          </div>
        </div>
      </div>

      <div
        ref="contentRef" class="box-border h0 w-full flex-1 overflow-y-auto bg-bg-container p16px"
        :style="{ '--img-size': itemWidth }"
      >
        <a-spin :spinning="spin">
          <div class="w-full flex flex-wrap gap16px">
            <div
              v-for="item in fileList" :key="item.id" :class="{ '!b-primary': selectItemIds.includes(item.id) }"
              class="box-border cursor-pointer b-1px b-#E3E5E8 b-solid shadow-[rgba(0,0,0,0.24)_0_3px_8px]"
            >
              <div class="relative h140px">
                <div class="tag absolute left-0 top-0 z-2 bg-#17191c px4px py2px text-12px c-white">
                  {{ extensionName(item).toUpperCase() }}
                </div>
                <a-image
                  v-if="item.fileType === FileType.图片" style="
                    height: 140px;
                    width: calc(var(--img-size) - 3.2px);
                    object-fit: cover;
                  " :src="item.viewPath!"
                />
                <div
                  v-else class="flex items-center justify-center" :style="{
                    background: bgSvg[extensionName(item)]?.color || '#ddd',
                  }" style="height: 140px; width: calc(var(--img-size) - 3.2px)"
                >
                  <Icon
                    v-if="extensionName(item) in bgSvg" class="text-[calc(var(--img-size)/3)]"
                    :component="bgSvg[extensionName(item)].icon"
                  />
                  <div
                    v-else
                    class="text-shadow-black text-[calc(var(--img-size)/4)] text-white font-600 text-shadow-lg text-shadow-color-#000000"
                  >
                    {{ extensionName(item).toUpperCase() }}
                  </div>
                </div>
              </div>
              <div
                class="file-name w-full flex justify-between b-t-1px b-t-border b-t-solid p8px text-12px c-text"
                @click="onSelectItem(item)"
              >
                <div class="w0 flex-1 overflow-hidden text-ellipsis text-nowrap" :title="item.originalName!">
                  {{ item.originalName }}
                </div>
                <div
                  class="select-icon ml4px c-text-tertiary" :class="{
                    '!block !c-primary': selectItemIds.includes(item.id),
                  }"
                >
                  <c-icon-check-outlined />
                </div>
              </div>
            </div>
            <i v-show="fileList.length === 0" class="c-text"> 暂无文件！ </i>
          </div>
        </a-spin>
      </div>

      <div class="relative z-9 flex bg-bg-elevated p8px shadow-[0_-2px_4px_rgba(46,49,56,0.15)]">
        <a-pagination
          v-model:current="queryObj.current" class="m-auto" show-quick-jumper :total="queryObj.total"
          :page-size="queryObj.limit" @change="onPageChange"
        />

        <a-button
          v-if="currentType !== 'time' && showSelectBtn" type="primary" class="ml-auto"
          :disabled="!selectItemIds.length" @click="() => emit('selected', selectItems, currentType as any)"
        >
          <template #icon>
            <c-icon-check-outlined />
          </template>
          选择
        </a-button>
      </div>
    </div>
  </div>

  <div v-else>
    <a-popover placement="bottom">
      <a-upload-dragger :before-upload="beforeUpload" :multiple="multiple" :accept="accept" :show-upload-list="false">
        <div class="h-40 flex flex-col items-center justify-center">
          <p class="ant-upload-drag-icon">
            <c-icon-inbox-outlined />
          </p>
          <p class="ant-upload-text">点击或拖动文件到此区域以上传</p>
          <p class="ant-upload-text">或者粘贴剪切板图片</p>
        </div>
      </a-upload-dragger>
      <template #content>
        <div class="min-w-250px">
          <div>剪切板</div>
          <div class="mt2 flex gap-4">
            <div v-for="(item, index) in clipboardImages" :key="index" class="group relative">
              <img :src="item" class="size-32 rounded object-cover" @click="handleClipboardImage(item)">
            </div>

            <div v-if="clipboardImages.length === 0" class="size-32 flex items-center justify-center rounded bg-gray-200 p2 text-xs c-text-quaternary">
              剪切板内容为空
            </div>
          </div>
        </div>
      </template>
    </a-popover>
  </div>
</template>

<script setup lang="ts">
import type { UploadFileInfo } from '@/api/models'
import * as api from '@/api'
import { FileAttribution, FileType } from '@/api/models'
import Icon, {
  CustomerServiceOutlined,
  FileMarkdownOutlined,
  FilePptOutlined,
  FileZipOutlined,
  PaperClipOutlined,
  PictureOutlined,
  PlaySquareOutlined,

} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, reactive, ref } from 'vue'
import archiveSvg from '../svg/archive'
import excelSvg from '../svg/excel'
import movieSvg from '../svg/movie'
import musicSvg from '../svg/music'
import pdfSvg from '../svg/pdf'
import pptSvg from '../svg/ppt'
import wordSvg from '../svg/word'
import { joinFilePath, uploadFile } from './uilt'
import { UploadConfig } from './uploadFileConfig'

const props = withDefaults(
  defineProps<{
    showSelectBtn?: boolean
    multiple?: boolean
    immediateReturn?: boolean
    fileType?: FileType
    accept?: string
    menu?: FileType[]
    fileAttribution?: FileAttribution
  }>(),
  { multiple: true },
)

const emit = defineEmits<{
  (event: 'selected', data: UploadFileInfo[], type: FileType): void
}>()

const uploadConfig = new UploadConfig()

const menu = reactive({
  [FileType.图片]: PictureOutlined,
  [FileType.文档]: FileMarkdownOutlined,
  [FileType.压缩]: FileZipOutlined,
  [FileType.视频]: PlaySquareOutlined,
  [FileType.音频]: CustomerServiceOutlined,
  [FileType.幻灯片]: FilePptOutlined,
  [FileType.未知]: PaperClipOutlined,
})

const accept = computed(() => {
  return props.accept ? props.accept : uploadConfig.getExtensions(Object.keys(menu) as any)
})

onMounted(() => {
  if (props.menu) {
    Object.keys(menu).forEach((key) => {
      if (!props.menu!.includes(Number(key) as any as FileType))
        delete menu[key as unknown as FileType]
    })
  }
})

const bgSvg: any = {
  'docx': wordSvg,
  'doc': wordSvg,
  'pdf': pdfSvg,
  'mp3': musicSvg,
  'wav': musicSvg,
  'zip': archiveSvg,
  'rar': archiveSvg,
  'tar': archiveSvg,
  'gz': archiveSvg,
  '7z': archiveSvg,
  'xlsx': excelSvg,
  'xls': excelSvg,
  'mp4': movieSvg,
  'avi': movieSvg,
  'mkv': movieSvg,
  'ppt': pptSvg,
  'pptx': pptSvg,
}

const currentType = ref<FileType | 'time'>(FileType.图片)

const fileName = ref('')

const extensionName = computed(() => (item: UploadFileInfo) => {
  return item.fileName?.split('.')?.pop() || ''
})

const fileList = ref<Array<UploadFileInfo & { viewPath: string }>>([])

const selectItems = ref<UploadFileInfo[]>([])

const selectItemIds = computed(() => selectItems.value.map(v => v.id))

function onSelectItem(item: UploadFileInfo) {
  if (selectItemIds.value.includes(item.id)) {
    selectItems.value = selectItems.value.filter(v => v.id !== item.id)
  }
  else {
    if (props.multiple)
      selectItems.value.push(item)
    else selectItems.value = [item]
  }
}

function onSelectAll() {
  if (selectItems.value.length === fileList.value.length)
    selectItems.value = []
  else selectItems.value = fileList.value
}

const queryObj = reactive({
  total: 0,
  limit: 20,
  offset: 0,
  current: 1,
  fileAttribution: props.fileAttribution ?? FileAttribution.门户站点,
})

const spin = ref(false)

function getData() {
  spin.value = true;
  (currentType.value === 'time'
    ? api.FileManage.GetRemoveFilesAsync
    : api.FileManage.GetFilesAsync)({
    type: currentType.value === 'time' ? undefined : currentType.value,
    fileName: fileName.value,
    ...queryObj,
  })
    .then((res) => {
      fileList.value = (res.items || []).map(v => ({
        ...v,
        viewPath: joinFilePath(v, 'p'),
      }))
      queryObj.total = res.totals || 0
      spin.value = false
    })
    .catch(() => (spin.value = false))
}

function onSearch() {
  queryObj.current = 1
  getData()
}

function onPageChange(pageNumber: number, pageSize: number) {
  queryObj.limit = pageSize
  queryObj.current = Math.max(pageNumber, 1)
  queryObj.offset = pageSize * (queryObj.current - 1)
  getData()
}

function selectType(type: FileType | 'time') {
  queryObj.offset = 0
  currentType.value = type
  selectItems.value = []
  getData()
}

const contentRef = useTemplateRef('contentRef')

const itemWidth = ref('170px')

useResizeObserver(contentRef, (entries) => {
  const entry = entries[0]
  if (entry) {
    const { width } = entry.contentRect
    const w = (width || 0) - 32 - 2
    const itemCount = w / 170

    if (Number.isInteger(itemCount)) {
      itemWidth.value = '170px'
    }
    else {
      const number = Math.floor(itemCount)
      itemWidth.value = `${(w - 16 * (number - 1)) / number}px`
    }
  }
})

onMounted(() => {
  if (props.fileType)
    currentType.value = props.fileType
  onSearch()
})

function onRemove() {
  (currentType.value === 'time'
    ? api.FileManage.RemoveCompletelyFile_PostAsync
    : api.FileManage.RemoveFile_PostAsync)(selectItemIds.value as any)
    .then(() => {
      selectItems.value = []
      getData()
      message.success('删除成功！')
    })
    .catch(() => {
      message.error('删除失败！')
      getData()
    })
}

const upVisible = ref(false)

const progressRes = ref(1)

// 剪贴板图片列表
const clipboardImages = ref<string[]>([])

// 获取剪贴板权限和内容
async function initClipboard() {
  try {
    // 请求剪贴板权限
    const permission = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName })
    console.log('剪贴板权限状态:', permission.state)

    if (permission.state === 'granted' || permission.state === 'prompt') {
      // 尝试读取剪贴板内容
      const clipboardItems = await navigator.clipboard.read()
      console.log('%c [ clipboardItems ]-380', 'font-size:13px; background:pink; color:#bf2c9f;', clipboardItems)
      for (const item of clipboardItems) {
        // 检查是否有图片类型
        if (item.types.includes('image/png') || item.types.includes('image/jpeg')) {
          const blob = await item.getType('image/png')
          const reader = new FileReader()
          reader.onload = (e) => {
            const result = e.target?.result
            if (typeof result === 'string') {
              clipboardImages.value.push(result)
            }
          }
          reader.readAsDataURL(blob)
        }
      }
    }
  }
  catch (error) {
    console.error('获取剪贴板内容失败:', error)
  }
}

// 监听剪贴板变化
onMounted(async () => {
  if (props.fileAttribution !== FileAttribution.门户站点) {
    window.addEventListener('paste', handlePaste)
    // 初始化时获取剪贴板内容
    await initClipboard()
  }
})

onUnmounted(() => {
  window.removeEventListener('paste', handlePaste)
})

// 处理剪贴板事件
function handlePaste(e: ClipboardEvent) {
  console.log('剪贴板事件触发')
  const items = e.clipboardData?.items
  if (!items) {
    console.log('没有剪贴板内容')
    return
  }

  for (const item of items) {
    console.log('剪贴板项目类型:', item.type)
    if (item.type.includes('image')) {
      const file = item.getAsFile()
      if (file) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const result = e.target?.result
          if (typeof result === 'string') {
            console.log('成功读取图片')
            // clipboardImages.value.push(result)
            handleClipboardImage(result)
          }
        }
        reader.readAsDataURL(file)
      }
    }
  }
}

// 处理剪贴板图片上传
async function handleClipboardImage(base64Image: string) {
  const response = await fetch(base64Image)
  const blob = await response.blob()
  const file = new File([blob], `clipboard-${Date.now()}.png`, { type: 'image/png' })
  // 调用上传方法
  await beforeUpload(file, [file])
}

// 修改 beforeUpload 函数，添加上传成功后更新最近上传列表
async function beforeUpload(flee: File, files: File[]) {
  if (!upVisible.value) {
    const res = files.filter(
      v => !uploadConfig.isMatch(v.name, Object.keys(menu) as any),
    )
    if (res.length) {
      message.error(`${res.map(v => v.name).join('，')}  上传文件类型不符合`)
      // eslint-disable-next-line prefer-promise-reject-errors
      return Promise.reject(false)
    }

    upVisible.value = true
    uploadFile(files, progressRes, undefined, queryObj.fileAttribution)
      .then(({ success }) => {
        upVisible.value = false
        getData()
        if (props.immediateReturn && currentType.value !== 'time') {
          emit(
            'selected',
            success,
            currentType.value,
          )
        }
        if (success.length) {
          message.success(`上传成功：${success.length}`)
        }
      })
      .catch((err) => {
        upVisible.value = false
        message.error(`上传失败：${err.message}`)
      })
  }
  // eslint-disable-next-line prefer-promise-reject-errors
  return Promise.reject(false)
}
</script>

<style scoped lang="less">
.file-name {
  &:hover .select-icon {
    color: @colorText;
  }
}
</style>

<style lang="less">
.file-manager-modal {
  .ant-modal-content {
    border-radius: 8px;
  }

  .ant-modal-body {
    padding: 0;
    border-radius: 8px;
  }

  .ant-modal-confirm-btns {
    display: none;
  }

  .ant-modal-confirm-title {
    background-color: #f9fafa;
    border-bottom: 1px solid #f9fafa;
    padding: 8px;
  }

  .ant-modal-close {
    .ant-modal-close-x {
      width: 40px;
      height: 40px;
      line-height: 40px;
    }
  }

  .ant-modal-confirm-content {
    margin-top: 0px;
  }
}
</style>
