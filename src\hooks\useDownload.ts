import { message } from 'ant-design-vue'

/**
 * @description 接收数据流生成blob，创建链接，下载文件
 * @param {Function} api 导出表格的api方法(必传)
 * @param {string} tempName 导出的文件名(必传)
 * @param {boolean} isNotify 是否有导出消息提示(默认为 true)
 * @param {string} fileType 导出的文件格式(默认为.xlsx)
 * @return void
 */
export async function useDownload(
  api: () => Promise<any>,
  tempName = '',
  isNotify = true,
  fileType = '.xlsx',
) {
  if (isNotify)
    message.success('如果数据庞大会导致下载缓慢哦，请您耐心等待', 3)

  try {
    const res = await api()
    const contentType = res.headers['content-type']

    if (contentType.includes('application/json')) {
      const reader = new FileReader()
      reader.onload = () => {
        const result = JSON.parse(reader.result as string)
        message.error(result.message || '下载失败，请稍后再试！')
      }
      reader.readAsText(res.data)
      return
    }

    const contentDisposition = res.headers.get('Content-Disposition')
    tempName = tempName + fileType
    if (contentDisposition && contentDisposition.includes('attachment')) {
      tempName = getFileName(contentDisposition)
    }
    const blob = res.data
    // 兼容 edge 不支持 createObjectURL 方法
    if ('msSaveOrOpenBlob' in navigator)
      return (window.navigator as any).msSaveOrOpenBlob(blob, tempName)

    const blobUrl = window.URL.createObjectURL(blob)
    console.log('%c [ blobUrl ]-30', 'font-size:13px; background:pink; color:#bf2c9f;', blobUrl)
    const exportFile = document.createElement('a')
    exportFile.style.display = 'none'
    exportFile.download = `${tempName}`
    exportFile.href = blobUrl
    document.body.appendChild(exportFile)
    exportFile.click()
    // 去除下载对 url 的影响
    document.body.removeChild(exportFile)
    window.URL.revokeObjectURL(blobUrl)
  }
  catch (error) {
    console.log(error)
  }
}

function getFileName(contentDisposition: string): string {
  const regex = /filename\*?=([\w'*();&=,.%-]+)(?:;|$)/g
  let match: RegExpExecArray | null
  let fileName: string = ''

  // 循环处理正则表达式的匹配
  // eslint-disable-next-line no-cond-assign
  while ((match = regex.exec(contentDisposition)) !== null) {
    if (match[0].includes('filename*=')) {
      // 对 filename* 的值进行解码（可能包含 UTF-8 编码的字符）
      const encodedFileName = match[1]?.replace(/^UTF-8''/, '') || ''
      fileName = decodeURIComponent(encodedFileName)
    }
    else if (match[0].includes('filename=')) {
      fileName = match[1]!
    }
  }

  return fileName
}

const contentDisposition: string = 'attachment; filename=____.xlsx; filename*=UTF-8\'\'%E6%B5%8B%E8%AF%95%E5%85%B3%E8%81%94.xlsx'
const fileName: string = getFileName(contentDisposition)
console.log(fileName) // 输出：测试关联.xlsx
