<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-06-11 11:24:00
 * @LastEditors: 景 彡
-->
<template>
  <c-form ref="ccCopyFormRef" :model="ccCopyState" autocomplete="off">
    <a-descriptions bordered :label-style="{ width: '140px' }" :carbon-copy-text-style="{ width: '300px' }" :column="4">
      <a-descriptions-item label="备注" :span="4" class="required">
        <a-form-item name="carbonCopyText" :rules="[{ required: true, message: '请输入备注!' }]">
          <c-textarea v-model:value="ccCopyState.carbonCopyText" placeholder="请输入备注" allow-clear />
        </a-form-item>
      </a-descriptions-item>
      <a-descriptions-item label="单位" :span="4" class="required">
        <a-form-item name="deptIds" :rules="[{ required: true, message: '请选择抄送单位!' }]">
          <C2TreeSelect v-model:value="ccCopyState.deptIds!" :disabled-ids="currentDeptIds" tree-checkable :api="api.DepartmentManage.GetAllDepartmentsAsync" />
        </a-form-item>
      </a-descriptions-item>
    </a-descriptions>
  </c-form>
</template>

<script lang='ts' setup>
import * as api from '@/api'
import { message } from 'ant-design-vue'

const props = defineProps<{ evenId: GUID | string, currentDeptIds: GUID[] }>()

const ccCopyFormRef = useTemplateRef('ccCopyFormRef')

const ccCopyState = ref({
  carbonCopyText: '',
  deptIds: [],
})

async function onSave() {
  return await ccCopyFormRef.value?.baseEl?.validate().then(() => api.EventManage.AddCarbonCopy_PostAsync({ carbonCopyText: ccCopyState.value.carbonCopyText, eventId: props.evenId! }, ccCopyState.value.deptIds)).catch((err: { message: any }) => {
    if (err.message)
      message.error(`保存失败：${err.message}`)
    throw err
  })
}

function onClose() {
  ccCopyState.value = {
    carbonCopyText: '',
    deptIds: [],
  }
}

defineExpose({
  onSave,
  onClose,
})
</script>

<style scoped>

</style>
