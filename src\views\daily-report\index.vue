<template>
  <c-pro-table
    ref="proTableRef" :row-class-name="(_record, index) => (index % 2 === 1 ? 'c2-table-striped' : 'c2-table-prototype')"
    :row-key="(record) => record.id" size="small"
    :columns="columns"
    :api="api.OpinionManage.GetListAsync"
    :show-search="false"
    :show-tool-btn="false" immediate serial-number operation :get-params="params"
  >
    <template #header>
      <div class="w-full flex justify-between">
        <a-radio-group v-model:value="params.handledStatus" @change="proTableRef?.search()">
          <a-radio-button :value="HandledType.查询全部">全部</a-radio-button>
          <a-radio-button :value="HandledType.处置完成">已处置</a-radio-button>
          <a-radio-button :value="HandledType.未处理">未处置</a-radio-button>
          <a-radio-button :value="HandledType.已读">已读</a-radio-button>
        </a-radio-group>
        <a-input-search
          v-model:value="params.keyWord"
          placeholder="请输入关键词"
          style="width: 280px"
          size="large"
          @search="proTableRef?.search()"
        />
      </div>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'content'">
        <div>{{ record.content }}</div>
        <div class="flex space-x-2">
          <div v-for="(img, idx) in record.images" :key="idx" class="coverBox size-30px overflow-hidden">
            <c-image
              :src="joinFilePathById(img)" alt="avatar" :preview="true"
              style="height: 30px; width:30px ; object-fit:cover"
            />
          </div>
        </div>
        <div>
          <a v-for="(url, idx) in record.url?.split(/\r?\n/)" :key="idx" :href="url" target="_blank"> {{ url }} </a>
        </div>
      </template>
      <template v-if="column.dataIndex === 'publisher'">
        <div class="flex items-center">
          <c-image
            :src="joinFilePathById(getByName(record.source!)?.iconId!)" alt="avatar" :preview="true"
            style="height: 20px; width:20px ; object-fit:cover"
          />
          <span class="ml-1">{{ record.publisher }}</span>
        </div>
        <div>id: {{ record.sourceId }}</div>
        <div class="c-text-secondary">{{ dateTime(record.published, 'YYYY-MM-DD HH:mm') }}</div>
      </template>
      <template v-if="column.dataIndex === 'dept'">
        <div class="text-center">
          <div>{{ record.dept?.name }}</div>
          <span>{{ record.address }}</span>
        </div>
      </template>
      <template v-if="column.dataIndex === 'auditStatus'">
        <div class="text-center">
          <a-tag v-if="record.auditStatus === AuditType.处置" class="mr-0!" color="red">
            已上报
          </a-tag>
          <a-tag v-else class="mr-0!" color="default">
            未上报
          </a-tag>
        </div>
      </template>
    </template>
    <template #operation="{ record }">
      <div v-if="$auth([_Role.合作单位])" class="flex flex-col">
        <a v-if="record.handledStatus === undefined || record.handledStatus === HandledType.未处理" @click="onAudit(record.id, HandledType.已读)">标记已读</a>
        <span v-if="record.handledStatus === HandledType.处置完成 || record.handledStatus === HandledType.已读" class="c-text">已读</span>
        <a v-if="record.handledStatus === undefined || record.handledStatus !== HandledType.处置完成" @click="onAudit(record.id, HandledType.处置完成)">处置</a>
        <span v-else class="cursor-pointer c-success" @click="onView(record)">已处置</span>
      </div>
    </template>
  </c-pro-table>

  <a-drawer v-model:open="open" destroy-on-close title="处置" width="860px" @close="open = false">
    <c-pro-form v-model:value="formState" :fields="fields" :descriptions="{ column: 1, bordered: true }" :read-only="readOnly" @finish="deptAudit">
      <template #footer>
        <a-descriptions-item v-if="!readOnly" label="操作">
          <a-button type="primary" html-type="submit">
            保存
          </a-button>
        </a-descriptions-item>
      </template>
    </c-pro-form>
  </a-drawer>
</template>

<script lang='ts' setup>
import type { PublicOpinionViewModel, UploadFileInfo } from '@/api/models'
import * as api from '@/api'
import { AuditType, FileAttribution, HandledType } from '@/api/models'
import Upload from '@/components/FileManager/Upload.vue'
import { useSocialMediaCache } from '@/hooks/useSocialMediaCache'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import { Guid } from '@/utils/GUID'
import { message, Modal } from 'ant-design-vue'

definePage({
  meta: {
    title: '舆情日报',
    icon: 'ScheduleOutlined',
    local: true,
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '舆情日报',
        local: true,
        icon: 'ScheduleOutlined',
        order: 7,
      },
    },
  },
})

const { getByName } = useSocialMediaCache()

const proTableRef = useTemplateRef('proTableRef')

const params = ref({
  handledStatus: HandledType.查询全部,
  keyWord: '',
})

const columns = ref([
  {
    title: '舆情摘要',
    dataIndex: 'summary',
  },
  { title: '发帖人信息', dataIndex: 'publisher', width: 240 },
  { title: '原文内容', dataIndex: 'content' },
  { title: '涉及地区/单位', dataIndex: 'dept', width: 150 },
  { title: '推送时间', dataIndex: 'auditAt', width: 160, dateFormat: 'YYYY-MM-DD HH:mm' },
  { title: '上报教育厅', dataIndex: 'auditStatus', width: 120, align: 'center' },
])

const formState = ref({
  id: Guid.empty,
  audit: HandledType.已读,
  handledText: '',
  handledAttachmentIds: '',
})

const open = ref(false)

const files = ref<UploadFileInfo[]>([])

const fields = computed(() => [
  {
    label: '处置内容',
    prop: 'handledText',
    el: 'textarea',
    formItem: {
      rules: [{ required: true, message: '专题概况必填!' }],
    },
    attrs: { rows: 4, showCount: true, maxlength: 100 },
  },
  {
    label: '附件',
    prop: 'handledAttachmentIds',
    el: () => h(Upload, { fileList: files.value, config: { immediateReturn: true, fileAttribution: FileAttribution.管理认证 } }),
    descriptionsItem: { span: 2 },
    attrs: {
    },
  },
])

const readOnly = ref(false)

function onView(record: PublicOpinionViewModel) {
  readOnly.value = true
  files.value = record.handledAttachmentFiles || []
  formState.value = {
    id: record.id,
    audit: record.handledStatus || '',
    handledText: record.handledText || '',
    handledAttachmentIds: record.handledAttachmentIds?.[0],
  }
  open.value = true
}

async function deptAudit() {
  try {
    await api.OpinionManage.CooperateDeptAudit_PostAsync(formState.value)
    proTableRef.value?.search()
    message.success('操作成功')
    open.value = false
  }
  catch (error: any) {
    message.error(error.message)
  }
}

function onAudit(id: GUID, audit: HandledType) {
  formState.value.id = id
  formState.value.audit = audit

  if (audit === HandledType.已读) {
    Modal.confirm({
      title: '确认标记已读吗？',
      okText: '确认',
      okType: 'primary',
      cancelText: '取消',
      async onOk() {
        deptAudit()
      },
    })
  }
  else {
    open.value = true
  }
}
</script>

<style scoped lang="less">
:deep(.c2-table-striped) td {
  background-color: @colorPrimaryBg;
}

:deep(.ant-table-thead tr th) {
  background: @colorPrimaryBgHover !important;
}
:deep(.header-button-lf) {
  width: 100%;
}
</style>
