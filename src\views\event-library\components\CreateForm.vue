<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-06-16 17:55:09
 * @LastEditors: 景 彡
-->
<template>
  <div :class="{ 'edit-form': !readOnly }">
    <c-form ref="formRef" :read-only="readOnly" :model="formState" name="basic" autocomplete="off">
      <a-descriptions bordered :label-style="{ width: '140px' }" :content-style="{ width: '300px' }" :column="4">
        <a-descriptions-item label="教育厅系统事件id" :span="4">
          <a-form-item name="jytEntityId">
            <c-input v-model:value="formState.jytEntityId!" placeholder="请输入教育厅系统事件id" />
          </a-form-item>
          <span class="c-error">教育厅系统事件id，从教育厅系统复制回来</span>
        </a-descriptions-item>
        <a-descriptions-item label="事件名称" :span="4" class="required">
          <a-form-item name="name" :rules="[{ required: true, message: '请输入事件名称!' }]">
            <c-textarea v-model:value="formState.name!" placeholder="请输入事件名称" allow-clear />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="信息标题" :span="4" class="required">
          <a-form-item name="title" :rules="[{ required: true, message: '请输入信息标题!' }]">
            <a-textarea v-model:value="formState.title!" placeholder="请输入事件标题" allow-clear />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item v-if="!readOnly" label="跟踪要求" :span="4" class="required">
          <a-form-item name="trackContent" :rules="[{ required: true, message: '请输入跟踪要求!' }]">
            <a-textarea v-model:value="formState.trackContent!" placeholder="请输入跟踪要求" allow-clear />
          </a-form-item>
        </a-descriptions-item>
        <template v-if="!formState.id">
          <a-descriptions-item v-if="!readOnly" label="关联单位" :span="4" class="required">
            <a-form-item name="carbonCopy" :rules="[{ required: true, message: '请选择关联单位!' }]">
              <C2TreeSelect v-model:value="formState.carbonCopy!" multiple :api="api.DepartmentManage.GetAllDepartmentsAsync" />
            </a-form-item>
          </a-descriptions-item>
          <a-descriptions-item v-if="!readOnly" label="备注" :span="4">
            <a-form-item name="carbonCopyText">
              <a-textarea v-model:value="formState.carbonCopyText!" placeholder="请输入备注" allow-clear />
            </a-form-item>
          </a-descriptions-item>
        </template>
      </a-descriptions>
    </c-form>
  </div>
</template>

<script lang='ts' setup>
import * as api from '@/api'
import { PublicEventCreateModel } from '@/api/models'
import { message } from 'ant-design-vue'

const props = defineProps<{
  readOnly?: boolean
}>()

const formState = defineModel<PublicEventCreateModel>({
  required: true,
  default: () => (new PublicEventCreateModel()),
})
// const formState = ref(new PublicEventCreateModel())

const formRef = useTemplateRef('formRef')

watch(() => props.readOnly, (v) => {
  if (v) {
    formRef.value?.baseEl?.clearValidate()
  }
})

async function onSubmit() {
  let temp = api.EventManage.Create_PostAsync
  if (formState.value.id) {
    temp = api.EventManage.Update_PostAsync
  }
  return await formRef.value?.baseEl?.validate().then(() => temp(formState.value)).catch((err: { message: any }) => {
    if (err.message)
      message.error(`保存失败：${err.message}`)
    throw err
  })
}

defineExpose({
  onSubmit,
})
</script>

<style scoped lang="less">
:deep(.ant-form-item) {
  margin-bottom: 0;
}

.edit-form {
  :deep(.ant-descriptions-item-label.required span) {
    display: flex;
    &::before {
      display: block;
      content: '*';
      color: red;
      padding-right: 4px;
    }
  }
}
</style>
