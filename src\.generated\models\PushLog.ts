import { PushType } from "./PushType";
import { PushStatus } from "./PushStatus";
import { User } from "./User";
/**教育厅推送记录*/
export class PushLog {
  /**推送类型*/
  entityType: PushType = 0;
  /**实体主键*/
  entityId: GUID = "00000000-0000-0000-0000-000000000000";
  status: PushStatus = 0;
  /**响应信息（错误信息）*/
  responseMessage?: string | null | undefined = null;
  /**推送时间*/
  createdTime: Dayjs = dayjs();
  /**用户*/
  createdUser?: User | null | undefined = null;
  createdUserId: GUID = "00000000-0000-0000-0000-000000000000";
  /**被推送数据副本（JSON格式）*/
  copyData?: string | null | undefined = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
