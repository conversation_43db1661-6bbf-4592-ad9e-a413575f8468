{"version": 3, "sources": ["../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/Errors.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/HttpClient.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/ILogger.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/Loggers.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/Utils.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/FetchHttpClient.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/XhrHttpClient.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/DefaultHttpClient.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/TextMessageFormat.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/HandshakeProtocol.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/IHubProtocol.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/Subject.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/HubConnection.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/DefaultReconnectPolicy.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/HeaderNames.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/AccessTokenHttpClient.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/ITransport.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/AbortController.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/LongPollingTransport.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/ServerSentEventsTransport.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/WebSocketTransport.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/HttpConnection.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/JsonHubProtocol.ts", "../../../../../node_modules/.pnpm/@microsoft+signalr@7.0.14/node_modules/@microsoft/signalr/src/HubConnectionBuilder.ts"], "sourcesContent": ["// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpTransportType } from \"./ITransport\";\r\n\r\n/** Error thrown when an HTTP request fails. */\r\nexport class HttpError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The HTTP status code represented by this error. */\r\n    public statusCode: number;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     * @param {number} statusCode The HTTP status code represented by this error.\r\n     */\r\n    constructor(errorMessage: string, statusCode: number) {\r\n        const trueProto = new.target.prototype;\r\n        super(`${errorMessage}: Status code '${statusCode}'`);\r\n        this.statusCode = statusCode;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when a timeout elapses. */\r\nexport class TimeoutError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.TimeoutError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"A timeout occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when an action is aborted. */\r\nexport class AbortError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** Constructs a new instance of {@link AbortError}.\r\n     *\r\n     * @param {string} errorMessage A descriptive error message.\r\n     */\r\n    constructor(errorMessage: string = \"An abort occurred.\") {\r\n        const trueProto = new.target.prototype;\r\n        super(errorMessage);\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is unsupported by the browser. */\r\n/** @private */\r\nexport class UnsupportedTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.UnsupportedTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'UnsupportedTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport is disabled by the browser. */\r\n/** @private */\r\nexport class DisabledTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.DisabledTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'DisabledTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the selected transport cannot be started. */\r\n/** @private */\r\nexport class FailedToStartTransportError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The {@link @microsoft/signalr.HttpTransportType} this error occurred on. */\r\n    public transport: HttpTransportType;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToStartTransportError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {HttpTransportType} transport The {@link @microsoft/signalr.HttpTransportType} this error occurred on.\r\n     */\r\n    constructor(message: string, transport: HttpTransportType) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.transport = transport;\r\n        this.errorType = 'FailedToStartTransportError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when the negotiation with the server failed to complete. */\r\n/** @private */\r\nexport class FailedToNegotiateWithServerError extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The type name of this error. */\r\n    public errorType: string;\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.FailedToNegotiateWithServerError}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     */\r\n    constructor(message: string) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n        this.errorType = 'FailedToNegotiateWithServerError';\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n\r\n/** Error thrown when multiple errors have occurred. */\r\n/** @private */\r\nexport class AggregateErrors extends Error {\r\n    // @ts-ignore: Intentionally unused.\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private __proto__: Error;\r\n\r\n    /** The collection of errors this error is aggregating. */\r\n    public innerErrors: Error[];\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.AggregateErrors}.\r\n     *\r\n     * @param {string} message A descriptive error message.\r\n     * @param {Error[]} innerErrors The collection of errors this error is aggregating.\r\n     */\r\n    constructor(message: string, innerErrors: Error[]) {\r\n        const trueProto = new.target.prototype;\r\n        super(message);\r\n\r\n        this.innerErrors = innerErrors;\r\n\r\n        // Workaround issue in Typescript compiler\r\n        // https://github.com/Microsoft/TypeScript/issues/13965#issuecomment-278570200\r\n        this.__proto__ = trueProto;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortSignal } from \"./AbortController\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\n\r\n/** Represents an HTTP request. */\r\nexport interface HttpRequest {\r\n    /** The HTTP method to use for the request. */\r\n    method?: string;\r\n\r\n    /** The URL for the request. */\r\n    url?: string;\r\n\r\n    /** The body content for the request. May be a string or an ArrayBuffer (for binary data). */\r\n    content?: string | ArrayBuffer;\r\n\r\n    /** An object describing headers to apply to the request. */\r\n    headers?: MessageHeaders;\r\n\r\n    /** The XMLHttpRequestResponseType to apply to the request. */\r\n    responseType?: XMLHttpRequestResponseType;\r\n\r\n    /** An AbortSignal that can be monitored for cancellation. */\r\n    abortSignal?: AbortSignal;\r\n\r\n    /** The time to wait for the request to complete before throwing a TimeoutError. Measured in milliseconds. */\r\n    timeout?: number;\r\n\r\n    /** This controls whether credentials such as cookies are sent in cross-site requests. */\r\n    withCredentials?: boolean;\r\n}\r\n\r\n/** Represents an HTTP response. */\r\nexport class HttpResponse {\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     */\r\n    constructor(statusCode: number);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code and message.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and string content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {string} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: string);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and binary content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {ArrayBuffer} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: ArrayBuffer);\r\n\r\n    /** Constructs a new instance of {@link @microsoft/signalr.HttpResponse} with the specified status code, message and binary content.\r\n     *\r\n     * @param {number} statusCode The status code of the response.\r\n     * @param {string} statusText The status message of the response.\r\n     * @param {string | ArrayBuffer} content The content of the response.\r\n     */\r\n    constructor(statusCode: number, statusText: string, content: string | ArrayBuffer);\r\n    constructor(\r\n        public readonly statusCode: number,\r\n        public readonly statusText?: string,\r\n        public readonly content?: string | ArrayBuffer) {\r\n    }\r\n}\r\n\r\n/** Abstraction over an HTTP client.\r\n *\r\n * This class provides an abstraction over an HTTP client so that a different implementation can be provided on different platforms.\r\n */\r\nexport abstract class HttpClient {\r\n    /** Issues an HTTP GET request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public get(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP GET request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public get(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public get(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"GET\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP POST request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public post(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP POST request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public post(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public post(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"POST\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP DELETE request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public delete(url: string): Promise<HttpResponse>;\r\n\r\n    /** Issues an HTTP DELETE request to the specified URL, returning a Promise that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {string} url The URL for the request.\r\n     * @param {HttpRequest} options Additional options to configure the request. The 'url' field in this object will be overridden by the url parameter.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an {@link @microsoft/signalr.HttpResponse} describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public delete(url: string, options: HttpRequest): Promise<HttpResponse>;\r\n    public delete(url: string, options?: HttpRequest): Promise<HttpResponse> {\r\n        return this.send({\r\n            ...options,\r\n            method: \"DELETE\",\r\n            url,\r\n        });\r\n    }\r\n\r\n    /** Issues an HTTP request to the specified URL, returning a {@link Promise} that resolves with an {@link @microsoft/signalr.HttpResponse} representing the result.\r\n     *\r\n     * @param {HttpRequest} request An {@link @microsoft/signalr.HttpRequest} describing the request to send.\r\n     * @returns {Promise<HttpResponse>} A Promise that resolves with an HttpResponse describing the response, or rejects with an Error indicating a failure.\r\n     */\r\n    public abstract send(request: HttpRequest): Promise<HttpResponse>;\r\n\r\n    /** Gets all cookies that apply to the specified URL.\r\n     *\r\n     * @param url The URL that the cookies are valid for.\r\n     * @returns {string} A string containing all the key-value cookie pairs for the specified URL.\r\n     */\r\n    // @ts-ignore\r\n    public getCookieString(url: string): string {\r\n        return \"\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// These values are designed to match the ASP.NET Log Levels since that's the pattern we're emulating here.\r\n/** Indicates the severity of a log message.\r\n *\r\n * Log Levels are ordered in increasing severity. So `Debug` is more severe than `Trace`, etc.\r\n */\r\nexport enum LogLevel {\r\n    /** Log level for very low severity diagnostic messages. */\r\n    Trace = 0,\r\n    /** Log level for low severity diagnostic messages. */\r\n    Debug = 1,\r\n    /** Log level for informational diagnostic messages. */\r\n    Information = 2,\r\n    /** Log level for diagnostic messages that indicate a non-fatal problem. */\r\n    Warning = 3,\r\n    /** Log level for diagnostic messages that indicate a failure in the current operation. */\r\n    Error = 4,\r\n    /** Log level for diagnostic messages that indicate a failure that will terminate the entire application. */\r\n    Critical = 5,\r\n    /** The highest possible log level. Used when configuring logging to indicate that no log messages should be emitted. */\r\n    None = 6,\r\n}\r\n\r\n/** An abstraction that provides a sink for diagnostic messages. */\r\nexport interface ILogger {\r\n    /** Called by the framework to emit a diagnostic message.\r\n     *\r\n     * @param {LogLevel} logLevel The severity level of the message.\r\n     * @param {string} message The message.\r\n     */\r\n    log(logLevel: LogLevel, message: string): void;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\n\r\n/** A logger that does nothing when log messages are sent to it. */\r\nexport class NullLogger implements ILogger {\r\n    /** The singleton instance of the {@link @microsoft/signalr.NullLogger}. */\r\n    public static instance: ILogger = new NullLogger();\r\n\r\n    private constructor() {}\r\n\r\n    /** @inheritDoc */\r\n    // eslint-disable-next-line\r\n    public log(_logLevel: LogLevel, _message: string): void {\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Version token that will be replaced by the prepack command\r\n/** The version of the SignalR client. */\r\n\r\nexport const VERSION: string = \"0.0.0-DEV_BUILD\";\r\n/** @private */\r\nexport class Arg {\r\n    public static isRequired(val: any, name: string): void {\r\n        if (val === null || val === undefined) {\r\n            throw new Error(`The '${name}' argument is required.`);\r\n        }\r\n    }\r\n    public static isNotEmpty(val: string, name: string): void {\r\n        if (!val || val.match(/^\\s*$/)) {\r\n            throw new Error(`The '${name}' argument should not be empty.`);\r\n        }\r\n    }\r\n\r\n    public static isIn(val: any, values: any, name: string): void {\r\n        // TypeScript enums have keys for **both** the name and the value of each enum member on the type itself.\r\n        if (!(val in values)) {\r\n            throw new Error(`Unknown ${name} value: ${val}.`);\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class Platform {\r\n    // react-native has a window but no document so we should check both\r\n    public static get isBrowser(): boolean {\r\n        return typeof window === \"object\" && typeof window.document === \"object\";\r\n    }\r\n\r\n    // WebWorkers don't have a window object so the isBrowser check would fail\r\n    public static get isWebWorker(): boolean {\r\n        return typeof self === \"object\" && \"importScripts\" in self;\r\n    }\r\n\r\n    // react-native has a window but no document\r\n    static get isReactNative(): boolean {\r\n        return typeof window === \"object\" && typeof window.document === \"undefined\";\r\n    }\r\n\r\n    // Node apps shouldn't have a window object, but WebWorkers don't either\r\n    // so we need to check for both WebWorker and window\r\n    public static get isNode(): boolean {\r\n        return !this.isBrowser && !this.isWebWorker && !this.isReactNative;\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getDataDetail(data: any, includeContent: boolean): string {\r\n    let detail = \"\";\r\n    if (isArrayBuffer(data)) {\r\n        detail = `Binary data of length ${data.byteLength}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${formatArrayBuffer(data)}'`;\r\n        }\r\n    } else if (typeof data === \"string\") {\r\n        detail = `String data of length ${data.length}`;\r\n        if (includeContent) {\r\n            detail += `. Content: '${data}'`;\r\n        }\r\n    }\r\n    return detail;\r\n}\r\n\r\n/** @private */\r\nexport function formatArrayBuffer(data: ArrayBuffer): string {\r\n    const view = new Uint8Array(data);\r\n\r\n    // Uint8Array.map only supports returning another Uint8Array?\r\n    let str = \"\";\r\n    view.forEach((num) => {\r\n        const pad = num < 16 ? \"0\" : \"\";\r\n        str += `0x${pad}${num.toString(16)} `;\r\n    });\r\n\r\n    // Trim of trailing space.\r\n    return str.substr(0, str.length - 1);\r\n}\r\n\r\n// Also in signalr-protocol-msgpack/Utils.ts\r\n/** @private */\r\nexport function isArrayBuffer(val: any): val is ArrayBuffer {\r\n    return val && typeof ArrayBuffer !== \"undefined\" &&\r\n        (val instanceof ArrayBuffer ||\r\n            // Sometimes we get an ArrayBuffer that doesn't satisfy instanceof\r\n            (val.constructor && val.constructor.name === \"ArrayBuffer\"));\r\n}\r\n\r\n/** @private */\r\nexport async function sendMessage(logger: ILogger, transportName: string, httpClient: HttpClient, url: string,\r\n                                  content: string | ArrayBuffer, options: IHttpConnectionOptions): Promise<void> {\r\n    const headers: {[k: string]: string} = {};\r\n\r\n    const [name, value] = getUserAgentHeader();\r\n    headers[name] = value;\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) sending data. ${getDataDetail(content, options.logMessageContent!)}.`);\r\n\r\n    const responseType = isArrayBuffer(content) ? \"arraybuffer\" : \"text\";\r\n    const response = await httpClient.post(url, {\r\n        content,\r\n        headers: { ...headers, ...options.headers},\r\n        responseType,\r\n        timeout: options.timeout,\r\n        withCredentials: options.withCredentials,\r\n    });\r\n\r\n    logger.log(LogLevel.Trace, `(${transportName} transport) request complete. Response status: ${response.statusCode}.`);\r\n}\r\n\r\n/** @private */\r\nexport function createLogger(logger?: ILogger | LogLevel): ILogger {\r\n    if (logger === undefined) {\r\n        return new ConsoleLogger(LogLevel.Information);\r\n    }\r\n\r\n    if (logger === null) {\r\n        return NullLogger.instance;\r\n    }\r\n\r\n    if ((logger as ILogger).log !== undefined) {\r\n        return logger as ILogger;\r\n    }\r\n\r\n    return new ConsoleLogger(logger as LogLevel);\r\n}\r\n\r\n/** @private */\r\nexport class SubjectSubscription<T> implements ISubscription<T> {\r\n    private _subject: Subject<T>;\r\n    private _observer: IStreamSubscriber<T>;\r\n\r\n    constructor(subject: Subject<T>, observer: IStreamSubscriber<T>) {\r\n        this._subject = subject;\r\n        this._observer = observer;\r\n    }\r\n\r\n    public dispose(): void {\r\n        const index: number = this._subject.observers.indexOf(this._observer);\r\n        if (index > -1) {\r\n            this._subject.observers.splice(index, 1);\r\n        }\r\n\r\n        if (this._subject.observers.length === 0 && this._subject.cancelCallback) {\r\n            this._subject.cancelCallback().catch((_) => { });\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport class ConsoleLogger implements ILogger {\r\n    private readonly _minLevel: LogLevel;\r\n\r\n    // Public for testing purposes.\r\n    public out: {\r\n        error(message: any): void,\r\n        warn(message: any): void,\r\n        info(message: any): void,\r\n        log(message: any): void,\r\n    };\r\n\r\n    constructor(minimumLogLevel: LogLevel) {\r\n        this._minLevel = minimumLogLevel;\r\n        this.out = console;\r\n    }\r\n\r\n    public log(logLevel: LogLevel, message: string): void {\r\n        if (logLevel >= this._minLevel) {\r\n            const msg = `[${new Date().toISOString()}] ${LogLevel[logLevel]}: ${message}`;\r\n            switch (logLevel) {\r\n                case LogLevel.Critical:\r\n                case LogLevel.Error:\r\n                    this.out.error(msg);\r\n                    break;\r\n                case LogLevel.Warning:\r\n                    this.out.warn(msg);\r\n                    break;\r\n                case LogLevel.Information:\r\n                    this.out.info(msg);\r\n                    break;\r\n                default:\r\n                    // console.debug only goes to attached debuggers in Node, so we use console.log for Trace and Debug\r\n                    this.out.log(msg);\r\n                    break;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getUserAgentHeader(): [string, string] {\r\n    let userAgentHeaderName = \"X-SignalR-User-Agent\";\r\n    if (Platform.isNode) {\r\n        userAgentHeaderName = \"User-Agent\";\r\n    }\r\n    return [ userAgentHeaderName, constructUserAgent(VERSION, getOsName(), getRuntime(), getRuntimeVersion()) ];\r\n}\r\n\r\n/** @private */\r\nexport function constructUserAgent(version: string, os: string, runtime: string, runtimeVersion: string | undefined): string {\r\n    // Microsoft SignalR/[Version] ([Detailed Version]; [Operating System]; [Runtime]; [Runtime Version])\r\n    let userAgent: string = \"Microsoft SignalR/\";\r\n\r\n    const majorAndMinor = version.split(\".\");\r\n    userAgent += `${majorAndMinor[0]}.${majorAndMinor[1]}`;\r\n    userAgent += ` (${version}; `;\r\n\r\n    if (os && os !== \"\") {\r\n        userAgent += `${os}; `;\r\n    } else {\r\n        userAgent += \"Unknown OS; \";\r\n    }\r\n\r\n    userAgent += `${runtime}`;\r\n\r\n    if (runtimeVersion) {\r\n        userAgent += `; ${runtimeVersion}`;\r\n    } else {\r\n        userAgent += \"; Unknown Runtime Version\";\r\n    }\r\n\r\n    userAgent += \")\";\r\n    return userAgent;\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getOsName(): string {\r\n    if (Platform.isNode) {\r\n        switch (process.platform) {\r\n            case \"win32\":\r\n                return \"Windows NT\";\r\n            case \"darwin\":\r\n                return \"macOS\";\r\n            case \"linux\":\r\n                return \"Linux\";\r\n            default:\r\n                return process.platform;\r\n        }\r\n    } else {\r\n        return \"\";\r\n    }\r\n}\r\n\r\n// eslint-disable-next-line spaced-comment\r\n/*#__PURE__*/ function getRuntimeVersion(): string | undefined {\r\n    if (Platform.isNode) {\r\n        return process.versions.node;\r\n    }\r\n    return undefined;\r\n}\r\n\r\nfunction getRuntime(): string {\r\n    if (Platform.isNode) {\r\n        return \"NodeJS\";\r\n    } else {\r\n        return \"Browser\";\r\n    }\r\n}\r\n\r\n/** @private */\r\nexport function getErrorString(e: any): string {\r\n    if (e.stack) {\r\n        return e.stack;\r\n    } else if (e.message) {\r\n        return e.message;\r\n    }\r\n    return `${e}`;\r\n}\r\n\r\n/** @private */\r\nexport function getGlobalThis(): unknown {\r\n    // globalThis is semi-new and not available in Node until v12\r\n    if (typeof globalThis !== \"undefined\") {\r\n        return globalThis;\r\n    }\r\n    if (typeof self !== \"undefined\") {\r\n        return self;\r\n    }\r\n    if (typeof window !== \"undefined\") {\r\n        return window;\r\n    }\r\n    if (typeof global !== \"undefined\") {\r\n        return global;\r\n    }\r\n    throw new Error(\"could not find global\");\r\n}", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// @ts-ignore: This will be removed from built files and is here to make the types available during dev work\r\nimport { CookieJar } from \"@types/tough-cookie\";\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { Platform, getGlobalThis, isArrayBuffer } from \"./Utils\";\r\n\r\nexport class FetchHttpClient extends HttpClient {\r\n    private readonly _abortControllerType: { prototype: AbortController, new(): AbortController };\r\n    private readonly _fetchType: (input: RequestInfo, init?: RequestInit) => Promise<Response>;\r\n    private readonly _jar?: CookieJar;\r\n\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n\r\n        if (typeof fetch === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: T<PERSON> doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Cookies aren't automatically handled in Node so we need to add a CookieJar to preserve cookies across requests\r\n            this._jar = new (requireFunc(\"tough-cookie\")).CookieJar();\r\n            this._fetchType = requireFunc(\"node-fetch\");\r\n\r\n            // node-fetch doesn't have a nice API for getting and setting cookies\r\n            // fetch-cookie will wrap a fetch implementation with a default CookieJar or a provided one\r\n            this._fetchType = requireFunc(\"fetch-cookie\")(this._fetchType, this._jar);\r\n        } else {\r\n            this._fetchType = fetch.bind(getGlobalThis());\r\n        }\r\n        if (typeof AbortController === \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n\r\n            // Node needs EventListener methods on AbortController which our custom polyfill doesn't provide\r\n            this._abortControllerType = requireFunc(\"abort-controller\");\r\n        } else {\r\n            this._abortControllerType = AbortController;\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public async send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            throw new AbortError();\r\n        }\r\n\r\n        if (!request.method) {\r\n            throw new Error(\"No method defined.\");\r\n        }\r\n        if (!request.url) {\r\n            throw new Error(\"No url defined.\");\r\n        }\r\n\r\n        const abortController = new this._abortControllerType();\r\n\r\n        let error: any;\r\n        // Hook our abortSignal into the abort controller\r\n        if (request.abortSignal) {\r\n            request.abortSignal.onabort = () => {\r\n                abortController.abort();\r\n                error = new AbortError();\r\n            };\r\n        }\r\n\r\n        // If a timeout has been passed in, setup a timeout to call abort\r\n        // Type needs to be any to fit window.setTimeout and NodeJS.setTimeout\r\n        let timeoutId: any = null;\r\n        if (request.timeout) {\r\n            const msTimeout = request.timeout!;\r\n            timeoutId = setTimeout(() => {\r\n                abortController.abort();\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                error = new TimeoutError();\r\n            }, msTimeout);\r\n        }\r\n\r\n        if (request.content === \"\") {\r\n            request.content = undefined;\r\n        }\r\n        if (request.content) {\r\n            // Explicitly setting the Content-Type header for React Native on Android platform.\r\n            request.headers = request.headers || {};\r\n            if (isArrayBuffer(request.content)) {\r\n                request.headers[\"Content-Type\"] = \"application/octet-stream\";\r\n            } else {\r\n                request.headers[\"Content-Type\"] = \"text/plain;charset=UTF-8\";\r\n            }\r\n        }\r\n\r\n        let response: Response;\r\n        try {\r\n            response = await this._fetchType(request.url!, {\r\n                body: request.content,\r\n                cache: \"no-cache\",\r\n                credentials: request.withCredentials === true ? \"include\" : \"same-origin\",\r\n                headers: {\r\n                    \"X-Requested-With\": \"XMLHttpRequest\",\r\n                    ...request.headers,\r\n                },\r\n                method: request.method!,\r\n                mode: \"cors\",\r\n                redirect: \"follow\",\r\n                signal: abortController.signal,\r\n            });\r\n        } catch (e) {\r\n            if (error) {\r\n                throw error;\r\n            }\r\n            this._logger.log(\r\n                LogLevel.Warning,\r\n                `Error from HTTP request. ${e}.`,\r\n            );\r\n            throw e;\r\n        } finally {\r\n            if (timeoutId) {\r\n                clearTimeout(timeoutId);\r\n            }\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = null;\r\n            }\r\n        }\r\n\r\n        if (!response.ok) {\r\n            const errorMessage = await deserializeContent(response, \"text\") as string;\r\n            throw new HttpError(errorMessage || response.statusText, response.status);\r\n        }\r\n\r\n        const content = deserializeContent(response, request.responseType);\r\n        const payload = await content;\r\n\r\n        return new HttpResponse(\r\n            response.status,\r\n            response.statusText,\r\n            payload,\r\n        );\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        let cookies: string = \"\";\r\n        if (Platform.isNode && this._jar) {\r\n            // @ts-ignore: unused variable\r\n            this._jar.getCookies(url, (e, c) => cookies = c.join(\"; \"));\r\n        }\r\n        return cookies;\r\n    }\r\n}\r\n\r\nfunction deserializeContent(response: Response, responseType?: XMLHttpRequestResponseType): Promise<string | ArrayBuffer> {\r\n    let content;\r\n    switch (responseType) {\r\n        case \"arraybuffer\":\r\n            content = response.arrayBuffer();\r\n            break;\r\n        case \"text\":\r\n            content = response.text();\r\n            break;\r\n        case \"blob\":\r\n        case \"document\":\r\n        case \"json\":\r\n            throw new Error(`${responseType} is not supported.`);\r\n        default:\r\n            content = response.text();\r\n            break;\r\n    }\r\n\r\n    return content;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError, HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\nexport class XhrHttpClient extends HttpClient {\r\n    private readonly _logger: ILogger;\r\n\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n        this._logger = logger;\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return new Promise<HttpResponse>((resolve, reject) => {\r\n            const xhr = new XMLHttpRequest();\r\n\r\n            xhr.open(request.method!, request.url!, true);\r\n            xhr.withCredentials = request.withCredentials === undefined ? true : request.withCredentials;\r\n            xhr.setRequestHeader(\"X-Requested-With\", \"XMLHttpRequest\");\r\n            if (request.content === \"\") {\r\n                request.content = undefined;\r\n            }\r\n            if (request.content) {\r\n                // Explicitly setting the Content-Type header for React Native on Android platform.\r\n                if (isArrayBuffer(request.content)) {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"application/octet-stream\");\r\n                } else {\r\n                    xhr.setRequestHeader(\"Content-Type\", \"text/plain;charset=UTF-8\");\r\n                }\r\n            }\r\n\r\n            const headers = request.headers;\r\n            if (headers) {\r\n                Object.keys(headers)\r\n                    .forEach((header) => {\r\n                        xhr.setRequestHeader(header, headers[header]);\r\n                    });\r\n            }\r\n\r\n            if (request.responseType) {\r\n                xhr.responseType = request.responseType;\r\n            }\r\n\r\n            if (request.abortSignal) {\r\n                request.abortSignal.onabort = () => {\r\n                    xhr.abort();\r\n                    reject(new AbortError());\r\n                };\r\n            }\r\n\r\n            if (request.timeout) {\r\n                xhr.timeout = request.timeout;\r\n            }\r\n\r\n            xhr.onload = () => {\r\n                if (request.abortSignal) {\r\n                    request.abortSignal.onabort = null;\r\n                }\r\n\r\n                if (xhr.status >= 200 && xhr.status < 300) {\r\n                    resolve(new HttpResponse(xhr.status, xhr.statusText, xhr.response || xhr.responseText));\r\n                } else {\r\n                    reject(new HttpError(xhr.response || xhr.responseText || xhr.statusText, xhr.status));\r\n                }\r\n            };\r\n\r\n            xhr.onerror = () => {\r\n                this._logger.log(LogLevel.Warning, `Error from HTTP request. ${xhr.status}: ${xhr.statusText}.`);\r\n                reject(new HttpError(xhr.statusText, xhr.status));\r\n            };\r\n\r\n            xhr.ontimeout = () => {\r\n                this._logger.log(LogLevel.Warning, `Timeout from HTTP request.`);\r\n                reject(new TimeoutError());\r\n            };\r\n\r\n            xhr.send(request.content);\r\n        });\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortError } from \"./Errors\";\r\nimport { FetchHttpClient } from \"./FetchHttpClient\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\nimport { ILogger } from \"./ILogger\";\r\nimport { Platform } from \"./Utils\";\r\nimport { XhrHttpClient } from \"./XhrHttpClient\";\r\n\r\n/** Default implementation of {@link @microsoft/signalr.HttpClient}. */\r\nexport class DefaultHttpClient extends HttpClient {\r\n    private readonly _httpClient: HttpClient;\r\n\r\n    /** Creates a new instance of the {@link @microsoft/signalr.DefaultHttpClient}, using the provided {@link @microsoft/signalr.ILogger} to log messages. */\r\n    public constructor(logger: ILogger) {\r\n        super();\r\n\r\n        if (typeof fetch !== \"undefined\" || Platform.isNode) {\r\n            this._httpClient = new FetchHttpClient(logger);\r\n        } else if (typeof XMLHttpRequest !== \"undefined\") {\r\n            this._httpClient = new XhrHttpClient(logger);\r\n        } else {\r\n            throw new Error(\"No usable HttpClient found.\");\r\n        }\r\n    }\r\n\r\n    /** @inheritDoc */\r\n    public send(request: HttpRequest): Promise<HttpResponse> {\r\n        // Check that abort was not signaled before calling send\r\n        if (request.abortSignal && request.abortSignal.aborted) {\r\n            return Promise.reject(new AbortError());\r\n        }\r\n\r\n        if (!request.method) {\r\n            return Promise.reject(new Error(\"No method defined.\"));\r\n        }\r\n        if (!request.url) {\r\n            return Promise.reject(new Error(\"No url defined.\"));\r\n        }\r\n\r\n        return this._httpClient.send(request);\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        return this._httpClient.getCookieString(url);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Not exported from index\r\n/** @private */\r\nexport class TextMessageFormat {\r\n    public static RecordSeparatorCode = 0x1e;\r\n    public static RecordSeparator = String.fromCharCode(TextMessageFormat.RecordSeparatorCode);\r\n\r\n    public static write(output: string): string {\r\n        return `${output}${TextMessageFormat.RecordSeparator}`;\r\n    }\r\n\r\n    public static parse(input: string): string[] {\r\n        if (input[input.length - 1] !== TextMessageFormat.RecordSeparator) {\r\n            throw new Error(\"Message is incomplete.\");\r\n        }\r\n\r\n        const messages = input.split(TextMessageFormat.RecordSeparator);\r\n        messages.pop();\r\n        return messages;\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\nimport { isArrayBuffer } from \"./Utils\";\r\n\r\n/** @private */\r\nexport interface HandshakeRequestMessage {\r\n    readonly protocol: string;\r\n    readonly version: number;\r\n}\r\n\r\n/** @private */\r\nexport interface HandshakeResponseMessage {\r\n    readonly error: string;\r\n    readonly minorVersion: number;\r\n}\r\n\r\n/** @private */\r\nexport class HandshakeProtocol {\r\n    // Handshake request is always JSON\r\n    public writeHandshakeRequest(handshakeRequest: HandshakeRequestMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(handshakeRequest));\r\n    }\r\n\r\n    public parseHandshakeResponse(data: any): [any, HandshakeResponseMessage] {\r\n        let messageData: string;\r\n        let remainingData: any;\r\n\r\n        if (isArrayBuffer(data)) {\r\n            // Format is binary but still need to read JSON text from handshake response\r\n            const binaryData = new Uint8Array(data);\r\n            const separatorIndex = binaryData.indexOf(TextMessageFormat.RecordSeparatorCode);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = String.fromCharCode.apply(null, Array.prototype.slice.call(binaryData.slice(0, responseLength)));\r\n            remainingData = (binaryData.byteLength > responseLength) ? binaryData.slice(responseLength).buffer : null;\r\n        } else {\r\n            const textData: string = data;\r\n            const separatorIndex = textData.indexOf(TextMessageFormat.RecordSeparator);\r\n            if (separatorIndex === -1) {\r\n                throw new Error(\"Message is incomplete.\");\r\n            }\r\n\r\n            // content before separator is handshake response\r\n            // optional content after is additional messages\r\n            const responseLength = separatorIndex + 1;\r\n            messageData = textData.substring(0, responseLength);\r\n            remainingData = (textData.length > responseLength) ? textData.substring(responseLength) : null;\r\n        }\r\n\r\n        // At this point we should have just the single handshake message\r\n        const messages = TextMessageFormat.parse(messageData);\r\n        const response = JSON.parse(messages[0]);\r\n        if (response.type) {\r\n            throw new Error(\"Expected a handshake response from the server.\");\r\n        }\r\n        const responseMessage: HandshakeResponseMessage = response;\r\n\r\n        // multiple messages could have arrived with handshake\r\n        // return additional data to be parsed as usual, or null if all parsed\r\n        return [remainingData, responseMessage];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { ILogger } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\n\r\n/** Defines the type of a Hub Message. */\r\nexport enum MessageType {\r\n    /** Indicates the message is an Invocation message and implements the {@link @microsoft/signalr.InvocationMessage} interface. */\r\n    Invocation = 1,\r\n    /** Indicates the message is a StreamItem message and implements the {@link @microsoft/signalr.StreamItemMessage} interface. */\r\n    StreamItem = 2,\r\n    /** Indicates the message is a Completion message and implements the {@link @microsoft/signalr.CompletionMessage} interface. */\r\n    Completion = 3,\r\n    /** Indicates the message is a Stream Invocation message and implements the {@link @microsoft/signalr.StreamInvocationMessage} interface. */\r\n    StreamInvocation = 4,\r\n    /** Indicates the message is a Cancel Invocation message and implements the {@link @microsoft/signalr.CancelInvocationMessage} interface. */\r\n    CancelInvocation = 5,\r\n    /** Indicates the message is a Ping message and implements the {@link @microsoft/signalr.PingMessage} interface. */\r\n    Ping = 6,\r\n    /** Indicates the message is a Close message and implements the {@link @microsoft/signalr.CloseMessage} interface. */\r\n    Close = 7,\r\n}\r\n\r\n/** Defines a dictionary of string keys and string values representing headers attached to a Hub message. */\r\nexport interface MessageHeaders {\r\n    /** Gets or sets the header with the specified key. */\r\n    [key: string]: string;\r\n}\r\n\r\n/** Union type of all known Hub messages. */\r\nexport type HubMessage =\r\n    InvocationMessage |\r\n    StreamInvocationMessage |\r\n    StreamItemMessage |\r\n    CompletionMessage |\r\n    CancelInvocationMessage |\r\n    PingMessage |\r\n    CloseMessage;\r\n\r\n/** Defines properties common to all Hub messages. */\r\nexport interface HubMessageBase {\r\n    /** A {@link @microsoft/signalr.MessageType} value indicating the type of this message. */\r\n    readonly type: MessageType;\r\n}\r\n\r\n/** Defines properties common to all Hub messages relating to a specific invocation. */\r\nexport interface HubInvocationMessage extends HubMessageBase {\r\n    /** A {@link @microsoft/signalr.MessageHeaders} dictionary containing headers attached to the message. */\r\n    readonly headers?: MessageHeaders;\r\n    /** The ID of the invocation relating to this message.\r\n     *\r\n     * This is expected to be present for {@link @microsoft/signalr.StreamInvocationMessage} and {@link @microsoft/signalr.CompletionMessage}. It may\r\n     * be 'undefined' for an {@link @microsoft/signalr.InvocationMessage} if the sender does not expect a response.\r\n     */\r\n    readonly invocationId?: string;\r\n}\r\n\r\n/** A hub message representing a non-streaming invocation. */\r\nexport interface InvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Invocation;\r\n    /** The target method name. */\r\n    readonly target: string;\r\n    /** The target method arguments. */\r\n    readonly arguments: any[];\r\n    /** The target methods stream IDs. */\r\n    readonly streamIds?: string[];\r\n}\r\n\r\n/** A hub message representing a streaming invocation. */\r\nexport interface StreamInvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.StreamInvocation;\r\n\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n    /** The target method name. */\r\n    readonly target: string;\r\n    /** The target method arguments. */\r\n    readonly arguments: any[];\r\n    /** The target methods stream IDs. */\r\n    readonly streamIds?: string[];\r\n}\r\n\r\n/** A hub message representing a single item produced as part of a result stream. */\r\nexport interface StreamItemMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.StreamItem;\r\n\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n\r\n    /** The item produced by the server. */\r\n    readonly item?: any;\r\n}\r\n\r\n/** A hub message representing the result of an invocation. */\r\nexport interface CompletionMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Completion;\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n    /** The error produced by the invocation, if any.\r\n     *\r\n     * Either {@link @microsoft/signalr.CompletionMessage.error} or {@link @microsoft/signalr.CompletionMessage.result} must be defined, but not both.\r\n     */\r\n    readonly error?: string;\r\n    /** The result produced by the invocation, if any.\r\n     *\r\n     * Either {@link @microsoft/signalr.CompletionMessage.error} or {@link @microsoft/signalr.CompletionMessage.result} must be defined, but not both.\r\n     */\r\n    readonly result?: any;\r\n}\r\n\r\n/** A hub message indicating that the sender is still active. */\r\nexport interface PingMessage extends HubMessageBase {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Ping;\r\n}\r\n\r\n/** A hub message indicating that the sender is closing the connection.\r\n *\r\n * If {@link @microsoft/signalr.CloseMessage.error} is defined, the sender is closing the connection due to an error.\r\n */\r\nexport interface CloseMessage extends HubMessageBase {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.Close;\r\n    /** The error that triggered the close, if any.\r\n     *\r\n     * If this property is undefined, the connection was closed normally and without error.\r\n     */\r\n    readonly error?: string;\r\n\r\n    /** If true, clients with automatic reconnects enabled should attempt to reconnect after receiving the CloseMessage. Otherwise, they should not. */\r\n    readonly allowReconnect?: boolean;\r\n}\r\n\r\n/** A hub message sent to request that a streaming invocation be canceled. */\r\nexport interface CancelInvocationMessage extends HubInvocationMessage {\r\n    /** @inheritDoc */\r\n    readonly type: MessageType.CancelInvocation;\r\n    /** The invocation ID. */\r\n    readonly invocationId: string;\r\n}\r\n\r\n/** A protocol abstraction for communicating with SignalR Hubs.  */\r\nexport interface IHubProtocol {\r\n    /** The name of the protocol. This is used by SignalR to resolve the protocol between the client and server. */\r\n    readonly name: string;\r\n    /** The version of the protocol. */\r\n    readonly version: number;\r\n    /** The {@link @microsoft/signalr.TransferFormat} of the protocol. */\r\n    readonly transferFormat: TransferFormat;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * If {@link @microsoft/signalr.IHubProtocol.transferFormat} is 'Text', the `input` parameter must be a string, otherwise it must be an ArrayBuffer.\r\n     *\r\n     * @param {string | ArrayBuffer} input A string or ArrayBuffer containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    parseMessages(input: string | ArrayBuffer, logger: ILogger): HubMessage[];\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string or ArrayBuffer and returns it.\r\n     *\r\n     * If {@link @microsoft/signalr.IHubProtocol.transferFormat} is 'Text', the result of this method will be a string, otherwise it will be an ArrayBuffer.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string | ArrayBuffer} A string or ArrayBuffer containing the serialized representation of the message.\r\n     */\r\n    writeMessage(message: HubMessage): string | ArrayBuffer;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IStreamResult, IStreamSubscriber, ISubscription } from \"./Stream\";\r\nimport { SubjectSubscription } from \"./Utils\";\r\n\r\n/** Stream implementation to stream items to the server. */\r\nexport class Subject<T> implements IStreamResult<T> {\r\n    /** @internal */\r\n    public observers: IStreamSubscriber<T>[];\r\n\r\n    /** @internal */\r\n    public cancelCallback?: () => Promise<void>;\r\n\r\n    constructor() {\r\n        this.observers = [];\r\n    }\r\n\r\n    public next(item: T): void {\r\n        for (const observer of this.observers) {\r\n            observer.next(item);\r\n        }\r\n    }\r\n\r\n    public error(err: any): void {\r\n        for (const observer of this.observers) {\r\n            if (observer.error) {\r\n                observer.error(err);\r\n            }\r\n        }\r\n    }\r\n\r\n    public complete(): void {\r\n        for (const observer of this.observers) {\r\n            if (observer.complete) {\r\n                observer.complete();\r\n            }\r\n        }\r\n    }\r\n\r\n    public subscribe(observer: IStreamSubscriber<T>): ISubscription<T> {\r\n        this.observers.push(observer);\r\n        return new SubjectSubscription(this, observer);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HandshakeProtocol, HandshakeRequestMessage, HandshakeResponseMessage } from \"./HandshakeProtocol\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { AbortError } from \"./Errors\";\r\nimport { CancelInvocationMessage, CompletionMessage, IHubProtocol, InvocationMessage, MessageType, StreamInvocationMessage, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { IStreamResult } from \"./Stream\";\r\nimport { Subject } from \"./Subject\";\r\nimport { Arg, getErrorString, Platform } from \"./Utils\";\r\n\r\nconst DEFAULT_TIMEOUT_IN_MS: number = 30 * 1000;\r\nconst DEFAULT_PING_INTERVAL_IN_MS: number = 15 * 1000;\r\n\r\n/** Describes the current state of the {@link HubConnection} to the server. */\r\nexport enum HubConnectionState {\r\n    /** The hub connection is disconnected. */\r\n    Disconnected = \"Disconnected\",\r\n    /** The hub connection is connecting. */\r\n    Connecting = \"Connecting\",\r\n    /** The hub connection is connected. */\r\n    Connected = \"Connected\",\r\n    /** The hub connection is disconnecting. */\r\n    Disconnecting = \"Disconnecting\",\r\n    /** The hub connection is reconnecting. */\r\n    Reconnecting = \"Reconnecting\",\r\n}\r\n\r\n/** Represents a connection to a SignalR Hub. */\r\nexport class HubConnection {\r\n    private readonly _cachedPingMessage: string | ArrayBuffer;\r\n    // Needs to not start with _ for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private readonly connection: IConnection;\r\n    private readonly _logger: ILogger;\r\n    private readonly _reconnectPolicy?: IRetryPolicy;\r\n    private _protocol: IHubProtocol;\r\n    private _handshakeProtocol: HandshakeProtocol;\r\n    private _callbacks: { [invocationId: string]: (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => void };\r\n    private _methods: { [name: string]: (((...args: any[]) => void) | ((...args: any[]) => any))[] };\r\n    private _invocationId: number;\r\n\r\n    private _closedCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectingCallbacks: ((error?: Error) => void)[];\r\n    private _reconnectedCallbacks: ((connectionId?: string) => void)[];\r\n\r\n    private _receivedHandshakeResponse: boolean;\r\n    private _handshakeResolver!: (value?: PromiseLike<{}>) => void;\r\n    private _handshakeRejecter!: (reason?: any) => void;\r\n    private _stopDuringStartError?: Error;\r\n\r\n    private _connectionState: HubConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private _startPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _nextKeepAlive: number = 0;\r\n\r\n    // The type of these a) doesn't matter and b) varies when building in browser and node contexts\r\n    // Since we're building the WebPack bundle directly from the TypeScript, this matters (previously\r\n    // we built the bundle from the compiled JavaScript).\r\n    private _reconnectDelayHandle?: any;\r\n    private _timeoutHandle?: any;\r\n    private _pingServerHandle?: any;\r\n\r\n    private _freezeEventListener = () =>\r\n    {\r\n        this._logger.log(LogLevel.Warning, \"The page is being frozen, this will likely lead to the connection being closed and messages being lost. For more information see the docs at https://docs.microsoft.com/aspnet/core/signalr/javascript-client#bsleep\");\r\n    };\r\n\r\n    /** The server timeout in milliseconds.\r\n     *\r\n     * If this timeout elapses without receiving any messages from the server, the connection will be terminated with an error.\r\n     * The default timeout value is 30,000 milliseconds (30 seconds).\r\n     */\r\n    public serverTimeoutInMilliseconds: number;\r\n\r\n    /** Default interval at which to ping the server.\r\n     *\r\n     * The default value is 15,000 milliseconds (15 seconds).\r\n     * Allows the server to detect hard disconnects (like when a client unplugs their computer).\r\n     * The ping will happen at most as often as the server pings.\r\n     * If the server pings every 5 seconds, a value lower than 5 will ping every 5 seconds.\r\n     */\r\n    public keepAliveIntervalInMilliseconds: number;\r\n\r\n    /** @internal */\r\n    // Using a public static factory method means we can have a private constructor and an _internal_\r\n    // create method that can be used by HubConnectionBuilder. An \"internal\" constructor would just\r\n    // be stripped away and the '.d.ts' file would have no constructor, which is interpreted as a\r\n    // public parameter-less constructor.\r\n    public static create(connection: IConnection, logger: ILogger, protocol: IHubProtocol, reconnectPolicy?: IRetryPolicy): HubConnection {\r\n        return new HubConnection(connection, logger, protocol, reconnectPolicy);\r\n    }\r\n\r\n    private constructor(connection: IConnection, logger: ILogger, protocol: IHubProtocol, reconnectPolicy?: IRetryPolicy) {\r\n        Arg.isRequired(connection, \"connection\");\r\n        Arg.isRequired(logger, \"logger\");\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.serverTimeoutInMilliseconds = DEFAULT_TIMEOUT_IN_MS;\r\n        this.keepAliveIntervalInMilliseconds = DEFAULT_PING_INTERVAL_IN_MS;\r\n\r\n        this._logger = logger;\r\n        this._protocol = protocol;\r\n        this.connection = connection;\r\n        this._reconnectPolicy = reconnectPolicy;\r\n        this._handshakeProtocol = new HandshakeProtocol();\r\n\r\n        this.connection.onreceive = (data: any) => this._processIncomingData(data);\r\n        this.connection.onclose = (error?: Error) => this._connectionClosed(error);\r\n\r\n        this._callbacks = {};\r\n        this._methods = {};\r\n        this._closedCallbacks = [];\r\n        this._reconnectingCallbacks = [];\r\n        this._reconnectedCallbacks = [];\r\n        this._invocationId = 0;\r\n        this._receivedHandshakeResponse = false;\r\n        this._connectionState = HubConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n\r\n        this._cachedPingMessage = this._protocol.writeMessage({ type: MessageType.Ping });\r\n    }\r\n\r\n    /** Indicates the state of the {@link HubConnection} to the server. */\r\n    get state(): HubConnectionState {\r\n        return this._connectionState;\r\n    }\r\n\r\n    /** Represents the connection id of the {@link HubConnection} on the server. The connection id will be null when the connection is either\r\n     *  in the disconnected state or if the negotiation step was skipped.\r\n     */\r\n    get connectionId(): string | null {\r\n        return this.connection ? (this.connection.connectionId || null) : null;\r\n    }\r\n\r\n    /** Indicates the url of the {@link HubConnection} to the server. */\r\n    get baseUrl(): string {\r\n        return this.connection.baseUrl || \"\";\r\n    }\r\n\r\n    /**\r\n     * Sets a new url for the HubConnection. Note that the url can only be changed when the connection is in either the Disconnected or\r\n     * Reconnecting states.\r\n     * @param {string} url The url to connect to.\r\n     */\r\n    set baseUrl(url: string) {\r\n        if (this._connectionState !== HubConnectionState.Disconnected && this._connectionState !== HubConnectionState.Reconnecting) {\r\n            throw new Error(\"The HubConnection must be in the Disconnected or Reconnecting state to change the url.\");\r\n        }\r\n\r\n        if (!url) {\r\n            throw new Error(\"The HubConnection url must be a valid url.\");\r\n        }\r\n\r\n        this.connection.baseUrl = url;\r\n    }\r\n\r\n    /** Starts the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully established, or rejects with an error.\r\n     */\r\n    public start(): Promise<void> {\r\n        this._startPromise = this._startWithStateTransitions();\r\n        return this._startPromise;\r\n    }\r\n\r\n    private async _startWithStateTransitions(): Promise<void> {\r\n        if (this._connectionState !== HubConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start a HubConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Connecting;\r\n        this._logger.log(LogLevel.Debug, \"Starting HubConnection.\");\r\n\r\n        try {\r\n            await this._startInternal();\r\n\r\n            if (Platform.isBrowser) {\r\n                // Log when the browser freezes the tab so users know why their connection unexpectedly stopped working\r\n                window.document.addEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            this._connectionState = HubConnectionState.Connected;\r\n            this._connectionStarted = true;\r\n            this._logger.log(LogLevel.Debug, \"HubConnection connected successfully.\");\r\n        } catch (e) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._logger.log(LogLevel.Debug, `HubConnection failed to start successfully because of error '${e}'.`);\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _startInternal() {\r\n        this._stopDuringStartError = undefined;\r\n        this._receivedHandshakeResponse = false;\r\n        // Set up the promise before any connection is (re)started otherwise it could race with received messages\r\n        const handshakePromise = new Promise((resolve, reject) => {\r\n            this._handshakeResolver = resolve;\r\n            this._handshakeRejecter = reject;\r\n        });\r\n\r\n        await this.connection.start(this._protocol.transferFormat);\r\n\r\n        try {\r\n            const handshakeRequest: HandshakeRequestMessage = {\r\n                protocol: this._protocol.name,\r\n                version: this._protocol.version,\r\n            };\r\n\r\n            this._logger.log(LogLevel.Debug, \"Sending handshake request.\");\r\n\r\n            await this._sendMessage(this._handshakeProtocol.writeHandshakeRequest(handshakeRequest));\r\n\r\n            this._logger.log(LogLevel.Information, `Using HubProtocol '${this._protocol.name}'.`);\r\n\r\n            // defensively cleanup timeout in case we receive a message from the server before we finish start\r\n            this._cleanupTimeout();\r\n            this._resetTimeoutPeriod();\r\n            this._resetKeepAliveInterval();\r\n\r\n            await handshakePromise;\r\n\r\n            // It's important to check the stopDuringStartError instead of just relying on the handshakePromise\r\n            // being rejected on close, because this continuation can run after both the handshake completed successfully\r\n            // and the connection was closed.\r\n            if (this._stopDuringStartError) {\r\n                // It's important to throw instead of returning a rejected promise, because we don't want to allow any state\r\n                // transitions to occur between now and the calling code observing the exceptions. Returning a rejected promise\r\n                // will cause the calling continuation to get scheduled to run later.\r\n                // eslint-disable-next-line @typescript-eslint/no-throw-literal\r\n                throw this._stopDuringStartError;\r\n            }\r\n\r\n            if (!this.connection.features.inherentKeepAlive) {\r\n                await this._sendMessage(this._cachedPingMessage);\r\n            }\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Debug, `Hub handshake failed with error '${e}' during start(). Stopping HubConnection.`);\r\n\r\n            this._cleanupTimeout();\r\n            this._cleanupPingTimer();\r\n\r\n            // HttpConnection.stop() should not complete until after the onclose callback is invoked.\r\n            // This will transition the HubConnection to the disconnected state before HttpConnection.stop() completes.\r\n            await this.connection.stop(e);\r\n            throw e;\r\n        }\r\n    }\r\n\r\n    /** Stops the connection.\r\n     *\r\n     * @returns {Promise<void>} A Promise that resolves when the connection has been successfully terminated, or rejects with an error.\r\n     */\r\n    public async stop(): Promise<void> {\r\n        // Capture the start promise before the connection might be restarted in an onclose callback.\r\n        const startPromise = this._startPromise;\r\n\r\n        this._stopPromise = this._stopInternal();\r\n        await this._stopPromise;\r\n\r\n        try {\r\n            // Awaiting undefined continues immediately\r\n            await startPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n    }\r\n\r\n    private _stopInternal(error?: Error): Promise<void> {\r\n        if (this._connectionState === HubConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HubConnection.stop(${error}) ignored because it is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise!;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Disconnecting;\r\n\r\n        this._logger.log(LogLevel.Debug, \"Stopping HubConnection.\");\r\n\r\n        if (this._reconnectDelayHandle) {\r\n            // We're in a reconnect delay which means the underlying connection is currently already stopped.\r\n            // Just clear the handle to stop the reconnect loop (which no one is waiting on thankfully) and\r\n            // fire the onclose callbacks.\r\n            this._logger.log(LogLevel.Debug, \"Connection stopped during reconnect delay. Done reconnecting.\");\r\n\r\n            clearTimeout(this._reconnectDelayHandle);\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            this._completeClose();\r\n            return Promise.resolve();\r\n        }\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n        this._stopDuringStartError = error || new AbortError(\"The connection was stopped before the hub handshake could complete.\");\r\n\r\n        // HttpConnection.stop() should not complete until after either HttpConnection.start() fails\r\n        // or the onclose callback is invoked. The onclose callback will transition the HubConnection\r\n        // to the disconnected state if need be before HttpConnection.stop() completes.\r\n        return this.connection.stop(error);\r\n    }\r\n\r\n    /** Invokes a streaming hub method on the server using the specified name and arguments.\r\n     *\r\n     * @typeparam T The type of the items returned by the server.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {IStreamResult<T>} An object that yields results from the server as they are received.\r\n     */\r\n    public stream<T = any>(methodName: string, ...args: any[]): IStreamResult<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createStreamInvocation(methodName, args, streamIds);\r\n\r\n        // eslint-disable-next-line prefer-const\r\n        let promiseQueue: Promise<void>;\r\n\r\n        const subject = new Subject<T>();\r\n        subject.cancelCallback = () => {\r\n            const cancelInvocation: CancelInvocationMessage = this._createCancelInvocation(invocationDescriptor.invocationId);\r\n\r\n            delete this._callbacks[invocationDescriptor.invocationId];\r\n\r\n            return promiseQueue.then(() => {\r\n                return this._sendWithProtocol(cancelInvocation);\r\n            });\r\n        };\r\n\r\n        this._callbacks[invocationDescriptor.invocationId] = (invocationEvent: CompletionMessage | StreamItemMessage | null, error?: Error) => {\r\n            if (error) {\r\n                subject.error(error);\r\n                return;\r\n            } else if (invocationEvent) {\r\n                // invocationEvent will not be null when an error is not passed to the callback\r\n                if (invocationEvent.type === MessageType.Completion) {\r\n                    if (invocationEvent.error) {\r\n                        subject.error(new Error(invocationEvent.error));\r\n                    } else {\r\n                        subject.complete();\r\n                    }\r\n                } else {\r\n                    subject.next((invocationEvent.item) as T);\r\n                }\r\n            }\r\n        };\r\n\r\n        promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n            .catch((e) => {\r\n                subject.error(e);\r\n                delete this._callbacks[invocationDescriptor.invocationId];\r\n            });\r\n\r\n        this._launchStreams(streams, promiseQueue);\r\n\r\n        return subject;\r\n    }\r\n\r\n    private _sendMessage(message: any) {\r\n        this._resetKeepAliveInterval();\r\n        return this.connection.send(message);\r\n    }\r\n\r\n    /**\r\n     * Sends a js object to the server.\r\n     * @param message The js object to serialize and send.\r\n     */\r\n    private _sendWithProtocol(message: any) {\r\n        return this._sendMessage(this._protocol.writeMessage(message));\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments. Does not wait for a response from the receiver.\r\n     *\r\n     * The Promise returned by this method resolves when the client has sent the invocation to the server. The server may still\r\n     * be processing the invocation.\r\n     *\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<void>} A Promise that resolves when the invocation has been successfully sent, or rejects with an error.\r\n     */\r\n    public send(methodName: string, ...args: any[]): Promise<void> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const sendPromise = this._sendWithProtocol(this._createInvocation(methodName, args, true, streamIds));\r\n\r\n        this._launchStreams(streams, sendPromise);\r\n\r\n        return sendPromise;\r\n    }\r\n\r\n    /** Invokes a hub method on the server using the specified name and arguments.\r\n     *\r\n     * The Promise returned by this method resolves when the server indicates it has finished invoking the method. When the promise\r\n     * resolves, the server has finished invoking the method. If the server method returns a result, it is produced as the result of\r\n     * resolving the Promise.\r\n     *\r\n     * @typeparam T The expected return type.\r\n     * @param {string} methodName The name of the server method to invoke.\r\n     * @param {any[]} args The arguments used to invoke the server method.\r\n     * @returns {Promise<T>} A Promise that resolves with the result of the server method (if any), or rejects with an error.\r\n     */\r\n    public invoke<T = any>(methodName: string, ...args: any[]): Promise<T> {\r\n        const [streams, streamIds] = this._replaceStreamingParams(args);\r\n        const invocationDescriptor = this._createInvocation(methodName, args, false, streamIds);\r\n\r\n        const p = new Promise<any>((resolve, reject) => {\r\n            // invocationId will always have a value for a non-blocking invocation\r\n            this._callbacks[invocationDescriptor.invocationId!] = (invocationEvent: StreamItemMessage | CompletionMessage | null, error?: Error) => {\r\n                if (error) {\r\n                    reject(error);\r\n                    return;\r\n                } else if (invocationEvent) {\r\n                    // invocationEvent will not be null when an error is not passed to the callback\r\n                    if (invocationEvent.type === MessageType.Completion) {\r\n                        if (invocationEvent.error) {\r\n                            reject(new Error(invocationEvent.error));\r\n                        } else {\r\n                            resolve(invocationEvent.result);\r\n                        }\r\n                    } else {\r\n                        reject(new Error(`Unexpected message type: ${invocationEvent.type}`));\r\n                    }\r\n                }\r\n            };\r\n\r\n            const promiseQueue = this._sendWithProtocol(invocationDescriptor)\r\n                .catch((e) => {\r\n                    reject(e);\r\n                    // invocationId will always have a value for a non-blocking invocation\r\n                    delete this._callbacks[invocationDescriptor.invocationId!];\r\n                });\r\n\r\n            this._launchStreams(streams, promiseQueue);\r\n        });\r\n\r\n        return p;\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the hub method with the specified method name is invoked.\r\n     *\r\n     * @param {string} methodName The name of the hub method to define.\r\n     * @param {Function} newMethod The handler that will be raised when the hub method is invoked.\r\n     */\r\n    public on(methodName: string, newMethod: (...args: any[]) => any): void\r\n    public on(methodName: string, newMethod: (...args: any[]) => void): void {\r\n        if (!methodName || !newMethod) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        if (!this._methods[methodName]) {\r\n            this._methods[methodName] = [];\r\n        }\r\n\r\n        // Preventing adding the same handler multiple times.\r\n        if (this._methods[methodName].indexOf(newMethod) !== -1) {\r\n            return;\r\n        }\r\n\r\n        this._methods[methodName].push(newMethod);\r\n    }\r\n\r\n    /** Removes all handlers for the specified hub method.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     */\r\n    public off(methodName: string): void;\r\n\r\n    /** Removes the specified handler for the specified hub method.\r\n     *\r\n     * You must pass the exact same Function instance as was previously passed to {@link @microsoft/signalr.HubConnection.on}. Passing a different instance (even if the function\r\n     * body is the same) will not remove the handler.\r\n     *\r\n     * @param {string} methodName The name of the method to remove handlers for.\r\n     * @param {Function} method The handler to remove. This must be the same Function instance as the one passed to {@link @microsoft/signalr.HubConnection.on}.\r\n     */\r\n    public off(methodName: string, method: (...args: any[]) => void): void;\r\n    public off(methodName: string, method?: (...args: any[]) => void): void {\r\n        if (!methodName) {\r\n            return;\r\n        }\r\n\r\n        methodName = methodName.toLowerCase();\r\n        const handlers = this._methods[methodName];\r\n        if (!handlers) {\r\n            return;\r\n        }\r\n        if (method) {\r\n            const removeIdx = handlers.indexOf(method);\r\n            if (removeIdx !== -1) {\r\n                handlers.splice(removeIdx, 1);\r\n                if (handlers.length === 0) {\r\n                    delete this._methods[methodName];\r\n                }\r\n            }\r\n        } else {\r\n            delete this._methods[methodName];\r\n        }\r\n\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection is closed.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection is closed. Optionally receives a single argument containing the error that caused the connection to close (if any).\r\n     */\r\n    public onclose(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._closedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection starts reconnecting.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection starts reconnecting. Optionally receives a single argument containing the error that caused the connection to start reconnecting (if any).\r\n     */\r\n    public onreconnecting(callback: (error?: Error) => void): void {\r\n        if (callback) {\r\n            this._reconnectingCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    /** Registers a handler that will be invoked when the connection successfully reconnects.\r\n     *\r\n     * @param {Function} callback The handler that will be invoked when the connection successfully reconnects.\r\n     */\r\n    public onreconnected(callback: (connectionId?: string) => void): void {\r\n        if (callback) {\r\n            this._reconnectedCallbacks.push(callback);\r\n        }\r\n    }\r\n\r\n    private _processIncomingData(data: any) {\r\n        this._cleanupTimeout();\r\n\r\n        if (!this._receivedHandshakeResponse) {\r\n            data = this._processHandshakeResponse(data);\r\n            this._receivedHandshakeResponse = true;\r\n        }\r\n\r\n        // Data may have all been read when processing handshake response\r\n        if (data) {\r\n            // Parse the messages\r\n            const messages = this._protocol.parseMessages(data, this._logger);\r\n\r\n            for (const message of messages) {\r\n                switch (message.type) {\r\n                    case MessageType.Invocation:\r\n                        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                        this._invokeClientMethod(message);\r\n                        break;\r\n                    case MessageType.StreamItem:\r\n                    case MessageType.Completion: {\r\n                        const callback = this._callbacks[message.invocationId];\r\n                        if (callback) {\r\n                            if (message.type === MessageType.Completion) {\r\n                                delete this._callbacks[message.invocationId];\r\n                            }\r\n                            try {\r\n                                callback(message);\r\n                            } catch (e) {\r\n                                this._logger.log(LogLevel.Error, `Stream callback threw error: ${getErrorString(e)}`);\r\n                            }\r\n                        }\r\n                        break;\r\n                    }\r\n                    case MessageType.Ping:\r\n                        // Don't care about pings\r\n                        break;\r\n                    case MessageType.Close: {\r\n                        this._logger.log(LogLevel.Information, \"Close message received from server.\");\r\n\r\n                        const error = message.error ? new Error(\"Server returned an error on close: \" + message.error) : undefined;\r\n\r\n                        if (message.allowReconnect === true) {\r\n                            // It feels wrong not to await connection.stop() here, but processIncomingData is called as part of an onreceive callback which is not async,\r\n                            // this is already the behavior for serverTimeout(), and HttpConnection.Stop() should catch and log all possible exceptions.\r\n\r\n                            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                            this.connection.stop(error);\r\n                        } else {\r\n                            // We cannot await stopInternal() here, but subsequent calls to stop() will await this if stopInternal() is still ongoing.\r\n                            this._stopPromise = this._stopInternal(error);\r\n                        }\r\n\r\n                        break;\r\n                    }\r\n                    default:\r\n                        this._logger.log(LogLevel.Warning, `Invalid message type: ${message.type}.`);\r\n                        break;\r\n                }\r\n            }\r\n        }\r\n\r\n        this._resetTimeoutPeriod();\r\n    }\r\n\r\n    private _processHandshakeResponse(data: any): any {\r\n        let responseMessage: HandshakeResponseMessage;\r\n        let remainingData: any;\r\n\r\n        try {\r\n            [remainingData, responseMessage] = this._handshakeProtocol.parseHandshakeResponse(data);\r\n        } catch (e) {\r\n            const message = \"Error parsing handshake response: \" + e;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        }\r\n        if (responseMessage.error) {\r\n            const message = \"Server returned handshake error: \" + responseMessage.error;\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            const error = new Error(message);\r\n            this._handshakeRejecter(error);\r\n            throw error;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"Server handshake complete.\");\r\n        }\r\n\r\n        this._handshakeResolver();\r\n        return remainingData;\r\n    }\r\n\r\n    private _resetKeepAliveInterval() {\r\n        if (this.connection.features.inherentKeepAlive) {\r\n            return;\r\n        }\r\n\r\n        // Set the time we want the next keep alive to be sent\r\n        // Timer will be setup on next message receive\r\n        this._nextKeepAlive = new Date().getTime() + this.keepAliveIntervalInMilliseconds;\r\n\r\n        this._cleanupPingTimer();\r\n    }\r\n\r\n    private _resetTimeoutPeriod() {\r\n        if (!this.connection.features || !this.connection.features.inherentKeepAlive) {\r\n            // Set the timeout timer\r\n            this._timeoutHandle = setTimeout(() => this.serverTimeout(), this.serverTimeoutInMilliseconds);\r\n\r\n            // Set keepAlive timer if there isn't one\r\n            if (this._pingServerHandle === undefined)\r\n            {\r\n                let nextPing = this._nextKeepAlive - new Date().getTime();\r\n                if (nextPing < 0) {\r\n                    nextPing = 0;\r\n                }\r\n\r\n                // The timer needs to be set from a networking callback to avoid Chrome timer throttling from causing timers to run once a minute\r\n                this._pingServerHandle = setTimeout(async () => {\r\n                    if (this._connectionState === HubConnectionState.Connected) {\r\n                        try {\r\n                            await this._sendMessage(this._cachedPingMessage);\r\n                        } catch {\r\n                            // We don't care about the error. It should be seen elsewhere in the client.\r\n                            // The connection is probably in a bad or closed state now, cleanup the timer so it stops triggering\r\n                            this._cleanupPingTimer();\r\n                        }\r\n                    }\r\n                }, nextPing);\r\n            }\r\n        }\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private serverTimeout() {\r\n        // The server hasn't talked to us in a while. It doesn't like us anymore ... :(\r\n        // Terminate the connection, but we don't need to wait on the promise. This could trigger reconnecting.\r\n        // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n        this.connection.stop(new Error(\"Server timeout elapsed without receiving a message from the server.\"));\r\n    }\r\n\r\n    private async _invokeClientMethod(invocationMessage: InvocationMessage) {\r\n        const methodName = invocationMessage.target.toLowerCase();\r\n        const methods = this._methods[methodName];\r\n        if (!methods) {\r\n            this._logger.log(LogLevel.Warning, `No client method with the name '${methodName}' found.`);\r\n\r\n            // No handlers provided by client but the server is expecting a response still, so we send an error\r\n            if (invocationMessage.invocationId) {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                await this._sendWithProtocol(this._createCompletionMessage(invocationMessage.invocationId, \"Client didn't provide a result.\", null));\r\n            }\r\n            return;\r\n        }\r\n\r\n        // Avoid issues with handlers removing themselves thus modifying the list while iterating through it\r\n        const methodsCopy = methods.slice();\r\n\r\n        // Server expects a response\r\n        const expectsResponse = invocationMessage.invocationId ? true : false;\r\n        // We preserve the last result or exception but still call all handlers\r\n        let res;\r\n        let exception;\r\n        let completionMessage;\r\n        for (const m of methodsCopy) {\r\n            try {\r\n                const prevRes = res;\r\n                res = await m.apply(this, invocationMessage.arguments);\r\n                if (expectsResponse && res && prevRes) {\r\n                    this._logger.log(LogLevel.Error, `Multiple results provided for '${methodName}'. Sending error to server.`);\r\n                    completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, `Client provided multiple results.`, null);\r\n                }\r\n                // Ignore exception if we got a result after, the exception will be logged\r\n                exception = undefined;\r\n            } catch (e) {\r\n                exception = e;\r\n                this._logger.log(LogLevel.Error, `A callback for the method '${methodName}' threw error '${e}'.`);\r\n            }\r\n        }\r\n        if (completionMessage) {\r\n            await this._sendWithProtocol(completionMessage);\r\n        } else if (expectsResponse) {\r\n            // If there is an exception that means either no result was given or a handler after a result threw\r\n            if (exception) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, `${exception}`, null);\r\n            } else if (res !== undefined) {\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, null, res);\r\n            } else {\r\n                this._logger.log(LogLevel.Warning, `No result given for '${methodName}' method and invocation ID '${invocationMessage.invocationId}'.`);\r\n                // Client didn't provide a result or throw from a handler, server expects a response so we send an error\r\n                completionMessage = this._createCompletionMessage(invocationMessage.invocationId!, \"Client didn't provide a result.\", null);\r\n            }\r\n            await this._sendWithProtocol(completionMessage);\r\n        } else {\r\n            if (res) {\r\n                this._logger.log(LogLevel.Error, `Result given for '${methodName}' method but server is not expecting a result.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _connectionClosed(error?: Error) {\r\n        this._logger.log(LogLevel.Debug, `HubConnection.connectionClosed(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        // Triggering this.handshakeRejecter is insufficient because it could already be resolved without the continuation having run yet.\r\n        this._stopDuringStartError = this._stopDuringStartError || error || new AbortError(\"The underlying connection was closed before the hub handshake could complete.\");\r\n\r\n        // If the handshake is in progress, start will be waiting for the handshake promise, so we complete it.\r\n        // If it has already completed, this should just noop.\r\n        if (this._handshakeResolver) {\r\n            this._handshakeResolver();\r\n        }\r\n\r\n        this._cancelCallbacksWithError(error || new Error(\"Invocation canceled due to the underlying connection being closed.\"));\r\n\r\n        this._cleanupTimeout();\r\n        this._cleanupPingTimer();\r\n\r\n        if (this._connectionState === HubConnectionState.Disconnecting) {\r\n            this._completeClose(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected && this._reconnectPolicy) {\r\n            // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n            this._reconnect(error);\r\n        } else if (this._connectionState === HubConnectionState.Connected) {\r\n            this._completeClose(error);\r\n        }\r\n\r\n        // If none of the above if conditions were true were called the HubConnection must be in either:\r\n        // 1. The Connecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail it.\r\n        // 2. The Reconnecting state in which case the handshakeResolver will complete it and stopDuringStartError will fail the current reconnect attempt\r\n        //    and potentially continue the reconnect() loop.\r\n        // 3. The Disconnected state in which case we're already done.\r\n    }\r\n\r\n    private _completeClose(error?: Error) {\r\n        if (this._connectionStarted) {\r\n            this._connectionState = HubConnectionState.Disconnected;\r\n            this._connectionStarted = false;\r\n\r\n            if (Platform.isBrowser) {\r\n                window.document.removeEventListener(\"freeze\", this._freezeEventListener);\r\n            }\r\n\r\n            try {\r\n                this._closedCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onclose callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private async _reconnect(error?: Error) {\r\n        const reconnectStartTime = Date.now();\r\n        let previousReconnectAttempts = 0;\r\n        let retryError = error !== undefined ? error : new Error(\"Attempting to reconnect due to a unknown error.\");\r\n\r\n        let nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, 0, retryError);\r\n\r\n        if (nextRetryDelay === null) {\r\n            this._logger.log(LogLevel.Debug, \"Connection not reconnecting because the IRetryPolicy returned null on the first reconnect attempt.\");\r\n            this._completeClose(error);\r\n            return;\r\n        }\r\n\r\n        this._connectionState = HubConnectionState.Reconnecting;\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Information, `Connection reconnecting because of error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection reconnecting.\");\r\n        }\r\n\r\n        if (this._reconnectingCallbacks.length !== 0) {\r\n            try {\r\n                this._reconnectingCallbacks.forEach((c) => c.apply(this, [error]));\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `An onreconnecting callback called with error '${error}' threw error '${e}'.`);\r\n            }\r\n\r\n            // Exit early if an onreconnecting callback called connection.stop().\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state in onreconnecting callback. Done reconnecting.\");\r\n                return;\r\n            }\r\n        }\r\n\r\n        while (nextRetryDelay !== null) {\r\n            this._logger.log(LogLevel.Information, `Reconnect attempt number ${previousReconnectAttempts} will start in ${nextRetryDelay} ms.`);\r\n\r\n            await new Promise((resolve) => {\r\n                this._reconnectDelayHandle = setTimeout(resolve, nextRetryDelay!);\r\n            });\r\n            this._reconnectDelayHandle = undefined;\r\n\r\n            if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                this._logger.log(LogLevel.Debug, \"Connection left the reconnecting state during reconnect delay. Done reconnecting.\");\r\n                return;\r\n            }\r\n\r\n            try {\r\n                await this._startInternal();\r\n\r\n                this._connectionState = HubConnectionState.Connected;\r\n                this._logger.log(LogLevel.Information, \"HubConnection reconnected successfully.\");\r\n\r\n                if (this._reconnectedCallbacks.length !== 0) {\r\n                    try {\r\n                        this._reconnectedCallbacks.forEach((c) => c.apply(this, [this.connection.connectionId]));\r\n                    } catch (e) {\r\n                        this._logger.log(LogLevel.Error, `An onreconnected callback called with connectionId '${this.connection.connectionId}; threw error '${e}'.`);\r\n                    }\r\n                }\r\n\r\n                return;\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Information, `Reconnect attempt failed because of error '${e}'.`);\r\n\r\n                if (this._connectionState !== HubConnectionState.Reconnecting) {\r\n                    this._logger.log(LogLevel.Debug, `Connection moved to the '${this._connectionState}' from the reconnecting state during reconnect attempt. Done reconnecting.`);\r\n                    // The TypeScript compiler thinks that connectionState must be Connected here. The TypeScript compiler is wrong.\r\n                    if (this._connectionState as any === HubConnectionState.Disconnecting) {\r\n                        this._completeClose();\r\n                    }\r\n                    return;\r\n                }\r\n\r\n                retryError = e instanceof Error ? e : new Error(e.toString());\r\n                nextRetryDelay = this._getNextRetryDelay(previousReconnectAttempts++, Date.now() - reconnectStartTime, retryError);\r\n            }\r\n        }\r\n\r\n        this._logger.log(LogLevel.Information, `Reconnect retries have been exhausted after ${Date.now() - reconnectStartTime} ms and ${previousReconnectAttempts} failed attempts. Connection disconnecting.`);\r\n\r\n        this._completeClose();\r\n    }\r\n\r\n    private _getNextRetryDelay(previousRetryCount: number, elapsedMilliseconds: number, retryReason: Error) {\r\n        try {\r\n            return this._reconnectPolicy!.nextRetryDelayInMilliseconds({\r\n                elapsedMilliseconds,\r\n                previousRetryCount,\r\n                retryReason,\r\n            });\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, `IRetryPolicy.nextRetryDelayInMilliseconds(${previousRetryCount}, ${elapsedMilliseconds}) threw error '${e}'.`);\r\n            return null;\r\n        }\r\n    }\r\n\r\n    private _cancelCallbacksWithError(error: Error) {\r\n        const callbacks = this._callbacks;\r\n        this._callbacks = {};\r\n\r\n        Object.keys(callbacks)\r\n            .forEach((key) => {\r\n                const callback = callbacks[key];\r\n                try {\r\n                    callback(null, error);\r\n                } catch (e) {\r\n                    this._logger.log(LogLevel.Error, `Stream 'error' callback called with '${error}' threw error: ${getErrorString(e)}`);\r\n                }\r\n            });\r\n    }\r\n\r\n    private _cleanupPingTimer(): void {\r\n        if (this._pingServerHandle) {\r\n            clearTimeout(this._pingServerHandle);\r\n            this._pingServerHandle = undefined;\r\n        }\r\n    }\r\n\r\n    private _cleanupTimeout(): void {\r\n        if (this._timeoutHandle) {\r\n            clearTimeout(this._timeoutHandle);\r\n        }\r\n    }\r\n\r\n    private _createInvocation(methodName: string, args: any[], nonblocking: boolean, streamIds: string[]): InvocationMessage {\r\n        if (nonblocking) {\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        } else {\r\n            const invocationId = this._invocationId;\r\n            this._invocationId++;\r\n\r\n            if (streamIds.length !== 0) {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    streamIds,\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            } else {\r\n                return {\r\n                    arguments: args,\r\n                    invocationId: invocationId.toString(),\r\n                    target: methodName,\r\n                    type: MessageType.Invocation,\r\n                };\r\n            }\r\n        }\r\n    }\r\n\r\n    private _launchStreams(streams: IStreamResult<any>[], promiseQueue: Promise<void>): void {\r\n        if (streams.length === 0) {\r\n            return;\r\n        }\r\n\r\n        // Synchronize stream data so they arrive in-order on the server\r\n        if (!promiseQueue) {\r\n            promiseQueue = Promise.resolve();\r\n        }\r\n\r\n        // We want to iterate over the keys, since the keys are the stream ids\r\n        // eslint-disable-next-line guard-for-in\r\n        for (const streamId in streams) {\r\n            streams[streamId].subscribe({\r\n                complete: () => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId)));\r\n                },\r\n                error: (err) => {\r\n                    let message: string;\r\n                    if (err instanceof Error) {\r\n                        message = err.message;\r\n                    } else if (err && err.toString) {\r\n                        message = err.toString();\r\n                    } else {\r\n                        message = \"Unknown error\";\r\n                    }\r\n\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createCompletionMessage(streamId, message)));\r\n                },\r\n                next: (item) => {\r\n                    promiseQueue = promiseQueue.then(() => this._sendWithProtocol(this._createStreamItemMessage(streamId, item)));\r\n                },\r\n            });\r\n        }\r\n    }\r\n\r\n    private _replaceStreamingParams(args: any[]): [IStreamResult<any>[], string[]] {\r\n        const streams: IStreamResult<any>[] = [];\r\n        const streamIds: string[] = [];\r\n        for (let i = 0; i < args.length; i++) {\r\n            const argument = args[i];\r\n            if (this._isObservable(argument)) {\r\n                const streamId = this._invocationId;\r\n                this._invocationId++;\r\n                // Store the stream for later use\r\n                streams[streamId] = argument;\r\n                streamIds.push(streamId.toString());\r\n\r\n                // remove stream from args\r\n                args.splice(i, 1);\r\n            }\r\n        }\r\n\r\n        return [streams, streamIds];\r\n    }\r\n\r\n    private _isObservable(arg: any): arg is IStreamResult<any> {\r\n        // This allows other stream implementations to just work (like rxjs)\r\n        return arg && arg.subscribe && typeof arg.subscribe === \"function\";\r\n    }\r\n\r\n    private _createStreamInvocation(methodName: string, args: any[], streamIds: string[]): StreamInvocationMessage {\r\n        const invocationId = this._invocationId;\r\n        this._invocationId++;\r\n\r\n        if (streamIds.length !== 0) {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                streamIds,\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        } else {\r\n            return {\r\n                arguments: args,\r\n                invocationId: invocationId.toString(),\r\n                target: methodName,\r\n                type: MessageType.StreamInvocation,\r\n            };\r\n        }\r\n    }\r\n\r\n    private _createCancelInvocation(id: string): CancelInvocationMessage {\r\n        return {\r\n            invocationId: id,\r\n            type: MessageType.CancelInvocation,\r\n        };\r\n    }\r\n\r\n    private _createStreamItemMessage(id: string, item: any): StreamItemMessage {\r\n        return {\r\n            invocationId: id,\r\n            item,\r\n            type: MessageType.StreamItem,\r\n        };\r\n    }\r\n\r\n    private _createCompletionMessage(id: string, error?: any, result?: any): CompletionMessage {\r\n        if (error) {\r\n            return {\r\n                error,\r\n                invocationId: id,\r\n                type: MessageType.Completion,\r\n            };\r\n        }\r\n\r\n        return {\r\n            invocationId: id,\r\n            result,\r\n            type: MessageType.Completion,\r\n        };\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { IRetryPolicy, RetryContext } from \"./IRetryPolicy\";\r\n\r\n// 0, 2, 10, 30 second delays before reconnect attempts.\r\nconst DEFAULT_RETRY_DELAYS_IN_MILLISECONDS = [0, 2000, 10000, 30000, null];\r\n\r\n/** @private */\r\nexport class DefaultReconnectPolicy implements IRetryPolicy {\r\n    private readonly _retryDelays: (number | null)[];\r\n\r\n    constructor(retryDelays?: number[]) {\r\n        this._retryDelays = retryDelays !== undefined ? [...retryDelays, null] : DEFAULT_RETRY_DELAYS_IN_MILLISECONDS;\r\n    }\r\n\r\n    public nextRetryDelayInMilliseconds(retryContext: RetryContext): number | null {\r\n        return this._retryDelays[retryContext.previousRetryCount];\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nexport abstract class HeaderNames {\r\n    static readonly Authorization = \"Authorization\";\r\n    static readonly Cookie = \"<PERSON>ie\";\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient, HttpRequest, HttpResponse } from \"./HttpClient\";\r\n\r\n/** @private */\r\nexport class AccessTokenHttpClient extends HttpClient {\r\n    private _innerClient: HttpClient;\r\n    _accessToken: string | undefined;\r\n    _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n\r\n    constructor(innerClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined) {\r\n        super();\r\n\r\n        this._innerClient = innerClient;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n    }\r\n\r\n    public async send(request: HttpRequest): Promise<HttpResponse> {\r\n        let allowRetry = true;\r\n        if (this._accessTokenFactory && (!this._accessToken || (request.url && request.url.indexOf(\"/negotiate?\") > 0))) {\r\n            // don't retry if the request is a negotiate or if we just got a potentially new token from the access token factory\r\n            allowRetry = false;\r\n            this._accessToken = await this._accessTokenFactory();\r\n        }\r\n        this._setAuthorizationHeader(request);\r\n        const response = await this._innerClient.send(request);\r\n\r\n        if (allowRetry && response.statusCode === 401 && this._accessTokenFactory) {\r\n            this._accessToken = await this._accessTokenFactory();\r\n            this._setAuthorizationHeader(request);\r\n            return await this._innerClient.send(request);\r\n        }\r\n        return response;\r\n    }\r\n\r\n    private _setAuthorizationHeader(request: HttpRequest) {\r\n        if (!request.headers) {\r\n            request.headers = {};\r\n        }\r\n        if (this._accessToken) {\r\n            request.headers[HeaderNames.Authorization] = `Bearer ${this._accessToken}`\r\n        }\r\n        // don't remove the header if there isn't an access token factory, the user manually added the header in this case\r\n        else if (this._accessTokenFactory) {\r\n            if (request.headers[HeaderNames.Authorization]) {\r\n                delete request.headers[HeaderNames.Authorization];\r\n            }\r\n        }\r\n    }\r\n\r\n    public getCookieString(url: string): string {\r\n        return this._innerClient.getCookieString(url);\r\n    }\r\n}", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// This will be treated as a bit flag in the future, so we keep it using power-of-two values.\r\n/** Specifies a specific HTTP transport type. */\r\nexport enum HttpTransportType {\r\n    /** Specifies no transport preference. */\r\n    None = 0,\r\n    /** Specifies the WebSockets transport. */\r\n    WebSockets = 1,\r\n    /** Specifies the Server-Sent Events transport. */\r\n    ServerSentEvents = 2,\r\n    /** Specifies the Long Polling transport. */\r\n    LongPolling = 4,\r\n}\r\n\r\n/** Specifies the transfer format for a connection. */\r\nexport enum TransferFormat {\r\n    /** Specifies that only text data will be transmitted over the connection. */\r\n    Text = 1,\r\n    /** Specifies that binary data will be transmitted over the connection. */\r\n    Binary = 2,\r\n}\r\n\r\n/** An abstraction over the behavior of transports. This is designed to support the framework and not intended for use by applications. */\r\nexport interface ITransport {\r\n    connect(url: string, transferFormat: TransferFormat): Promise<void>;\r\n    send(data: any): Promise<void>;\r\n    stop(): Promise<void>;\r\n    onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    onclose: ((error?: Error) => void) | null;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\n// Rough polyfill of https://developer.mozilla.org/en-US/docs/Web/API/AbortController\r\n// We don't actually ever use the API being polyfilled, we always use the polyfill because\r\n// it's a very new API right now.\r\n\r\n// Not exported from index.\r\n/** @private */\r\nexport class AbortController implements AbortSignal {\r\n    private _isAborted: boolean = false;\r\n    public onabort: (() => void) | null = null;\r\n\r\n    public abort(): void {\r\n        if (!this._isAborted) {\r\n            this._isAborted = true;\r\n            if (this.onabort) {\r\n                this.onabort();\r\n            }\r\n        }\r\n    }\r\n\r\n    get signal(): AbortSignal {\r\n        return this;\r\n    }\r\n\r\n    get aborted(): boolean {\r\n        return this._isAborted;\r\n    }\r\n}\r\n\r\n/** Represents a signal that can be monitored to determine if a request has been aborted. */\r\nexport interface AbortSignal {\r\n    /** Indicates if the request has been aborted. */\r\n    aborted: boolean;\r\n    /** Set this to a handler that will be invoked when the request is aborted. */\r\n    onabort: (() => void) | null;\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AbortController } from \"./AbortController\";\r\nimport { HttpError, TimeoutError } from \"./Errors\";\r\nimport { HttpClient, HttpRequest } from \"./HttpClient\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n// Not exported from 'index', this type is internal.\r\n/** @private */\r\nexport class LongPollingTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private readonly _pollAbort: AbortController;\r\n\r\n    private _url?: string;\r\n    private _running: boolean;\r\n    private _receiving?: Promise<void>;\r\n    private _closeError?: Error;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    // This is an internal type, not exported from 'index' so this is really just internal.\r\n    public get pollAborted(): boolean {\r\n        return this._pollAbort.aborted;\r\n    }\r\n\r\n    constructor(httpClient: HttpClient, logger: ILogger, options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._logger = logger;\r\n        this._pollAbort = new AbortController();\r\n        this._options = options;\r\n\r\n        this._running = false;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._url = url;\r\n\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Connecting.\");\r\n\r\n        // Allow binary format on Node and Browsers that support binary content (indicated by the presence of responseType property)\r\n        if (transferFormat === TransferFormat.Binary &&\r\n            (typeof XMLHttpRequest !== \"undefined\" && typeof new XMLHttpRequest().responseType !== \"string\")) {\r\n            throw new Error(\"Binary protocols over XmlHttpRequest not implementing advanced features are not supported.\");\r\n        }\r\n\r\n        const [name, value] = getUserAgentHeader();\r\n        const headers = { [name]: value, ...this._options.headers };\r\n\r\n        const pollOptions: HttpRequest = {\r\n            abortSignal: this._pollAbort.signal,\r\n            headers,\r\n            timeout: 100000,\r\n            withCredentials: this._options.withCredentials,\r\n        };\r\n\r\n        if (transferFormat === TransferFormat.Binary) {\r\n            pollOptions.responseType = \"arraybuffer\";\r\n        }\r\n\r\n        // Make initial long polling request\r\n        // Server uses first long polling request to finish initializing connection and it returns without data\r\n        const pollUrl = `${url}&_=${Date.now()}`;\r\n        this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n        const response = await this._httpClient.get(pollUrl, pollOptions);\r\n        if (response.statusCode !== 200) {\r\n            this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n            // Mark running as false so that the poll immediately ends and runs the close logic\r\n            this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n            this._running = false;\r\n        } else {\r\n            this._running = true;\r\n        }\r\n\r\n        this._receiving = this._poll(this._url, pollOptions);\r\n    }\r\n\r\n    private async _poll(url: string, pollOptions: HttpRequest): Promise<void> {\r\n        try {\r\n            while (this._running) {\r\n                try {\r\n                    const pollUrl = `${url}&_=${Date.now()}`;\r\n                    this._logger.log(LogLevel.Trace, `(LongPolling transport) polling: ${pollUrl}.`);\r\n                    const response = await this._httpClient.get(pollUrl, pollOptions);\r\n\r\n                    if (response.statusCode === 204) {\r\n                        this._logger.log(LogLevel.Information, \"(LongPolling transport) Poll terminated by server.\");\r\n\r\n                        this._running = false;\r\n                    } else if (response.statusCode !== 200) {\r\n                        this._logger.log(LogLevel.Error, `(LongPolling transport) Unexpected response code: ${response.statusCode}.`);\r\n\r\n                        // Unexpected status code\r\n                        this._closeError = new HttpError(response.statusText || \"\", response.statusCode);\r\n                        this._running = false;\r\n                    } else {\r\n                        // Process the response\r\n                        if (response.content) {\r\n                            this._logger.log(LogLevel.Trace, `(LongPolling transport) data received. ${getDataDetail(response.content, this._options.logMessageContent!)}.`);\r\n                            if (this.onreceive) {\r\n                                this.onreceive(response.content);\r\n                            }\r\n                        } else {\r\n                            // This is another way timeout manifest.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        }\r\n                    }\r\n                } catch (e) {\r\n                    if (!this._running) {\r\n                        // Log but disregard errors that occur after stopping\r\n                        this._logger.log(LogLevel.Trace, `(LongPolling transport) Poll errored after shutdown: ${e.message}`);\r\n                    } else {\r\n                        if (e instanceof TimeoutError) {\r\n                            // Ignore timeouts and reissue the poll.\r\n                            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Poll timed out, reissuing.\");\r\n                        } else {\r\n                            // Close the connection with the error as the result.\r\n                            this._closeError = e;\r\n                            this._running = false;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Polling complete.\");\r\n\r\n            // We will reach here with pollAborted==false when the server returned a response causing the transport to stop.\r\n            // If pollAborted==true then client initiated the stop and the stop method will raise the close event after DELETE is sent.\r\n            if (!this.pollAborted) {\r\n                this._raiseOnClose();\r\n            }\r\n        }\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._running) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"LongPolling\", this._httpClient, this._url!, data, this._options);\r\n    }\r\n\r\n    public async stop(): Promise<void> {\r\n        this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stopping polling.\");\r\n\r\n        // Tell receiving loop to stop, abort any current request, and then wait for it to finish\r\n        this._running = false;\r\n        this._pollAbort.abort();\r\n\r\n        try {\r\n            await this._receiving;\r\n\r\n            // Send DELETE to clean up long polling on the server\r\n            this._logger.log(LogLevel.Trace, `(LongPolling transport) sending DELETE request to ${this._url}.`);\r\n\r\n            const headers: {[k: string]: string} = {};\r\n            const [name, value] = getUserAgentHeader();\r\n            headers[name] = value;\r\n\r\n            const deleteOptions: HttpRequest = {\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            };\r\n            await this._httpClient.delete(this._url!, deleteOptions);\r\n\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) DELETE request sent.\");\r\n        } finally {\r\n            this._logger.log(LogLevel.Trace, \"(LongPolling transport) Stop finished.\");\r\n\r\n            // Raise close event here instead of in polling\r\n            // It needs to happen after the DELETE request is sent\r\n            this._raiseOnClose();\r\n        }\r\n    }\r\n\r\n    private _raiseOnClose() {\r\n        if (this.onclose) {\r\n            let logMessage = \"(LongPolling transport) Firing onclose event.\";\r\n            if (this._closeError) {\r\n                logMessage += \" Error: \" + this._closeError;\r\n            }\r\n            this._logger.log(LogLevel.Trace, logMessage);\r\n            this.onclose(this._closeError);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform, sendMessage } from \"./Utils\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\n\r\n/** @private */\r\nexport class ServerSentEventsTransport implements ITransport {\r\n    private readonly _httpClient: HttpClient;\r\n    private readonly _accessToken: string | undefined;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    private _eventSource?: EventSource;\r\n    private _url?: string;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessToken: string | undefined, logger: ILogger,\r\n                options: IHttpConnectionOptions) {\r\n        this._httpClient = httpClient;\r\n        this._accessToken = accessToken;\r\n        this._logger = logger;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Trace, \"(SSE transport) Connecting.\");\r\n\r\n        // set url before accessTokenFactory because this._url is only for send and we set the auth header instead of the query string for send\r\n        this._url = url;\r\n\r\n        if (this._accessToken) {\r\n            url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(this._accessToken)}`;\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            let opened = false;\r\n            if (transferFormat !== TransferFormat.Text) {\r\n                reject(new Error(\"The Server-Sent Events transport only supports the 'Text' transfer format\"));\r\n                return;\r\n            }\r\n\r\n            let eventSource: EventSource;\r\n            if (Platform.isBrowser || Platform.isWebWorker) {\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials });\r\n            } else {\r\n                // Non-browser passes cookies via the dictionary\r\n                const cookies = this._httpClient.getCookieString(url);\r\n                const headers: MessageHeaders = {};\r\n                headers.Cookie = cookies;\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n\r\n                eventSource = new this._options.EventSource!(url, { withCredentials: this._options.withCredentials, headers: { ...headers, ...this._options.headers} } as EventSourceInit);\r\n            }\r\n\r\n            try {\r\n                eventSource.onmessage = (e: MessageEvent) => {\r\n                    if (this.onreceive) {\r\n                        try {\r\n                            this._logger.log(LogLevel.Trace, `(SSE transport) data received. ${getDataDetail(e.data, this._options.logMessageContent!)}.`);\r\n                            this.onreceive(e.data);\r\n                        } catch (error) {\r\n                            this._close(error);\r\n                            return;\r\n                        }\r\n                    }\r\n                };\r\n\r\n                // @ts-ignore: not using event on purpose\r\n                eventSource.onerror = (e: Event) => {\r\n                    // EventSource doesn't give any useful information about server side closes.\r\n                    if (opened) {\r\n                        this._close();\r\n                    } else {\r\n                        reject(new Error(\"EventSource failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the connection ID is not present on the server, or a proxy is refusing/buffering the connection.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\"));\r\n                    }\r\n                };\r\n\r\n                eventSource.onopen = () => {\r\n                    this._logger.log(LogLevel.Information, `SSE connected to ${this._url}`);\r\n                    this._eventSource = eventSource;\r\n                    opened = true;\r\n                    resolve();\r\n                };\r\n            } catch (e) {\r\n                reject(e);\r\n                return;\r\n            }\r\n        });\r\n    }\r\n\r\n    public async send(data: any): Promise<void> {\r\n        if (!this._eventSource) {\r\n            return Promise.reject(new Error(\"Cannot send until the transport is connected\"));\r\n        }\r\n        return sendMessage(this._logger, \"SSE\", this._httpClient, this._url!, data, this._options);\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._close();\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(e?: Error) {\r\n        if (this._eventSource) {\r\n            this._eventSource.close();\r\n            this._eventSource = undefined;\r\n\r\n            if (this.onclose) {\r\n                this.onclose(e);\r\n            }\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { HeaderNames } from \"./HeaderNames\";\r\nimport { HttpClient } from \"./HttpClient\";\r\nimport { MessageHeaders } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { ITransport, TransferFormat } from \"./ITransport\";\r\nimport { WebSocketConstructor } from \"./Polyfills\";\r\nimport { Arg, getDataDetail, getUserAgentHeader, Platform } from \"./Utils\";\r\n\r\n/** @private */\r\nexport class WebSocketTransport implements ITransport {\r\n    private readonly _logger: ILogger;\r\n    private readonly _accessTokenFactory: (() => string | Promise<string>) | undefined;\r\n    private readonly _logMessageContent: boolean;\r\n    private readonly _webSocketConstructor: WebSocketConstructor;\r\n    private readonly _httpClient: HttpClient;\r\n    private _webSocket?: WebSocket;\r\n    private _headers: MessageHeaders;\r\n\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((error?: Error) => void) | null;\r\n\r\n    constructor(httpClient: HttpClient, accessTokenFactory: (() => string | Promise<string>) | undefined, logger: ILogger,\r\n                logMessageContent: boolean, webSocketConstructor: WebSocketConstructor, headers: MessageHeaders) {\r\n        this._logger = logger;\r\n        this._accessTokenFactory = accessTokenFactory;\r\n        this._logMessageContent = logMessageContent;\r\n        this._webSocketConstructor = webSocketConstructor;\r\n        this._httpClient = httpClient;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n        this._headers = headers;\r\n    }\r\n\r\n    public async connect(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isRequired(transferFormat, \"transferFormat\");\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) Connecting.\");\r\n\r\n        let token: string;\r\n        if (this._accessTokenFactory) {\r\n            token = await this._accessTokenFactory();\r\n        }\r\n\r\n        return new Promise<void>((resolve, reject) => {\r\n            url = url.replace(/^http/, \"ws\");\r\n            let webSocket: WebSocket | undefined;\r\n            const cookies = this._httpClient.getCookieString(url);\r\n            let opened = false;\r\n\r\n            if (Platform.isNode || Platform.isReactNative) {\r\n                const headers: {[k: string]: string} = {};\r\n                const [name, value] = getUserAgentHeader();\r\n                headers[name] = value;\r\n                if (token) {\r\n                    headers[HeaderNames.Authorization] = `Bearer ${token}`;\r\n                }\r\n\r\n                if (cookies) {\r\n                    headers[HeaderNames.Cookie] = cookies;\r\n                }\r\n\r\n                // Only pass headers when in non-browser environments\r\n                webSocket = new this._webSocketConstructor(url, undefined, {\r\n                    headers: { ...headers, ...this._headers },\r\n                });\r\n            }\r\n            else\r\n            {\r\n                if (token) {\r\n                    url += (url.indexOf(\"?\") < 0 ? \"?\" : \"&\") + `access_token=${encodeURIComponent(token)}`;\r\n                }\r\n            }\r\n\r\n            if (!webSocket) {\r\n                // Chrome is not happy with passing 'undefined' as protocol\r\n                webSocket = new this._webSocketConstructor(url);\r\n            }\r\n\r\n            if (transferFormat === TransferFormat.Binary) {\r\n                webSocket.binaryType = \"arraybuffer\";\r\n            }\r\n\r\n            webSocket.onopen = (_event: Event) => {\r\n                this._logger.log(LogLevel.Information, `WebSocket connected to ${url}.`);\r\n                this._webSocket = webSocket;\r\n                opened = true;\r\n                resolve();\r\n            };\r\n\r\n            webSocket.onerror = (event: Event) => {\r\n                let error: any = null;\r\n                // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                    error = event.error;\r\n                } else {\r\n                    error = \"There was an error with the transport\";\r\n                }\r\n\r\n                this._logger.log(LogLevel.Information, `(WebSockets transport) ${error}.`);\r\n            };\r\n\r\n            webSocket.onmessage = (message: MessageEvent) => {\r\n                this._logger.log(LogLevel.Trace, `(WebSockets transport) data received. ${getDataDetail(message.data, this._logMessageContent)}.`);\r\n                if (this.onreceive) {\r\n                    try {\r\n                        this.onreceive(message.data);\r\n                    } catch (error) {\r\n                        this._close(error);\r\n                        return;\r\n                    }\r\n                }\r\n            };\r\n\r\n            webSocket.onclose = (event: CloseEvent) => {\r\n                // Don't call close handler if connection was never established\r\n                // We'll reject the connect call instead\r\n                if (opened) {\r\n                    this._close(event);\r\n                } else {\r\n                    let error: any = null;\r\n                    // ErrorEvent is a browser only type we need to check if the type exists before using it\r\n                    if (typeof ErrorEvent !== \"undefined\" && event instanceof ErrorEvent) {\r\n                        error = event.error;\r\n                    } else {\r\n                        error = \"WebSocket failed to connect. The connection could not be found on the server,\"\r\n                        + \" either the endpoint may not be a SignalR endpoint,\"\r\n                        + \" the connection ID is not present on the server, or there is a proxy blocking WebSockets.\"\r\n                        + \" If you have multiple servers check that sticky sessions are enabled.\";\r\n                    }\r\n\r\n                    reject(new Error(error));\r\n                }\r\n            };\r\n        });\r\n    }\r\n\r\n    public send(data: any): Promise<void> {\r\n        if (this._webSocket && this._webSocket.readyState === this._webSocketConstructor.OPEN) {\r\n            this._logger.log(LogLevel.Trace, `(WebSockets transport) sending data. ${getDataDetail(data, this._logMessageContent)}.`);\r\n            this._webSocket.send(data);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        return Promise.reject(\"WebSocket is not in the OPEN state\");\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        if (this._webSocket) {\r\n            // Manually invoke onclose callback inline so we know the HttpConnection was closed properly before returning\r\n            // This also solves an issue where websocket.onclose could take 18+ seconds to trigger during network disconnects\r\n            this._close(undefined);\r\n        }\r\n\r\n        return Promise.resolve();\r\n    }\r\n\r\n    private _close(event?: CloseEvent | Error): void {\r\n        // webSocket will be null if the transport did not start successfully\r\n        if (this._webSocket) {\r\n            // Clear websocket handlers because we are considering the socket closed now\r\n            this._webSocket.onclose = () => {};\r\n            this._webSocket.onmessage = () => {};\r\n            this._webSocket.onerror = () => {};\r\n            this._webSocket.close();\r\n            this._webSocket = undefined;\r\n        }\r\n\r\n        this._logger.log(LogLevel.Trace, \"(WebSockets transport) socket closed.\");\r\n        if (this.onclose) {\r\n            if (this._isCloseEvent(event) && (event.wasClean === false || event.code !== 1000)) {\r\n                this.onclose(new Error(`WebSocket closed with status code: ${event.code} (${event.reason || \"no reason given\"}).`));\r\n            } else if (event instanceof Error) {\r\n                this.onclose(event);\r\n            } else {\r\n                this.onclose();\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isCloseEvent(event?: any): event is CloseEvent {\r\n        return event && typeof event.wasClean === \"boolean\" && typeof event.code === \"number\";\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { AccessTokenHttpClient } from \"./AccessTokenHttpClient\";\r\nimport { DefaultHttpClient } from \"./DefaultHttpClient\";\r\nimport { AggregateErrors, DisabledTransportError, FailedToNegotiateWithServerError, FailedToStartTransportError, HttpError, UnsupportedTransportError, AbortError } from \"./Errors\";\r\nimport { IConnection } from \"./IConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { HttpTransportType, ITransport, TransferFormat } from \"./ITransport\";\r\nimport { LongPollingTransport } from \"./LongPollingTransport\";\r\nimport { ServerSentEventsTransport } from \"./ServerSentEventsTransport\";\r\nimport { Arg, createLogger, getUserAgentHeader, Platform } from \"./Utils\";\r\nimport { WebSocketTransport } from \"./WebSocketTransport\";\r\n\r\n/** @private */\r\nconst enum ConnectionState {\r\n    Connecting = \"Connecting\",\r\n    Connected = \"Connected\",\r\n    Disconnected = \"Disconnected\",\r\n    Disconnecting = \"Disconnecting\",\r\n}\r\n\r\n/** @private */\r\nexport interface INegotiateResponse {\r\n    connectionId?: string;\r\n    connectionToken?: string;\r\n    negotiateVersion?: number;\r\n    availableTransports?: IAvailableTransport[];\r\n    url?: string;\r\n    accessToken?: string;\r\n    error?: string;\r\n}\r\n\r\n/** @private */\r\nexport interface IAvailableTransport {\r\n    transport: keyof typeof HttpTransportType;\r\n    transferFormats: (keyof typeof TransferFormat)[];\r\n}\r\n\r\nconst MAX_REDIRECTS = 100;\r\n\r\n/** @private */\r\nexport class HttpConnection implements IConnection {\r\n    private _connectionState: ConnectionState;\r\n    // connectionStarted is tracked independently from connectionState, so we can check if the\r\n    // connection ever did successfully transition from connecting to connected before disconnecting.\r\n    private _connectionStarted: boolean;\r\n    private readonly _httpClient: AccessTokenHttpClient;\r\n    private readonly _logger: ILogger;\r\n    private readonly _options: IHttpConnectionOptions;\r\n    // Needs to not start with _ to be available for tests\r\n    // eslint-disable-next-line @typescript-eslint/naming-convention\r\n    private transport?: ITransport;\r\n    private _startInternalPromise?: Promise<void>;\r\n    private _stopPromise?: Promise<void>;\r\n    private _stopPromiseResolver: (value?: PromiseLike<void>) => void = () => {};\r\n    private _stopError?: Error;\r\n    private _accessTokenFactory?: () => string | Promise<string>;\r\n    private _sendQueue?: TransportSendQueue;\r\n\r\n    public readonly features: any = {};\r\n    public baseUrl: string;\r\n    public connectionId?: string;\r\n    public onreceive: ((data: string | ArrayBuffer) => void) | null;\r\n    public onclose: ((e?: Error) => void) | null;\r\n\r\n    private readonly _negotiateVersion: number = 1;\r\n\r\n    constructor(url: string, options: IHttpConnectionOptions = {}) {\r\n        Arg.isRequired(url, \"url\");\r\n\r\n        this._logger = createLogger(options.logger);\r\n        this.baseUrl = this._resolveUrl(url);\r\n\r\n        options = options || {};\r\n        options.logMessageContent = options.logMessageContent === undefined ? false : options.logMessageContent;\r\n        if (typeof options.withCredentials === \"boolean\" || options.withCredentials === undefined) {\r\n            options.withCredentials = options.withCredentials === undefined ? true : options.withCredentials;\r\n        } else {\r\n            throw new Error(\"withCredentials option was not a 'boolean' or 'undefined' value\");\r\n        }\r\n        options.timeout = options.timeout === undefined ? 100 * 1000 : options.timeout;\r\n\r\n        let webSocketModule: any = null;\r\n        let eventSourceModule: any = null;\r\n\r\n        if (Platform.isNode && typeof require !== \"undefined\") {\r\n            // In order to ignore the dynamic require in webpack builds we need to do this magic\r\n            // @ts-ignore: TS doesn't know about these names\r\n            const requireFunc = typeof __webpack_require__ === \"function\" ? __non_webpack_require__ : require;\r\n            webSocketModule = requireFunc(\"ws\");\r\n            eventSourceModule = requireFunc(\"eventsource\");\r\n        }\r\n\r\n        if (!Platform.isNode && typeof WebSocket !== \"undefined\" && !options.WebSocket) {\r\n            options.WebSocket = WebSocket;\r\n        } else if (Platform.isNode && !options.WebSocket) {\r\n            if (webSocketModule) {\r\n                options.WebSocket = webSocketModule;\r\n            }\r\n        }\r\n\r\n        if (!Platform.isNode && typeof EventSource !== \"undefined\" && !options.EventSource) {\r\n            options.EventSource = EventSource;\r\n        } else if (Platform.isNode && !options.EventSource) {\r\n            if (typeof eventSourceModule !== \"undefined\") {\r\n                options.EventSource = eventSourceModule;\r\n            }\r\n        }\r\n\r\n        this._httpClient = new AccessTokenHttpClient(options.httpClient || new DefaultHttpClient(this._logger), options.accessTokenFactory);\r\n        this._connectionState = ConnectionState.Disconnected;\r\n        this._connectionStarted = false;\r\n        this._options = options;\r\n\r\n        this.onreceive = null;\r\n        this.onclose = null;\r\n    }\r\n\r\n    public start(): Promise<void>;\r\n    public start(transferFormat: TransferFormat): Promise<void>;\r\n    public async start(transferFormat?: TransferFormat): Promise<void> {\r\n        transferFormat = transferFormat || TransferFormat.Binary;\r\n\r\n        Arg.isIn(transferFormat, TransferFormat, \"transferFormat\");\r\n\r\n        this._logger.log(LogLevel.Debug, `Starting connection with transfer format '${TransferFormat[transferFormat]}'.`);\r\n\r\n        if (this._connectionState !== ConnectionState.Disconnected) {\r\n            return Promise.reject(new Error(\"Cannot start an HttpConnection that is not in the 'Disconnected' state.\"));\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Connecting;\r\n\r\n        this._startInternalPromise = this._startInternal(transferFormat);\r\n        await this._startInternalPromise;\r\n\r\n        // The TypeScript compiler thinks that connectionState must be Connecting here. The TypeScript compiler is wrong.\r\n        if (this._connectionState as any === ConnectionState.Disconnecting) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"Failed to start the HttpConnection before stop() was called.\";\r\n            this._logger.log(LogLevel.Error, message);\r\n\r\n            // We cannot await stopPromise inside startInternal since stopInternal awaits the startInternalPromise.\r\n            await this._stopPromise;\r\n\r\n            return Promise.reject(new AbortError(message));\r\n        } else if (this._connectionState as any !== ConnectionState.Connected) {\r\n            // stop() was called and transitioned the client into the Disconnecting state.\r\n            const message = \"HttpConnection.startInternal completed gracefully but didn't enter the connection into the connected state!\";\r\n            this._logger.log(LogLevel.Error, message);\r\n            return Promise.reject(new AbortError(message));\r\n        }\r\n\r\n        this._connectionStarted = true;\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        if (this._connectionState !== ConnectionState.Connected) {\r\n            return Promise.reject(new Error(\"Cannot send data if the connection is not in the 'Connected' State.\"));\r\n        }\r\n\r\n        if (!this._sendQueue) {\r\n            this._sendQueue = new TransportSendQueue(this.transport!);\r\n        }\r\n\r\n        // Transport will not be null if state is connected\r\n        return this._sendQueue.send(data);\r\n    }\r\n\r\n    public async stop(error?: Error): Promise<void> {\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnected state.`);\r\n            return Promise.resolve();\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stop(${error}) ignored because the connection is already in the disconnecting state.`);\r\n            return this._stopPromise;\r\n        }\r\n\r\n        this._connectionState = ConnectionState.Disconnecting;\r\n\r\n        this._stopPromise = new Promise((resolve) => {\r\n            // Don't complete stop() until stopConnection() completes.\r\n            this._stopPromiseResolver = resolve;\r\n        });\r\n\r\n        // stopInternal should never throw so just observe it.\r\n        await this._stopInternal(error);\r\n        await this._stopPromise;\r\n    }\r\n\r\n    private async _stopInternal(error?: Error): Promise<void> {\r\n        // Set error as soon as possible otherwise there is a race between\r\n        // the transport closing and providing an error and the error from a close message\r\n        // We would prefer the close message error.\r\n        this._stopError = error;\r\n\r\n        try {\r\n            await this._startInternalPromise;\r\n        } catch (e) {\r\n            // This exception is returned to the user as a rejected Promise from the start method.\r\n        }\r\n\r\n        // The transport's onclose will trigger stopConnection which will run our onclose event.\r\n        // The transport should always be set if currently connected. If it wasn't set, it's likely because\r\n        // stop was called during start() and start() failed.\r\n        if (this.transport) {\r\n            try {\r\n                await this.transport.stop();\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.transport.stop() threw error '${e}'.`);\r\n                this._stopConnection();\r\n            }\r\n\r\n            this.transport = undefined;\r\n        } else {\r\n            this._logger.log(LogLevel.Debug, \"HttpConnection.transport is undefined in HttpConnection.stop() because start() failed.\");\r\n        }\r\n    }\r\n\r\n    private async _startInternal(transferFormat: TransferFormat): Promise<void> {\r\n        // Store the original base url and the access token factory since they may change\r\n        // as part of negotiating\r\n        let url = this.baseUrl;\r\n        this._accessTokenFactory = this._options.accessTokenFactory;\r\n        this._httpClient._accessTokenFactory = this._accessTokenFactory;\r\n\r\n        try {\r\n            if (this._options.skipNegotiation) {\r\n                if (this._options.transport === HttpTransportType.WebSockets) {\r\n                    // No need to add a connection ID in this case\r\n                    this.transport = this._constructTransport(HttpTransportType.WebSockets);\r\n                    // We should just call connect directly in this case.\r\n                    // No fallback or negotiate in this case.\r\n                    await this._startTransport(url, transferFormat);\r\n                } else {\r\n                    throw new Error(\"Negotiation can only be skipped when using the WebSocket transport directly.\");\r\n                }\r\n            } else {\r\n                let negotiateResponse: INegotiateResponse | null = null;\r\n                let redirects = 0;\r\n\r\n                do {\r\n                    negotiateResponse = await this._getNegotiationResponse(url);\r\n                    // the user tries to stop the connection when it is being started\r\n                    if (this._connectionState === ConnectionState.Disconnecting || this._connectionState === ConnectionState.Disconnected) {\r\n                        throw new AbortError(\"The connection was stopped during negotiation.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.error) {\r\n                        throw new Error(negotiateResponse.error);\r\n                    }\r\n\r\n                    if ((negotiateResponse as any).ProtocolVersion) {\r\n                        throw new Error(\"Detected a connection attempt to an ASP.NET SignalR Server. This client only supports connecting to an ASP.NET Core SignalR Server. See https://aka.ms/signalr-core-differences for details.\");\r\n                    }\r\n\r\n                    if (negotiateResponse.url) {\r\n                        url = negotiateResponse.url;\r\n                    }\r\n\r\n                    if (negotiateResponse.accessToken) {\r\n                        // Replace the current access token factory with one that uses\r\n                        // the returned access token\r\n                        const accessToken = negotiateResponse.accessToken;\r\n                        this._accessTokenFactory = () => accessToken;\r\n                        // set the factory to undefined so the AccessTokenHttpClient won't retry with the same token, since we know it won't change until a connection restart\r\n                        this._httpClient._accessToken = accessToken;\r\n                        this._httpClient._accessTokenFactory = undefined;\r\n                    }\r\n\r\n                    redirects++;\r\n                }\r\n                while (negotiateResponse.url && redirects < MAX_REDIRECTS);\r\n\r\n                if (redirects === MAX_REDIRECTS && negotiateResponse.url) {\r\n                    throw new Error(\"Negotiate redirection limit exceeded.\");\r\n                }\r\n\r\n                await this._createTransport(url, this._options.transport, negotiateResponse, transferFormat);\r\n            }\r\n\r\n            if (this.transport instanceof LongPollingTransport) {\r\n                this.features.inherentKeepAlive = true;\r\n            }\r\n\r\n            if (this._connectionState === ConnectionState.Connecting) {\r\n                // Ensure the connection transitions to the connected state prior to completing this.startInternalPromise.\r\n                // start() will handle the case when stop was called and startInternal exits still in the disconnecting state.\r\n                this._logger.log(LogLevel.Debug, \"The HttpConnection connected successfully.\");\r\n                this._connectionState = ConnectionState.Connected;\r\n            }\r\n\r\n            // stop() is waiting on us via this.startInternalPromise so keep this.transport around so it can clean up.\r\n            // This is the only case startInternal can exit in neither the connected nor disconnected state because stopConnection()\r\n            // will transition to the disconnected state. start() will wait for the transition using the stopPromise.\r\n        } catch (e) {\r\n            this._logger.log(LogLevel.Error, \"Failed to start the connection: \" + e);\r\n            this._connectionState = ConnectionState.Disconnected;\r\n            this.transport = undefined;\r\n\r\n            // if start fails, any active calls to stop assume that start will complete the stop promise\r\n            this._stopPromiseResolver();\r\n            return Promise.reject(e);\r\n        }\r\n    }\r\n\r\n    private async _getNegotiationResponse(url: string): Promise<INegotiateResponse> {\r\n        const headers: {[k: string]: string} = {};\r\n        const [name, value] = getUserAgentHeader();\r\n        headers[name] = value;\r\n\r\n        const negotiateUrl = this._resolveNegotiateUrl(url);\r\n        this._logger.log(LogLevel.Debug, `Sending negotiation request: ${negotiateUrl}.`);\r\n        try {\r\n            const response = await this._httpClient.post(negotiateUrl, {\r\n                content: \"\",\r\n                headers: { ...headers, ...this._options.headers },\r\n                timeout: this._options.timeout,\r\n                withCredentials: this._options.withCredentials,\r\n            });\r\n\r\n            if (response.statusCode !== 200) {\r\n                return Promise.reject(new Error(`Unexpected status code returned from negotiate '${response.statusCode}'`));\r\n            }\r\n\r\n            const negotiateResponse = JSON.parse(response.content as string) as INegotiateResponse;\r\n            if (!negotiateResponse.negotiateVersion || negotiateResponse.negotiateVersion < 1) {\r\n                // Negotiate version 0 doesn't use connectionToken\r\n                // So we set it equal to connectionId so all our logic can use connectionToken without being aware of the negotiate version\r\n                negotiateResponse.connectionToken = negotiateResponse.connectionId;\r\n            }\r\n            return negotiateResponse;\r\n        } catch (e) {\r\n            let errorMessage = \"Failed to complete negotiation with the server: \" + e;\r\n            if (e instanceof HttpError) {\r\n                if (e.statusCode === 404) {\r\n                    errorMessage = errorMessage + \" Either this is not a SignalR endpoint or there is a proxy blocking the connection.\";\r\n                }\r\n            }\r\n            this._logger.log(LogLevel.Error, errorMessage);\r\n\r\n            return Promise.reject(new FailedToNegotiateWithServerError(errorMessage));\r\n        }\r\n    }\r\n\r\n    private _createConnectUrl(url: string, connectionToken: string | null | undefined) {\r\n        if (!connectionToken) {\r\n            return url;\r\n        }\r\n\r\n        return url + (url.indexOf(\"?\") === -1 ? \"?\" : \"&\") + `id=${connectionToken}`;\r\n    }\r\n\r\n    private async _createTransport(url: string, requestedTransport: HttpTransportType | ITransport | undefined, negotiateResponse: INegotiateResponse, requestedTransferFormat: TransferFormat): Promise<void> {\r\n        let connectUrl = this._createConnectUrl(url, negotiateResponse.connectionToken);\r\n        if (this._isITransport(requestedTransport)) {\r\n            this._logger.log(LogLevel.Debug, \"Connection was provided an instance of ITransport, using that directly.\");\r\n            this.transport = requestedTransport;\r\n            await this._startTransport(connectUrl, requestedTransferFormat);\r\n\r\n            this.connectionId = negotiateResponse.connectionId;\r\n            return;\r\n        }\r\n\r\n        const transportExceptions: any[] = [];\r\n        const transports = negotiateResponse.availableTransports || [];\r\n        let negotiate: INegotiateResponse | undefined = negotiateResponse;\r\n        for (const endpoint of transports) {\r\n            const transportOrError = this._resolveTransportOrError(endpoint, requestedTransport, requestedTransferFormat);\r\n            if (transportOrError instanceof Error) {\r\n                // Store the error and continue, we don't want to cause a re-negotiate in these cases\r\n                transportExceptions.push(`${endpoint.transport} failed:`);\r\n                transportExceptions.push(transportOrError);\r\n            } else if (this._isITransport(transportOrError)) {\r\n                this.transport = transportOrError;\r\n                if (!negotiate) {\r\n                    try {\r\n                        negotiate = await this._getNegotiationResponse(url);\r\n                    } catch (ex) {\r\n                        return Promise.reject(ex);\r\n                    }\r\n                    connectUrl = this._createConnectUrl(url, negotiate.connectionToken);\r\n                }\r\n                try {\r\n                    await this._startTransport(connectUrl, requestedTransferFormat);\r\n                    this.connectionId = negotiate.connectionId;\r\n                    return;\r\n                } catch (ex) {\r\n                    this._logger.log(LogLevel.Error, `Failed to start the transport '${endpoint.transport}': ${ex}`);\r\n                    negotiate = undefined;\r\n                    transportExceptions.push(new FailedToStartTransportError(`${endpoint.transport} failed: ${ex}`, HttpTransportType[endpoint.transport]));\r\n\r\n                    if (this._connectionState !== ConnectionState.Connecting) {\r\n                        const message = \"Failed to select transport before stop() was called.\";\r\n                        this._logger.log(LogLevel.Debug, message);\r\n                        return Promise.reject(new AbortError(message));\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        if (transportExceptions.length > 0) {\r\n            return Promise.reject(new AggregateErrors(`Unable to connect to the server with any of the available transports. ${transportExceptions.join(\" \")}`, transportExceptions));\r\n        }\r\n        return Promise.reject(new Error(\"None of the transports supported by the client are supported by the server.\"));\r\n    }\r\n\r\n    private _constructTransport(transport: HttpTransportType): ITransport {\r\n        switch (transport) {\r\n            case HttpTransportType.WebSockets:\r\n                if (!this._options.WebSocket) {\r\n                    throw new Error(\"'WebSocket' is not supported in your environment.\");\r\n                }\r\n                return new WebSocketTransport(this._httpClient, this._accessTokenFactory, this._logger, this._options.logMessageContent!, this._options.WebSocket, this._options.headers || {});\r\n            case HttpTransportType.ServerSentEvents:\r\n                if (!this._options.EventSource) {\r\n                    throw new Error(\"'EventSource' is not supported in your environment.\");\r\n                }\r\n                return new ServerSentEventsTransport(this._httpClient, this._httpClient._accessToken, this._logger, this._options);\r\n            case HttpTransportType.LongPolling:\r\n                return new LongPollingTransport(this._httpClient, this._logger, this._options);\r\n            default:\r\n                throw new Error(`Unknown transport: ${transport}.`);\r\n        }\r\n    }\r\n\r\n    private _startTransport(url: string, transferFormat: TransferFormat): Promise<void> {\r\n        this.transport!.onreceive = this.onreceive;\r\n        this.transport!.onclose = (e) => this._stopConnection(e);\r\n        return this.transport!.connect(url, transferFormat);\r\n    }\r\n\r\n    private _resolveTransportOrError(endpoint: IAvailableTransport, requestedTransport: HttpTransportType | undefined, requestedTransferFormat: TransferFormat): ITransport | Error {\r\n        const transport = HttpTransportType[endpoint.transport];\r\n        if (transport === null || transport === undefined) {\r\n            this._logger.log(LogLevel.Debug, `Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n            return new Error(`Skipping transport '${endpoint.transport}' because it is not supported by this client.`);\r\n        } else {\r\n            if (transportMatches(requestedTransport, transport)) {\r\n                const transferFormats = endpoint.transferFormats.map((s) => TransferFormat[s]);\r\n                if (transferFormats.indexOf(requestedTransferFormat) >= 0) {\r\n                    if ((transport === HttpTransportType.WebSockets && !this._options.WebSocket) ||\r\n                        (transport === HttpTransportType.ServerSentEvents && !this._options.EventSource)) {\r\n                        this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it is not supported in your environment.'`);\r\n                        return new UnsupportedTransportError(`'${HttpTransportType[transport]}' is not supported in your environment.`, transport);\r\n                    } else {\r\n                        this._logger.log(LogLevel.Debug, `Selecting transport '${HttpTransportType[transport]}'.`);\r\n                        try {\r\n                            return this._constructTransport(transport);\r\n                        } catch (ex) {\r\n                            return ex;\r\n                        }\r\n                    }\r\n                } else {\r\n                    this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it does not support the requested transfer format '${TransferFormat[requestedTransferFormat]}'.`);\r\n                    return new Error(`'${HttpTransportType[transport]}' does not support ${TransferFormat[requestedTransferFormat]}.`);\r\n                }\r\n            } else {\r\n                this._logger.log(LogLevel.Debug, `Skipping transport '${HttpTransportType[transport]}' because it was disabled by the client.`);\r\n                return new DisabledTransportError(`'${HttpTransportType[transport]}' is disabled by the client.`, transport);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _isITransport(transport: any): transport is ITransport {\r\n        return transport && typeof (transport) === \"object\" && \"connect\" in transport;\r\n    }\r\n\r\n    private _stopConnection(error?: Error): void {\r\n        this._logger.log(LogLevel.Debug, `HttpConnection.stopConnection(${error}) called while in state ${this._connectionState}.`);\r\n\r\n        this.transport = undefined;\r\n\r\n        // If we have a stopError, it takes precedence over the error from the transport\r\n        error = this._stopError || error;\r\n        this._stopError = undefined;\r\n\r\n        if (this._connectionState === ConnectionState.Disconnected) {\r\n            this._logger.log(LogLevel.Debug, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is already in the disconnected state.`);\r\n            return;\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Connecting) {\r\n            this._logger.log(LogLevel.Warning, `Call to HttpConnection.stopConnection(${error}) was ignored because the connection is still in the connecting state.`);\r\n            throw new Error(`HttpConnection.stopConnection(${error}) was called while the connection is still in the connecting state.`);\r\n        }\r\n\r\n        if (this._connectionState === ConnectionState.Disconnecting) {\r\n            // A call to stop() induced this call to stopConnection and needs to be completed.\r\n            // Any stop() awaiters will be scheduled to continue after the onclose callback fires.\r\n            this._stopPromiseResolver();\r\n        }\r\n\r\n        if (error) {\r\n            this._logger.log(LogLevel.Error, `Connection disconnected with error '${error}'.`);\r\n        } else {\r\n            this._logger.log(LogLevel.Information, \"Connection disconnected.\");\r\n        }\r\n\r\n        if (this._sendQueue) {\r\n            this._sendQueue.stop().catch((e) => {\r\n                this._logger.log(LogLevel.Error, `TransportSendQueue.stop() threw error '${e}'.`);\r\n            });\r\n            this._sendQueue = undefined;\r\n        }\r\n\r\n        this.connectionId = undefined;\r\n        this._connectionState = ConnectionState.Disconnected;\r\n\r\n        if (this._connectionStarted) {\r\n            this._connectionStarted = false;\r\n            try {\r\n                if (this.onclose) {\r\n                    this.onclose(error);\r\n                }\r\n            } catch (e) {\r\n                this._logger.log(LogLevel.Error, `HttpConnection.onclose(${error}) threw error '${e}'.`);\r\n            }\r\n        }\r\n    }\r\n\r\n    private _resolveUrl(url: string): string {\r\n        // startsWith is not supported in IE\r\n        if (url.lastIndexOf(\"https://\", 0) === 0 || url.lastIndexOf(\"http://\", 0) === 0) {\r\n            return url;\r\n        }\r\n\r\n        if (!Platform.isBrowser) {\r\n            throw new Error(`Cannot resolve '${url}'.`);\r\n        }\r\n\r\n        // Setting the url to the href propery of an anchor tag handles normalization\r\n        // for us. There are 3 main cases.\r\n        // 1. Relative path normalization e.g \"b\" -> \"http://localhost:5000/a/b\"\r\n        // 2. Absolute path normalization e.g \"/a/b\" -> \"http://localhost:5000/a/b\"\r\n        // 3. Networkpath reference normalization e.g \"//localhost:5000/a/b\" -> \"http://localhost:5000/a/b\"\r\n        const aTag = window.document.createElement(\"a\");\r\n        aTag.href = url;\r\n\r\n        this._logger.log(LogLevel.Information, `Normalizing '${url}' to '${aTag.href}'.`);\r\n        return aTag.href;\r\n    }\r\n\r\n    private _resolveNegotiateUrl(url: string): string {\r\n        const index = url.indexOf(\"?\");\r\n        let negotiateUrl = url.substring(0, index === -1 ? url.length : index);\r\n        if (negotiateUrl[negotiateUrl.length - 1] !== \"/\") {\r\n            negotiateUrl += \"/\";\r\n        }\r\n        negotiateUrl += \"negotiate\";\r\n        negotiateUrl += index === -1 ? \"\" : url.substring(index);\r\n\r\n        if (negotiateUrl.indexOf(\"negotiateVersion\") === -1) {\r\n            negotiateUrl += index === -1 ? \"?\" : \"&\";\r\n            negotiateUrl += \"negotiateVersion=\" + this._negotiateVersion;\r\n        }\r\n        return negotiateUrl;\r\n    }\r\n}\r\n\r\nfunction transportMatches(requestedTransport: HttpTransportType | undefined, actualTransport: HttpTransportType) {\r\n    return !requestedTransport || ((actualTransport & requestedTransport) !== 0);\r\n}\r\n\r\n/** @private */\r\nexport class TransportSendQueue {\r\n    private _buffer: any[] = [];\r\n    private _sendBufferedData: PromiseSource;\r\n    private _executing: boolean = true;\r\n    private _transportResult?: PromiseSource;\r\n    private _sendLoopPromise: Promise<void>;\r\n\r\n    constructor(private readonly _transport: ITransport) {\r\n        this._sendBufferedData = new PromiseSource();\r\n        this._transportResult = new PromiseSource();\r\n\r\n        this._sendLoopPromise = this._sendLoop();\r\n    }\r\n\r\n    public send(data: string | ArrayBuffer): Promise<void> {\r\n        this._bufferData(data);\r\n        if (!this._transportResult) {\r\n            this._transportResult = new PromiseSource();\r\n        }\r\n        return this._transportResult.promise;\r\n    }\r\n\r\n    public stop(): Promise<void> {\r\n        this._executing = false;\r\n        this._sendBufferedData.resolve();\r\n        return this._sendLoopPromise;\r\n    }\r\n\r\n    private _bufferData(data: string | ArrayBuffer): void {\r\n        if (this._buffer.length && typeof(this._buffer[0]) !== typeof(data)) {\r\n            throw new Error(`Expected data to be of type ${typeof(this._buffer)} but was of type ${typeof(data)}`);\r\n        }\r\n\r\n        this._buffer.push(data);\r\n        this._sendBufferedData.resolve();\r\n    }\r\n\r\n    private async _sendLoop(): Promise<void> {\r\n        while (true) {\r\n            await this._sendBufferedData.promise;\r\n\r\n            if (!this._executing) {\r\n                if (this._transportResult) {\r\n                    this._transportResult.reject(\"Connection stopped.\");\r\n                }\r\n\r\n                break;\r\n            }\r\n\r\n            this._sendBufferedData = new PromiseSource();\r\n\r\n            const transportResult = this._transportResult!;\r\n            this._transportResult = undefined;\r\n\r\n            const data = typeof(this._buffer[0]) === \"string\" ?\r\n                this._buffer.join(\"\") :\r\n                TransportSendQueue._concatBuffers(this._buffer);\r\n\r\n            this._buffer.length = 0;\r\n\r\n            try {\r\n                await this._transport.send(data);\r\n                transportResult.resolve();\r\n            } catch (error) {\r\n                transportResult.reject(error);\r\n            }\r\n        }\r\n    }\r\n\r\n    private static _concatBuffers(arrayBuffers: ArrayBuffer[]): ArrayBuffer {\r\n        const totalLength = arrayBuffers.map((b) => b.byteLength).reduce((a, b) => a + b);\r\n        const result = new Uint8Array(totalLength);\r\n        let offset = 0;\r\n        for (const item of arrayBuffers) {\r\n            result.set(new Uint8Array(item), offset);\r\n            offset += item.byteLength;\r\n        }\r\n\r\n        return result.buffer;\r\n    }\r\n}\r\n\r\nclass PromiseSource {\r\n    private _resolver?: () => void;\r\n    private _rejecter!: (reason?: any) => void;\r\n    public promise: Promise<void>;\r\n\r\n    constructor() {\r\n        this.promise = new Promise((resolve, reject) => [this._resolver, this._rejecter] = [resolve, reject]);\r\n    }\r\n\r\n    public resolve(): void {\r\n        this._resolver!();\r\n    }\r\n\r\n    public reject(reason?: any): void {\r\n        this._rejecter!(reason);\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { CompletionMessage, HubMessage, IHubProtocol, InvocationMessage, MessageType, StreamItemMessage } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { TransferFormat } from \"./ITransport\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { TextMessageFormat } from \"./TextMessageFormat\";\r\n\r\nconst JSON_HUB_PROTOCOL_NAME: string = \"json\";\r\n\r\n/** Implements the JSON Hub Protocol. */\r\nexport class JsonHubProtocol implements IHubProtocol {\r\n\r\n    /** @inheritDoc */\r\n    public readonly name: string = JSON_HUB_PROTOCOL_NAME;\r\n    /** @inheritDoc */\r\n    public readonly version: number = 1;\r\n\r\n    /** @inheritDoc */\r\n    public readonly transferFormat: TransferFormat = TransferFormat.Text;\r\n\r\n    /** Creates an array of {@link @microsoft/signalr.HubMessage} objects from the specified serialized representation.\r\n     *\r\n     * @param {string} input A string containing the serialized representation.\r\n     * @param {ILogger} logger A logger that will be used to log messages that occur during parsing.\r\n     */\r\n    public parseMessages(input: string, logger: ILogger): HubMessage[] {\r\n        // The interface does allow \"ArrayBuffer\" to be passed in, but this implementation does not. So let's throw a useful error.\r\n        if (typeof input !== \"string\") {\r\n            throw new Error(\"Invalid input for JSON hub protocol. Expected a string.\");\r\n        }\r\n\r\n        if (!input) {\r\n            return [];\r\n        }\r\n\r\n        if (logger === null) {\r\n            logger = NullLogger.instance;\r\n        }\r\n\r\n        // Parse the messages\r\n        const messages = TextMessageFormat.parse(input);\r\n\r\n        const hubMessages = [];\r\n        for (const message of messages) {\r\n            const parsedMessage = JSON.parse(message) as HubMessage;\r\n            if (typeof parsedMessage.type !== \"number\") {\r\n                throw new Error(\"Invalid payload.\");\r\n            }\r\n            switch (parsedMessage.type) {\r\n                case MessageType.Invocation:\r\n                    this._isInvocationMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.StreamItem:\r\n                    this._isStreamItemMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Completion:\r\n                    this._isCompletionMessage(parsedMessage);\r\n                    break;\r\n                case MessageType.Ping:\r\n                    // Single value, no need to validate\r\n                    break;\r\n                case MessageType.Close:\r\n                    // All optional values, no need to validate\r\n                    break;\r\n                default:\r\n                    // Future protocol changes can add message types, old clients can ignore them\r\n                    logger.log(LogLevel.Information, \"Unknown message type '\" + parsedMessage.type + \"' ignored.\");\r\n                    continue;\r\n            }\r\n            hubMessages.push(parsedMessage);\r\n        }\r\n\r\n        return hubMessages;\r\n    }\r\n\r\n    /** Writes the specified {@link @microsoft/signalr.HubMessage} to a string and returns it.\r\n     *\r\n     * @param {HubMessage} message The message to write.\r\n     * @returns {string} A string containing the serialized representation of the message.\r\n     */\r\n    public writeMessage(message: HubMessage): string {\r\n        return TextMessageFormat.write(JSON.stringify(message));\r\n    }\r\n\r\n    private _isInvocationMessage(message: InvocationMessage): void {\r\n        this._assertNotEmptyString(message.target, \"Invalid payload for Invocation message.\");\r\n\r\n        if (message.invocationId !== undefined) {\r\n            this._assertNotEmptyString(message.invocationId, \"Invalid payload for Invocation message.\");\r\n        }\r\n    }\r\n\r\n    private _isStreamItemMessage(message: StreamItemMessage): void {\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for StreamItem message.\");\r\n\r\n        if (message.item === undefined) {\r\n            throw new Error(\"Invalid payload for StreamItem message.\");\r\n        }\r\n    }\r\n\r\n    private _isCompletionMessage(message: CompletionMessage): void {\r\n        if (message.result && message.error) {\r\n            throw new Error(\"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        if (!message.result && message.error) {\r\n            this._assertNotEmptyString(message.error, \"Invalid payload for Completion message.\");\r\n        }\r\n\r\n        this._assertNotEmptyString(message.invocationId, \"Invalid payload for Completion message.\");\r\n    }\r\n\r\n    private _assertNotEmptyString(value: any, errorMessage: string): void {\r\n        if (typeof value !== \"string\" || value === \"\") {\r\n            throw new Error(errorMessage);\r\n        }\r\n    }\r\n}\r\n", "// Licensed to the .NET Foundation under one or more agreements.\r\n// The .NET Foundation licenses this file to you under the MIT license.\r\n\r\nimport { DefaultReconnectPolicy } from \"./DefaultReconnectPolicy\";\r\nimport { HttpConnection } from \"./HttpConnection\";\r\nimport { HubConnection } from \"./HubConnection\";\r\nimport { IHttpConnectionOptions } from \"./IHttpConnectionOptions\";\r\nimport { IHubProtocol } from \"./IHubProtocol\";\r\nimport { ILogger, LogLevel } from \"./ILogger\";\r\nimport { IRetryPolicy } from \"./IRetryPolicy\";\r\nimport { HttpTransportType } from \"./ITransport\";\r\nimport { JsonHubProtocol } from \"./JsonHubProtocol\";\r\nimport { NullLogger } from \"./Loggers\";\r\nimport { Arg, ConsoleLogger } from \"./Utils\";\r\n\r\nconst LogLevelNameMapping: {[k: string]: LogLevel} = {\r\n    trace: LogLevel.Trace,\r\n    debug: LogLevel.Debug,\r\n    info: LogLevel.Information,\r\n    information: LogLevel.Information,\r\n    warn: LogLevel.Warning,\r\n    warning: LogLevel.Warning,\r\n    error: LogLevel.Error,\r\n    critical: LogLevel.Critical,\r\n    none: LogLevel.None,\r\n};\r\n\r\nfunction parseLogLevel(name: string): LogLevel {\r\n    // Case-insensitive matching via lower-casing\r\n    // Yes, I know case-folding is a complicated problem in Unicode, but we only support\r\n    // the ASCII strings defined in LogLevelNameMapping anyway, so it's fine -anurse.\r\n    const mapping = LogLevelNameMapping[name.toLowerCase()];\r\n    if (typeof mapping !== \"undefined\") {\r\n        return mapping;\r\n    } else {\r\n        throw new Error(`Unknown log level: ${name}`);\r\n    }\r\n}\r\n\r\n/** A builder for configuring {@link @microsoft/signalr.HubConnection} instances. */\r\nexport class HubConnectionBuilder {\r\n    /** @internal */\r\n    public protocol?: IHubProtocol;\r\n    /** @internal */\r\n    public httpConnectionOptions?: IHttpConnectionOptions;\r\n    /** @internal */\r\n    public url?: string;\r\n    /** @internal */\r\n    public logger?: ILogger;\r\n\r\n    /** If defined, this indicates the client should automatically attempt to reconnect if the connection is lost. */\r\n    /** @internal */\r\n    public reconnectPolicy?: IRetryPolicy;\r\n\r\n    /** Configures console logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel} logLevel The minimum level of messages to log. Anything at this level, or a more severe level, will be logged.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logLevel: LogLevel): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {ILogger} logger An object implementing the {@link @microsoft/signalr.ILogger} interface, which will be used to write all log messages.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logger: ILogger): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {string} logLevel A string representing a LogLevel setting a minimum level of messages to log.\r\n     *    See {@link https://docs.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     */\r\n    public configureLogging(logLevel: string): HubConnectionBuilder;\r\n\r\n    /** Configures custom logging for the {@link @microsoft/signalr.HubConnection}.\r\n     *\r\n     * @param {LogLevel | string | ILogger} logging A {@link @microsoft/signalr.LogLevel}, a string representing a LogLevel, or an object implementing the {@link @microsoft/signalr.ILogger} interface.\r\n     *    See {@link https://docs.microsoft.com/aspnet/core/signalr/configuration#configure-logging|the documentation for client logging configuration} for more details.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder;\r\n    public configureLogging(logging: LogLevel | string | ILogger): HubConnectionBuilder {\r\n        Arg.isRequired(logging, \"logging\");\r\n\r\n        if (isLogger(logging)) {\r\n            this.logger = logging;\r\n        } else if (typeof logging === \"string\") {\r\n            const logLevel = parseLogLevel(logging);\r\n            this.logger = new ConsoleLogger(logLevel);\r\n        } else {\r\n            this.logger = new ConsoleLogger(logging);\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * The transport will be selected automatically based on what the server and client support.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified HTTP-based transport to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {HttpTransportType} transportType The specific transport to use.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, transportType: HttpTransportType): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use HTTP-based transports to connect to the specified URL.\r\n     *\r\n     * @param {string} url The URL the connection will use.\r\n     * @param {IHttpConnectionOptions} options An options object used to configure the connection.\r\n     * @returns The {@link @microsoft/signalr.HubConnectionBuilder} instance, for chaining.\r\n     */\r\n    public withUrl(url: string, options: IHttpConnectionOptions): HubConnectionBuilder;\r\n    public withUrl(url: string, transportTypeOrOptions?: IHttpConnectionOptions | HttpTransportType): HubConnectionBuilder {\r\n        Arg.isRequired(url, \"url\");\r\n        Arg.isNotEmpty(url, \"url\");\r\n\r\n        this.url = url;\r\n\r\n        // Flow-typing knows where it's at. Since HttpTransportType is a number and IHttpConnectionOptions is guaranteed\r\n        // to be an object, we know (as does TypeScript) this comparison is all we need to figure out which overload was called.\r\n        if (typeof transportTypeOrOptions === \"object\") {\r\n            this.httpConnectionOptions = { ...this.httpConnectionOptions, ...transportTypeOrOptions };\r\n        } else {\r\n            this.httpConnectionOptions = {\r\n                ...this.httpConnectionOptions,\r\n                transport: transportTypeOrOptions,\r\n            };\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to use the specified Hub Protocol.\r\n     *\r\n     * @param {IHubProtocol} protocol The {@link @microsoft/signalr.IHubProtocol} implementation to use.\r\n     */\r\n    public withHubProtocol(protocol: IHubProtocol): HubConnectionBuilder {\r\n        Arg.isRequired(protocol, \"protocol\");\r\n\r\n        this.protocol = protocol;\r\n        return this;\r\n    }\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     * By default, the client will wait 0, 2, 10 and 30 seconds respectively before trying up to 4 reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {number[]} retryDelays An array containing the delays in milliseconds before trying each reconnect attempt.\r\n     * The length of the array represents how many failed reconnect attempts it takes before the client will stop attempting to reconnect.\r\n     */\r\n    public withAutomaticReconnect(retryDelays: number[]): HubConnectionBuilder;\r\n\r\n    /** Configures the {@link @microsoft/signalr.HubConnection} to automatically attempt to reconnect if the connection is lost.\r\n     *\r\n     * @param {IRetryPolicy} reconnectPolicy An {@link @microsoft/signalR.IRetryPolicy} that controls the timing and number of reconnect attempts.\r\n     */\r\n    public withAutomaticReconnect(reconnectPolicy: IRetryPolicy): HubConnectionBuilder;\r\n    public withAutomaticReconnect(retryDelaysOrReconnectPolicy?: number[] | IRetryPolicy): HubConnectionBuilder {\r\n        if (this.reconnectPolicy) {\r\n            throw new Error(\"A reconnectPolicy has already been set.\");\r\n        }\r\n\r\n        if (!retryDelaysOrReconnectPolicy) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy();\r\n        } else if (Array.isArray(retryDelaysOrReconnectPolicy)) {\r\n            this.reconnectPolicy = new DefaultReconnectPolicy(retryDelaysOrReconnectPolicy);\r\n        } else {\r\n            this.reconnectPolicy = retryDelaysOrReconnectPolicy;\r\n        }\r\n\r\n        return this;\r\n    }\r\n\r\n    /** Creates a {@link @microsoft/signalr.HubConnection} from the configuration options specified in this builder.\r\n     *\r\n     * @returns {HubConnection} The configured {@link @microsoft/signalr.HubConnection}.\r\n     */\r\n    public build(): HubConnection {\r\n        // If httpConnectionOptions has a logger, use it. Otherwise, override it with the one\r\n        // provided to configureLogger\r\n        const httpConnectionOptions = this.httpConnectionOptions || {};\r\n\r\n        // If it's 'null', the user **explicitly** asked for null, don't mess with it.\r\n        if (httpConnectionOptions.logger === undefined) {\r\n            // If our logger is undefined or null, that's OK, the HttpConnection constructor will handle it.\r\n            httpConnectionOptions.logger = this.logger;\r\n        }\r\n\r\n        // Now create the connection\r\n        if (!this.url) {\r\n            throw new Error(\"The 'HubConnectionBuilder.withUrl' method must be called before building the connection.\");\r\n        }\r\n        const connection = new HttpConnection(this.url, httpConnectionOptions);\r\n\r\n        return HubConnection.create(\r\n            connection,\r\n            this.logger || NullLogger.instance,\r\n            this.protocol || new JsonHubProtocol(),\r\n            this.reconnectPolicy);\r\n    }\r\n}\r\n\r\nfunction isLogger(logger: any): logger is ILogger {\r\n    return logger.log !== undefined;\r\n}\r\n"], "mappings": ";;;;;AAMM,IAAO,YAAP,cAAyB,MAAK;;;;;;EAahC,YAAY,cAAsB,YAAkB;AAChD,UAAM,YAAY,WAAW;AAC7B,UAAM,GAAG,YAAY,kBAAkB,UAAU,GAAG;AACpD,SAAK,aAAa;AAIlB,SAAK,YAAY;EACrB;;AAIE,IAAO,eAAP,cAA4B,MAAK;;;;;EASnC,YAAY,eAAuB,uBAAqB;AACpD,UAAM,YAAY,WAAW;AAC7B,UAAM,YAAY;AAIlB,SAAK,YAAY;EACrB;;AAIE,IAAO,aAAP,cAA0B,MAAK;;;;;EASjC,YAAY,eAAuB,sBAAoB;AACnD,UAAM,YAAY,WAAW;AAC7B,UAAM,YAAY;AAIlB,SAAK,YAAY;EACrB;;AAKE,IAAO,4BAAP,cAAyC,MAAK;;;;;;EAgBhD,YAAY,SAAiB,WAA4B;AACrD,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAIjB,SAAK,YAAY;EACrB;;AAKE,IAAO,yBAAP,cAAsC,MAAK;;;;;;EAgB7C,YAAY,SAAiB,WAA4B;AACrD,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAIjB,SAAK,YAAY;EACrB;;AAKE,IAAO,8BAAP,cAA2C,MAAK;;;;;;EAgBlD,YAAY,SAAiB,WAA4B;AACrD,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AACjB,SAAK,YAAY;AAIjB,SAAK,YAAY;EACrB;;AAKE,IAAO,mCAAP,cAAgD,MAAK;;;;;EAYvD,YAAY,SAAe;AACvB,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AACb,SAAK,YAAY;AAIjB,SAAK,YAAY;EACrB;;AAKE,IAAO,kBAAP,cAA+B,MAAK;;;;;;EAatC,YAAY,SAAiB,aAAoB;AAC7C,UAAM,YAAY,WAAW;AAC7B,UAAM,OAAO;AAEb,SAAK,cAAc;AAInB,SAAK,YAAY;EACrB;;;;AC/KE,IAAO,eAAP,MAAmB;EAqCrB,YACoB,YACA,YACA,SAA8B;AAF9B,SAAA,aAAA;AACA,SAAA,aAAA;AACA,SAAA,UAAA;EACpB;;AAOE,IAAgB,aAAhB,MAA0B;EAerB,IAAI,KAAa,SAAqB;AACzC,WAAO,KAAK,KAAK;MACb,GAAG;MACH,QAAQ;MACR;KACH;EACL;EAgBO,KAAK,KAAa,SAAqB;AAC1C,WAAO,KAAK,KAAK;MACb,GAAG;MACH,QAAQ;MACR;KACH;EACL;EAgBO,OAAO,KAAa,SAAqB;AAC5C,WAAO,KAAK,KAAK;MACb,GAAG;MACH,QAAQ;MACR;KACH;EACL;;;;;;;EAeO,gBAAgB,KAAW;AAC9B,WAAO;EACX;;;;AC5JJ,IAAY;CAAZ,SAAYA,WAAQ;AAEhB,EAAAA,UAAAA,UAAA,OAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,UAAAA,UAAA,OAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,UAAAA,UAAA,aAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,UAAAA,UAAA,SAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,UAAAA,UAAA,OAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,UAAAA,UAAA,UAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,UAAAA,UAAA,MAAA,IAAA,CAAA,IAAA;AACJ,GAfY,aAAA,WAAQ,CAAA,EAAA;;;ACFd,IAAO,aAAP,MAAiB;EAInB,cAAA;EAAuB;;;EAIhB,IAAI,WAAqB,UAAgB;EAChD;;AAPc,WAAA,WAAoB,IAAI,WAAU;;;ACK7C,IAAM,UAAkB;AAEzB,IAAO,MAAP,MAAU;EACL,OAAO,WAAW,KAAU,MAAY;AAC3C,QAAI,QAAQ,QAAQ,QAAQ,QAAW;AACnC,YAAM,IAAI,MAAM,QAAQ,IAAI,yBAAyB;;EAE7D;EACO,OAAO,WAAW,KAAa,MAAY;AAC9C,QAAI,CAAC,OAAO,IAAI,MAAM,OAAO,GAAG;AAC5B,YAAM,IAAI,MAAM,QAAQ,IAAI,iCAAiC;;EAErE;EAEO,OAAO,KAAK,KAAU,QAAa,MAAY;AAElD,QAAI,EAAE,OAAO,SAAS;AAClB,YAAM,IAAI,MAAM,WAAW,IAAI,WAAW,GAAG,GAAG;;EAExD;;AAIE,IAAO,WAAP,MAAe;;EAEV,WAAW,YAAS;AACvB,WAAO,OAAO,WAAW,YAAY,OAAO,OAAO,aAAa;EACpE;;EAGO,WAAW,cAAW;AACzB,WAAO,OAAO,SAAS,YAAY,mBAAmB;EAC1D;;EAGA,WAAW,gBAAa;AACpB,WAAO,OAAO,WAAW,YAAY,OAAO,OAAO,aAAa;EACpE;;;EAIO,WAAW,SAAM;AACpB,WAAO,CAAC,KAAK,aAAa,CAAC,KAAK,eAAe,CAAC,KAAK;EACzD;;AAIE,SAAU,cAAc,MAAW,gBAAuB;AAC5D,MAAI,SAAS;AACb,MAAI,cAAc,IAAI,GAAG;AACrB,aAAS,yBAAyB,KAAK,UAAU;AACjD,QAAI,gBAAgB;AAChB,gBAAU,eAAe,kBAAkB,IAAI,CAAC;;aAE7C,OAAO,SAAS,UAAU;AACjC,aAAS,yBAAyB,KAAK,MAAM;AAC7C,QAAI,gBAAgB;AAChB,gBAAU,eAAe,IAAI;;;AAGrC,SAAO;AACX;AAGM,SAAU,kBAAkB,MAAiB;AAC/C,QAAM,OAAO,IAAI,WAAW,IAAI;AAGhC,MAAI,MAAM;AACV,OAAK,QAAQ,CAAC,QAAO;AACjB,UAAM,MAAM,MAAM,KAAK,MAAM;AAC7B,WAAO,KAAK,GAAG,GAAG,IAAI,SAAS,EAAE,CAAC;EACtC,CAAC;AAGD,SAAO,IAAI,OAAO,GAAG,IAAI,SAAS,CAAC;AACvC;AAIM,SAAU,cAAc,KAAQ;AAClC,SAAO,OAAO,OAAO,gBAAgB,gBAChC,eAAe;EAEX,IAAI,eAAe,IAAI,YAAY,SAAS;AACzD;AAGA,eAAsB,YAAY,QAAiB,eAAuB,YAAwB,KAChE,SAA+B,SAA+B;AAC5F,QAAM,UAAiC,CAAA;AAEvC,QAAM,CAAC,MAAM,KAAK,IAAI,mBAAkB;AACxC,UAAQ,IAAI,IAAI;AAEhB,SAAO,IAAI,SAAS,OAAO,IAAI,aAAa,6BAA6B,cAAc,SAAS,QAAQ,iBAAkB,CAAC,GAAG;AAE9H,QAAM,eAAe,cAAc,OAAO,IAAI,gBAAgB;AAC9D,QAAM,WAAW,MAAM,WAAW,KAAK,KAAK;IACxC;IACA,SAAS,EAAE,GAAG,SAAS,GAAG,QAAQ,QAAO;IACzC;IACA,SAAS,QAAQ;IACjB,iBAAiB,QAAQ;GAC5B;AAED,SAAO,IAAI,SAAS,OAAO,IAAI,aAAa,kDAAkD,SAAS,UAAU,GAAG;AACxH;AAGM,SAAU,aAAa,QAA2B;AACpD,MAAI,WAAW,QAAW;AACtB,WAAO,IAAI,cAAc,SAAS,WAAW;;AAGjD,MAAI,WAAW,MAAM;AACjB,WAAO,WAAW;;AAGtB,MAAK,OAAmB,QAAQ,QAAW;AACvC,WAAO;;AAGX,SAAO,IAAI,cAAc,MAAkB;AAC/C;AAGM,IAAO,sBAAP,MAA0B;EAI5B,YAAY,SAAqB,UAA8B;AAC3D,SAAK,WAAW;AAChB,SAAK,YAAY;EACrB;EAEO,UAAO;AACV,UAAM,QAAgB,KAAK,SAAS,UAAU,QAAQ,KAAK,SAAS;AACpE,QAAI,QAAQ,IAAI;AACZ,WAAK,SAAS,UAAU,OAAO,OAAO,CAAC;;AAG3C,QAAI,KAAK,SAAS,UAAU,WAAW,KAAK,KAAK,SAAS,gBAAgB;AACtE,WAAK,SAAS,eAAc,EAAG,MAAM,CAAC,MAAK;MAAG,CAAC;;EAEvD;;AAIE,IAAO,gBAAP,MAAoB;EAWtB,YAAY,iBAAyB;AACjC,SAAK,YAAY;AACjB,SAAK,MAAM;EACf;EAEO,IAAI,UAAoB,SAAe;AAC1C,QAAI,YAAY,KAAK,WAAW;AAC5B,YAAM,MAAM,KAAI,oBAAI,KAAI,GAAG,YAAW,CAAE,KAAK,SAAS,QAAQ,CAAC,KAAK,OAAO;AAC3E,cAAQ,UAAU;QACd,KAAK,SAAS;QACd,KAAK,SAAS;AACV,eAAK,IAAI,MAAM,GAAG;AAClB;QACJ,KAAK,SAAS;AACV,eAAK,IAAI,KAAK,GAAG;AACjB;QACJ,KAAK,SAAS;AACV,eAAK,IAAI,KAAK,GAAG;AACjB;QACJ;AAEI,eAAK,IAAI,IAAI,GAAG;AAChB;;;EAGhB;;AAIE,SAAU,qBAAkB;AAC9B,MAAI,sBAAsB;AAC1B,MAAI,SAAS,QAAQ;AACjB,0BAAsB;;AAE1B,SAAO,CAAE,qBAAqB,mBAAmB,SAAS,UAAS,GAAI,WAAU,GAAI,kBAAiB,CAAE,CAAC;AAC7G;AAGM,SAAU,mBAAmB,SAAiB,IAAY,SAAiB,gBAAkC;AAE/G,MAAI,YAAoB;AAExB,QAAM,gBAAgB,QAAQ,MAAM,GAAG;AACvC,eAAa,GAAG,cAAc,CAAC,CAAC,IAAI,cAAc,CAAC,CAAC;AACpD,eAAa,KAAK,OAAO;AAEzB,MAAI,MAAM,OAAO,IAAI;AACjB,iBAAa,GAAG,EAAE;SACf;AACH,iBAAa;;AAGjB,eAAa,GAAG,OAAO;AAEvB,MAAI,gBAAgB;AAChB,iBAAa,KAAK,cAAc;SAC7B;AACH,iBAAa;;AAGjB,eAAa;AACb,SAAO;AACX;AAGc,SAAS,YAAS;AAC5B,MAAI,SAAS,QAAQ;AACjB,YAAQ,QAAQ,UAAU;MACtB,KAAK;AACD,eAAO;MACX,KAAK;AACD,eAAO;MACX,KAAK;AACD,eAAO;MACX;AACI,eAAO,QAAQ;;SAEpB;AACH,WAAO;;AAEf;AAGc,SAAS,oBAAiB;AACpC,MAAI,SAAS,QAAQ;AACjB,WAAO,QAAQ,SAAS;;AAE5B,SAAO;AACX;AAEA,SAAS,aAAU;AACf,MAAI,SAAS,QAAQ;AACjB,WAAO;SACJ;AACH,WAAO;;AAEf;AAGM,SAAU,eAAe,GAAM;AACjC,MAAI,EAAE,OAAO;AACT,WAAO,EAAE;aACF,EAAE,SAAS;AAClB,WAAO,EAAE;;AAEb,SAAO,GAAG,CAAC;AACf;AAGM,SAAU,gBAAa;AAEzB,MAAI,OAAO,eAAe,aAAa;AACnC,WAAO;;AAEX,MAAI,OAAO,SAAS,aAAa;AAC7B,WAAO;;AAEX,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;;AAEX,MAAI,OAAO,WAAW,aAAa;AAC/B,WAAO;;AAEX,QAAM,IAAI,MAAM,uBAAuB;AAC3C;;;AC9RM,IAAO,kBAAP,cAA+B,WAAU;EAO3C,YAAmB,QAAe;AAC9B,UAAK;AACL,SAAK,UAAU;AAEf,QAAI,OAAO,UAAU,aAAa;AAG9B,YAAM,cAAc,OAAO,wBAAwB,aAAa,0BAA0B;AAG1F,WAAK,OAAO,KAAK,YAAY,cAAc,GAAG,UAAS;AACvD,WAAK,aAAa,YAAY,YAAY;AAI1C,WAAK,aAAa,YAAY,cAAc,EAAE,KAAK,YAAY,KAAK,IAAI;WACrE;AACH,WAAK,aAAa,MAAM,KAAK,cAAa,CAAE;;AAEhD,QAAI,OAAO,oBAAoB,aAAa;AAGxC,YAAM,cAAc,OAAO,wBAAwB,aAAa,0BAA0B;AAG1F,WAAK,uBAAuB,YAAY,kBAAkB;WACvD;AACH,WAAK,uBAAuB;;EAEpC;;EAGO,MAAM,KAAK,SAAoB;AAElC,QAAI,QAAQ,eAAe,QAAQ,YAAY,SAAS;AACpD,YAAM,IAAI,WAAU;;AAGxB,QAAI,CAAC,QAAQ,QAAQ;AACjB,YAAM,IAAI,MAAM,oBAAoB;;AAExC,QAAI,CAAC,QAAQ,KAAK;AACd,YAAM,IAAI,MAAM,iBAAiB;;AAGrC,UAAM,kBAAkB,IAAI,KAAK,qBAAoB;AAErD,QAAI;AAEJ,QAAI,QAAQ,aAAa;AACrB,cAAQ,YAAY,UAAU,MAAK;AAC/B,wBAAgB,MAAK;AACrB,gBAAQ,IAAI,WAAU;MAC1B;;AAKJ,QAAI,YAAiB;AACrB,QAAI,QAAQ,SAAS;AACjB,YAAM,YAAY,QAAQ;AAC1B,kBAAY,WAAW,MAAK;AACxB,wBAAgB,MAAK;AACrB,aAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B;AAC/D,gBAAQ,IAAI,aAAY;MAC5B,GAAG,SAAS;;AAGhB,QAAI,QAAQ,YAAY,IAAI;AACxB,cAAQ,UAAU;;AAEtB,QAAI,QAAQ,SAAS;AAEjB,cAAQ,UAAU,QAAQ,WAAW,CAAA;AACrC,UAAI,cAAc,QAAQ,OAAO,GAAG;AAChC,gBAAQ,QAAQ,cAAc,IAAI;aAC/B;AACH,gBAAQ,QAAQ,cAAc,IAAI;;;AAI1C,QAAI;AACJ,QAAI;AACA,iBAAW,MAAM,KAAK,WAAW,QAAQ,KAAM;QAC3C,MAAM,QAAQ;QACd,OAAO;QACP,aAAa,QAAQ,oBAAoB,OAAO,YAAY;QAC5D,SAAS;UACL,oBAAoB;UACpB,GAAG,QAAQ;;QAEf,QAAQ,QAAQ;QAChB,MAAM;QACN,UAAU;QACV,QAAQ,gBAAgB;OAC3B;aACI,GAAG;AACR,UAAI,OAAO;AACP,cAAM;;AAEV,WAAK,QAAQ,IACT,SAAS,SACT,4BAA4B,CAAC,GAAG;AAEpC,YAAM;;AAEN,UAAI,WAAW;AACX,qBAAa,SAAS;;AAE1B,UAAI,QAAQ,aAAa;AACrB,gBAAQ,YAAY,UAAU;;;AAItC,QAAI,CAAC,SAAS,IAAI;AACd,YAAM,eAAe,MAAM,mBAAmB,UAAU,MAAM;AAC9D,YAAM,IAAI,UAAU,gBAAgB,SAAS,YAAY,SAAS,MAAM;;AAG5E,UAAM,UAAU,mBAAmB,UAAU,QAAQ,YAAY;AACjE,UAAM,UAAU,MAAM;AAEtB,WAAO,IAAI,aACP,SAAS,QACT,SAAS,YACT,OAAO;EAEf;EAEO,gBAAgB,KAAW;AAC9B,QAAI,UAAkB;AACtB,QAAI,SAAS,UAAU,KAAK,MAAM;AAE9B,WAAK,KAAK,WAAW,KAAK,CAAC,GAAG,MAAM,UAAU,EAAE,KAAK,IAAI,CAAC;;AAE9D,WAAO;EACX;;AAGJ,SAAS,mBAAmB,UAAoB,cAAyC;AACrF,MAAI;AACJ,UAAQ,cAAc;IAClB,KAAK;AACD,gBAAU,SAAS,YAAW;AAC9B;IACJ,KAAK;AACD,gBAAU,SAAS,KAAI;AACvB;IACJ,KAAK;IACL,KAAK;IACL,KAAK;AACD,YAAM,IAAI,MAAM,GAAG,YAAY,oBAAoB;IACvD;AACI,gBAAU,SAAS,KAAI;AACvB;;AAGR,SAAO;AACX;;;ACxKM,IAAO,gBAAP,cAA6B,WAAU;EAGzC,YAAmB,QAAe;AAC9B,UAAK;AACL,SAAK,UAAU;EACnB;;EAGO,KAAK,SAAoB;AAE5B,QAAI,QAAQ,eAAe,QAAQ,YAAY,SAAS;AACpD,aAAO,QAAQ,OAAO,IAAI,WAAU,CAAE;;AAG1C,QAAI,CAAC,QAAQ,QAAQ;AACjB,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;;AAEzD,QAAI,CAAC,QAAQ,KAAK;AACd,aAAO,QAAQ,OAAO,IAAI,MAAM,iBAAiB,CAAC;;AAGtD,WAAO,IAAI,QAAsB,CAAC,SAAS,WAAU;AACjD,YAAM,MAAM,IAAI,eAAc;AAE9B,UAAI,KAAK,QAAQ,QAAS,QAAQ,KAAM,IAAI;AAC5C,UAAI,kBAAkB,QAAQ,oBAAoB,SAAY,OAAO,QAAQ;AAC7E,UAAI,iBAAiB,oBAAoB,gBAAgB;AACzD,UAAI,QAAQ,YAAY,IAAI;AACxB,gBAAQ,UAAU;;AAEtB,UAAI,QAAQ,SAAS;AAEjB,YAAI,cAAc,QAAQ,OAAO,GAAG;AAChC,cAAI,iBAAiB,gBAAgB,0BAA0B;eAC5D;AACH,cAAI,iBAAiB,gBAAgB,0BAA0B;;;AAIvE,YAAM,UAAU,QAAQ;AACxB,UAAI,SAAS;AACT,eAAO,KAAK,OAAO,EACd,QAAQ,CAAC,WAAU;AAChB,cAAI,iBAAiB,QAAQ,QAAQ,MAAM,CAAC;QAChD,CAAC;;AAGT,UAAI,QAAQ,cAAc;AACtB,YAAI,eAAe,QAAQ;;AAG/B,UAAI,QAAQ,aAAa;AACrB,gBAAQ,YAAY,UAAU,MAAK;AAC/B,cAAI,MAAK;AACT,iBAAO,IAAI,WAAU,CAAE;QAC3B;;AAGJ,UAAI,QAAQ,SAAS;AACjB,YAAI,UAAU,QAAQ;;AAG1B,UAAI,SAAS,MAAK;AACd,YAAI,QAAQ,aAAa;AACrB,kBAAQ,YAAY,UAAU;;AAGlC,YAAI,IAAI,UAAU,OAAO,IAAI,SAAS,KAAK;AACvC,kBAAQ,IAAI,aAAa,IAAI,QAAQ,IAAI,YAAY,IAAI,YAAY,IAAI,YAAY,CAAC;eACnF;AACH,iBAAO,IAAI,UAAU,IAAI,YAAY,IAAI,gBAAgB,IAAI,YAAY,IAAI,MAAM,CAAC;;MAE5F;AAEA,UAAI,UAAU,MAAK;AACf,aAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B,IAAI,MAAM,KAAK,IAAI,UAAU,GAAG;AAC/F,eAAO,IAAI,UAAU,IAAI,YAAY,IAAI,MAAM,CAAC;MACpD;AAEA,UAAI,YAAY,MAAK;AACjB,aAAK,QAAQ,IAAI,SAAS,SAAS,4BAA4B;AAC/D,eAAO,IAAI,aAAY,CAAE;MAC7B;AAEA,UAAI,KAAK,QAAQ,OAAO;IAC5B,CAAC;EACL;;;;ACpFE,IAAO,oBAAP,cAAiC,WAAU;;EAI7C,YAAmB,QAAe;AAC9B,UAAK;AAEL,QAAI,OAAO,UAAU,eAAe,SAAS,QAAQ;AACjD,WAAK,cAAc,IAAI,gBAAgB,MAAM;eACtC,OAAO,mBAAmB,aAAa;AAC9C,WAAK,cAAc,IAAI,cAAc,MAAM;WACxC;AACH,YAAM,IAAI,MAAM,6BAA6B;;EAErD;;EAGO,KAAK,SAAoB;AAE5B,QAAI,QAAQ,eAAe,QAAQ,YAAY,SAAS;AACpD,aAAO,QAAQ,OAAO,IAAI,WAAU,CAAE;;AAG1C,QAAI,CAAC,QAAQ,QAAQ;AACjB,aAAO,QAAQ,OAAO,IAAI,MAAM,oBAAoB,CAAC;;AAEzD,QAAI,CAAC,QAAQ,KAAK;AACd,aAAO,QAAQ,OAAO,IAAI,MAAM,iBAAiB,CAAC;;AAGtD,WAAO,KAAK,YAAY,KAAK,OAAO;EACxC;EAEO,gBAAgB,KAAW;AAC9B,WAAO,KAAK,YAAY,gBAAgB,GAAG;EAC/C;;;;ACzCE,IAAO,oBAAP,MAAO,mBAAiB;EAInB,OAAO,MAAM,QAAc;AAC9B,WAAO,GAAG,MAAM,GAAG,mBAAkB,eAAe;EACxD;EAEO,OAAO,MAAM,OAAa;AAC7B,QAAI,MAAM,MAAM,SAAS,CAAC,MAAM,mBAAkB,iBAAiB;AAC/D,YAAM,IAAI,MAAM,wBAAwB;;AAG5C,UAAM,WAAW,MAAM,MAAM,mBAAkB,eAAe;AAC9D,aAAS,IAAG;AACZ,WAAO;EACX;;AAfc,kBAAA,sBAAsB;AACtB,kBAAA,kBAAkB,OAAO,aAAa,kBAAkB,mBAAmB;;;ACYvF,IAAO,oBAAP,MAAwB;;EAEnB,sBAAsB,kBAAyC;AAClE,WAAO,kBAAkB,MAAM,KAAK,UAAU,gBAAgB,CAAC;EACnE;EAEO,uBAAuB,MAAS;AACnC,QAAI;AACJ,QAAI;AAEJ,QAAI,cAAc,IAAI,GAAG;AAErB,YAAM,aAAa,IAAI,WAAW,IAAI;AACtC,YAAM,iBAAiB,WAAW,QAAQ,kBAAkB,mBAAmB;AAC/E,UAAI,mBAAmB,IAAI;AACvB,cAAM,IAAI,MAAM,wBAAwB;;AAK5C,YAAM,iBAAiB,iBAAiB;AACxC,oBAAc,OAAO,aAAa,MAAM,MAAM,MAAM,UAAU,MAAM,KAAK,WAAW,MAAM,GAAG,cAAc,CAAC,CAAC;AAC7G,sBAAiB,WAAW,aAAa,iBAAkB,WAAW,MAAM,cAAc,EAAE,SAAS;WAClG;AACH,YAAM,WAAmB;AACzB,YAAM,iBAAiB,SAAS,QAAQ,kBAAkB,eAAe;AACzE,UAAI,mBAAmB,IAAI;AACvB,cAAM,IAAI,MAAM,wBAAwB;;AAK5C,YAAM,iBAAiB,iBAAiB;AACxC,oBAAc,SAAS,UAAU,GAAG,cAAc;AAClD,sBAAiB,SAAS,SAAS,iBAAkB,SAAS,UAAU,cAAc,IAAI;;AAI9F,UAAM,WAAW,kBAAkB,MAAM,WAAW;AACpD,UAAM,WAAW,KAAK,MAAM,SAAS,CAAC,CAAC;AACvC,QAAI,SAAS,MAAM;AACf,YAAM,IAAI,MAAM,gDAAgD;;AAEpE,UAAM,kBAA4C;AAIlD,WAAO,CAAC,eAAe,eAAe;EAC1C;;;;AC5DJ,IAAY;CAAZ,SAAYC,cAAW;AAEnB,EAAAA,aAAAA,aAAA,YAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,aAAAA,aAAA,YAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,aAAAA,aAAA,YAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,aAAAA,aAAA,kBAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,aAAAA,aAAA,kBAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,aAAAA,aAAA,MAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,aAAAA,aAAA,OAAA,IAAA,CAAA,IAAA;AACJ,GAfY,gBAAA,cAAW,CAAA,EAAA;;;ACAjB,IAAO,UAAP,MAAc;EAOhB,cAAA;AACI,SAAK,YAAY,CAAA;EACrB;EAEO,KAAK,MAAO;AACf,eAAW,YAAY,KAAK,WAAW;AACnC,eAAS,KAAK,IAAI;;EAE1B;EAEO,MAAM,KAAQ;AACjB,eAAW,YAAY,KAAK,WAAW;AACnC,UAAI,SAAS,OAAO;AAChB,iBAAS,MAAM,GAAG;;;EAG9B;EAEO,WAAQ;AACX,eAAW,YAAY,KAAK,WAAW;AACnC,UAAI,SAAS,UAAU;AACnB,iBAAS,SAAQ;;;EAG7B;EAEO,UAAU,UAA8B;AAC3C,SAAK,UAAU,KAAK,QAAQ;AAC5B,WAAO,IAAI,oBAAoB,MAAM,QAAQ;EACjD;;;;AC9BJ,IAAM,wBAAgC,KAAK;AAC3C,IAAM,8BAAsC,KAAK;AAGjD,IAAY;CAAZ,SAAYC,qBAAkB;AAE1B,EAAAA,oBAAA,cAAA,IAAA;AAEA,EAAAA,oBAAA,YAAA,IAAA;AAEA,EAAAA,oBAAA,WAAA,IAAA;AAEA,EAAAA,oBAAA,eAAA,IAAA;AAEA,EAAAA,oBAAA,cAAA,IAAA;AACJ,GAXY,uBAAA,qBAAkB,CAAA,EAAA;AAcxB,IAAO,gBAAP,MAAO,eAAa;EAmEtB,YAAoB,YAAyB,QAAiB,UAAwB,iBAA8B;AAvC5G,SAAA,iBAAyB;AASzB,SAAA,uBAAuB,MAAK;AAEhC,WAAK,QAAQ,IAAI,SAAS,SAAS,sNAAsN;IAC7P;AA4BI,QAAI,WAAW,YAAY,YAAY;AACvC,QAAI,WAAW,QAAQ,QAAQ;AAC/B,QAAI,WAAW,UAAU,UAAU;AAEnC,SAAK,8BAA8B;AACnC,SAAK,kCAAkC;AAEvC,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,aAAa;AAClB,SAAK,mBAAmB;AACxB,SAAK,qBAAqB,IAAI,kBAAiB;AAE/C,SAAK,WAAW,YAAY,CAAC,SAAc,KAAK,qBAAqB,IAAI;AACzE,SAAK,WAAW,UAAU,CAAC,UAAkB,KAAK,kBAAkB,KAAK;AAEzE,SAAK,aAAa,CAAA;AAClB,SAAK,WAAW,CAAA;AAChB,SAAK,mBAAmB,CAAA;AACxB,SAAK,yBAAyB,CAAA;AAC9B,SAAK,wBAAwB,CAAA;AAC7B,SAAK,gBAAgB;AACrB,SAAK,6BAA6B;AAClC,SAAK,mBAAmB,mBAAmB;AAC3C,SAAK,qBAAqB;AAE1B,SAAK,qBAAqB,KAAK,UAAU,aAAa,EAAE,MAAM,YAAY,KAAI,CAAE;EACpF;;;;;;EAhCO,OAAO,OAAO,YAAyB,QAAiB,UAAwB,iBAA8B;AACjH,WAAO,IAAI,eAAc,YAAY,QAAQ,UAAU,eAAe;EAC1E;;EAiCA,IAAI,QAAK;AACL,WAAO,KAAK;EAChB;;;;EAKA,IAAI,eAAY;AACZ,WAAO,KAAK,aAAc,KAAK,WAAW,gBAAgB,OAAQ;EACtE;;EAGA,IAAI,UAAO;AACP,WAAO,KAAK,WAAW,WAAW;EACtC;;;;;;EAOA,IAAI,QAAQ,KAAW;AACnB,QAAI,KAAK,qBAAqB,mBAAmB,gBAAgB,KAAK,qBAAqB,mBAAmB,cAAc;AACxH,YAAM,IAAI,MAAM,wFAAwF;;AAG5G,QAAI,CAAC,KAAK;AACN,YAAM,IAAI,MAAM,4CAA4C;;AAGhE,SAAK,WAAW,UAAU;EAC9B;;;;;EAMO,QAAK;AACR,SAAK,gBAAgB,KAAK,2BAA0B;AACpD,WAAO,KAAK;EAChB;EAEQ,MAAM,6BAA0B;AACpC,QAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC3D,aAAO,QAAQ,OAAO,IAAI,MAAM,uEAAuE,CAAC;;AAG5G,SAAK,mBAAmB,mBAAmB;AAC3C,SAAK,QAAQ,IAAI,SAAS,OAAO,yBAAyB;AAE1D,QAAI;AACA,YAAM,KAAK,eAAc;AAEzB,UAAI,SAAS,WAAW;AAEpB,eAAO,SAAS,iBAAiB,UAAU,KAAK,oBAAoB;;AAGxE,WAAK,mBAAmB,mBAAmB;AAC3C,WAAK,qBAAqB;AAC1B,WAAK,QAAQ,IAAI,SAAS,OAAO,uCAAuC;aACnE,GAAG;AACR,WAAK,mBAAmB,mBAAmB;AAC3C,WAAK,QAAQ,IAAI,SAAS,OAAO,gEAAgE,CAAC,IAAI;AACtG,aAAO,QAAQ,OAAO,CAAC;;EAE/B;EAEQ,MAAM,iBAAc;AACxB,SAAK,wBAAwB;AAC7B,SAAK,6BAA6B;AAElC,UAAM,mBAAmB,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrD,WAAK,qBAAqB;AAC1B,WAAK,qBAAqB;IAC9B,CAAC;AAED,UAAM,KAAK,WAAW,MAAM,KAAK,UAAU,cAAc;AAEzD,QAAI;AACA,YAAM,mBAA4C;QAC9C,UAAU,KAAK,UAAU;QACzB,SAAS,KAAK,UAAU;;AAG5B,WAAK,QAAQ,IAAI,SAAS,OAAO,4BAA4B;AAE7D,YAAM,KAAK,aAAa,KAAK,mBAAmB,sBAAsB,gBAAgB,CAAC;AAEvF,WAAK,QAAQ,IAAI,SAAS,aAAa,sBAAsB,KAAK,UAAU,IAAI,IAAI;AAGpF,WAAK,gBAAe;AACpB,WAAK,oBAAmB;AACxB,WAAK,wBAAuB;AAE5B,YAAM;AAKN,UAAI,KAAK,uBAAuB;AAK5B,cAAM,KAAK;;AAGf,UAAI,CAAC,KAAK,WAAW,SAAS,mBAAmB;AAC7C,cAAM,KAAK,aAAa,KAAK,kBAAkB;;aAE9C,GAAG;AACR,WAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC,CAAC,2CAA2C;AAEjH,WAAK,gBAAe;AACpB,WAAK,kBAAiB;AAItB,YAAM,KAAK,WAAW,KAAK,CAAC;AAC5B,YAAM;;EAEd;;;;;EAMO,MAAM,OAAI;AAEb,UAAM,eAAe,KAAK;AAE1B,SAAK,eAAe,KAAK,cAAa;AACtC,UAAM,KAAK;AAEX,QAAI;AAEA,YAAM;aACD,GAAG;;EAGhB;EAEQ,cAAc,OAAa;AAC/B,QAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC3D,WAAK,QAAQ,IAAI,SAAS,OAAO,8BAA8B,KAAK,4DAA4D;AAChI,aAAO,QAAQ,QAAO;;AAG1B,QAAI,KAAK,qBAAqB,mBAAmB,eAAe;AAC5D,WAAK,QAAQ,IAAI,SAAS,OAAO,+BAA+B,KAAK,yEAAyE;AAC9I,aAAO,KAAK;;AAGhB,SAAK,mBAAmB,mBAAmB;AAE3C,SAAK,QAAQ,IAAI,SAAS,OAAO,yBAAyB;AAE1D,QAAI,KAAK,uBAAuB;AAI5B,WAAK,QAAQ,IAAI,SAAS,OAAO,+DAA+D;AAEhG,mBAAa,KAAK,qBAAqB;AACvC,WAAK,wBAAwB;AAE7B,WAAK,eAAc;AACnB,aAAO,QAAQ,QAAO;;AAG1B,SAAK,gBAAe;AACpB,SAAK,kBAAiB;AACtB,SAAK,wBAAwB,SAAS,IAAI,WAAW,qEAAqE;AAK1H,WAAO,KAAK,WAAW,KAAK,KAAK;EACrC;;;;;;;;EASO,OAAgB,eAAuB,MAAW;AACrD,UAAM,CAAC,SAAS,SAAS,IAAI,KAAK,wBAAwB,IAAI;AAC9D,UAAM,uBAAuB,KAAK,wBAAwB,YAAY,MAAM,SAAS;AAGrF,QAAI;AAEJ,UAAM,UAAU,IAAI,QAAO;AAC3B,YAAQ,iBAAiB,MAAK;AAC1B,YAAM,mBAA4C,KAAK,wBAAwB,qBAAqB,YAAY;AAEhH,aAAO,KAAK,WAAW,qBAAqB,YAAY;AAExD,aAAO,aAAa,KAAK,MAAK;AAC1B,eAAO,KAAK,kBAAkB,gBAAgB;MAClD,CAAC;IACL;AAEA,SAAK,WAAW,qBAAqB,YAAY,IAAI,CAAC,iBAA+D,UAAiB;AAClI,UAAI,OAAO;AACP,gBAAQ,MAAM,KAAK;AACnB;iBACO,iBAAiB;AAExB,YAAI,gBAAgB,SAAS,YAAY,YAAY;AACjD,cAAI,gBAAgB,OAAO;AACvB,oBAAQ,MAAM,IAAI,MAAM,gBAAgB,KAAK,CAAC;iBAC3C;AACH,oBAAQ,SAAQ;;eAEjB;AACH,kBAAQ,KAAM,gBAAgB,IAAU;;;IAGpD;AAEA,mBAAe,KAAK,kBAAkB,oBAAoB,EACrD,MAAM,CAAC,MAAK;AACT,cAAQ,MAAM,CAAC;AACf,aAAO,KAAK,WAAW,qBAAqB,YAAY;IAC5D,CAAC;AAEL,SAAK,eAAe,SAAS,YAAY;AAEzC,WAAO;EACX;EAEQ,aAAa,SAAY;AAC7B,SAAK,wBAAuB;AAC5B,WAAO,KAAK,WAAW,KAAK,OAAO;EACvC;;;;;EAMQ,kBAAkB,SAAY;AAClC,WAAO,KAAK,aAAa,KAAK,UAAU,aAAa,OAAO,CAAC;EACjE;;;;;;;;;;EAWO,KAAK,eAAuB,MAAW;AAC1C,UAAM,CAAC,SAAS,SAAS,IAAI,KAAK,wBAAwB,IAAI;AAC9D,UAAM,cAAc,KAAK,kBAAkB,KAAK,kBAAkB,YAAY,MAAM,MAAM,SAAS,CAAC;AAEpG,SAAK,eAAe,SAAS,WAAW;AAExC,WAAO;EACX;;;;;;;;;;;;EAaO,OAAgB,eAAuB,MAAW;AACrD,UAAM,CAAC,SAAS,SAAS,IAAI,KAAK,wBAAwB,IAAI;AAC9D,UAAM,uBAAuB,KAAK,kBAAkB,YAAY,MAAM,OAAO,SAAS;AAEtF,UAAM,IAAI,IAAI,QAAa,CAAC,SAAS,WAAU;AAE3C,WAAK,WAAW,qBAAqB,YAAa,IAAI,CAAC,iBAA+D,UAAiB;AACnI,YAAI,OAAO;AACP,iBAAO,KAAK;AACZ;mBACO,iBAAiB;AAExB,cAAI,gBAAgB,SAAS,YAAY,YAAY;AACjD,gBAAI,gBAAgB,OAAO;AACvB,qBAAO,IAAI,MAAM,gBAAgB,KAAK,CAAC;mBACpC;AACH,sBAAQ,gBAAgB,MAAM;;iBAE/B;AACH,mBAAO,IAAI,MAAM,4BAA4B,gBAAgB,IAAI,EAAE,CAAC;;;MAGhF;AAEA,YAAM,eAAe,KAAK,kBAAkB,oBAAoB,EAC3D,MAAM,CAAC,MAAK;AACT,eAAO,CAAC;AAER,eAAO,KAAK,WAAW,qBAAqB,YAAa;MAC7D,CAAC;AAEL,WAAK,eAAe,SAAS,YAAY;IAC7C,CAAC;AAED,WAAO;EACX;EAQO,GAAG,YAAoB,WAAmC;AAC7D,QAAI,CAAC,cAAc,CAAC,WAAW;AAC3B;;AAGJ,iBAAa,WAAW,YAAW;AACnC,QAAI,CAAC,KAAK,SAAS,UAAU,GAAG;AAC5B,WAAK,SAAS,UAAU,IAAI,CAAA;;AAIhC,QAAI,KAAK,SAAS,UAAU,EAAE,QAAQ,SAAS,MAAM,IAAI;AACrD;;AAGJ,SAAK,SAAS,UAAU,EAAE,KAAK,SAAS;EAC5C;EAiBO,IAAI,YAAoB,QAAiC;AAC5D,QAAI,CAAC,YAAY;AACb;;AAGJ,iBAAa,WAAW,YAAW;AACnC,UAAM,WAAW,KAAK,SAAS,UAAU;AACzC,QAAI,CAAC,UAAU;AACX;;AAEJ,QAAI,QAAQ;AACR,YAAM,YAAY,SAAS,QAAQ,MAAM;AACzC,UAAI,cAAc,IAAI;AAClB,iBAAS,OAAO,WAAW,CAAC;AAC5B,YAAI,SAAS,WAAW,GAAG;AACvB,iBAAO,KAAK,SAAS,UAAU;;;WAGpC;AACH,aAAO,KAAK,SAAS,UAAU;;EAGvC;;;;;EAMO,QAAQ,UAAiC;AAC5C,QAAI,UAAU;AACV,WAAK,iBAAiB,KAAK,QAAQ;;EAE3C;;;;;EAMO,eAAe,UAAiC;AACnD,QAAI,UAAU;AACV,WAAK,uBAAuB,KAAK,QAAQ;;EAEjD;;;;;EAMO,cAAc,UAAyC;AAC1D,QAAI,UAAU;AACV,WAAK,sBAAsB,KAAK,QAAQ;;EAEhD;EAEQ,qBAAqB,MAAS;AAClC,SAAK,gBAAe;AAEpB,QAAI,CAAC,KAAK,4BAA4B;AAClC,aAAO,KAAK,0BAA0B,IAAI;AAC1C,WAAK,6BAA6B;;AAItC,QAAI,MAAM;AAEN,YAAM,WAAW,KAAK,UAAU,cAAc,MAAM,KAAK,OAAO;AAEhE,iBAAW,WAAW,UAAU;AAC5B,gBAAQ,QAAQ,MAAM;UAClB,KAAK,YAAY;AAEb,iBAAK,oBAAoB,OAAO;AAChC;UACJ,KAAK,YAAY;UACjB,KAAK,YAAY,YAAY;AACzB,kBAAM,WAAW,KAAK,WAAW,QAAQ,YAAY;AACrD,gBAAI,UAAU;AACV,kBAAI,QAAQ,SAAS,YAAY,YAAY;AACzC,uBAAO,KAAK,WAAW,QAAQ,YAAY;;AAE/C,kBAAI;AACA,yBAAS,OAAO;uBACX,GAAG;AACR,qBAAK,QAAQ,IAAI,SAAS,OAAO,gCAAgC,eAAe,CAAC,CAAC,EAAE;;;AAG5F;;UAEJ,KAAK,YAAY;AAEb;UACJ,KAAK,YAAY,OAAO;AACpB,iBAAK,QAAQ,IAAI,SAAS,aAAa,qCAAqC;AAE5E,kBAAM,QAAQ,QAAQ,QAAQ,IAAI,MAAM,wCAAwC,QAAQ,KAAK,IAAI;AAEjG,gBAAI,QAAQ,mBAAmB,MAAM;AAKjC,mBAAK,WAAW,KAAK,KAAK;mBACvB;AAEH,mBAAK,eAAe,KAAK,cAAc,KAAK;;AAGhD;;UAEJ;AACI,iBAAK,QAAQ,IAAI,SAAS,SAAS,yBAAyB,QAAQ,IAAI,GAAG;AAC3E;;;;AAKhB,SAAK,oBAAmB;EAC5B;EAEQ,0BAA0B,MAAS;AACvC,QAAI;AACJ,QAAI;AAEJ,QAAI;AACA,OAAC,eAAe,eAAe,IAAI,KAAK,mBAAmB,uBAAuB,IAAI;aACjF,GAAG;AACR,YAAM,UAAU,uCAAuC;AACvD,WAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AAExC,YAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,WAAK,mBAAmB,KAAK;AAC7B,YAAM;;AAEV,QAAI,gBAAgB,OAAO;AACvB,YAAM,UAAU,sCAAsC,gBAAgB;AACtE,WAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AAExC,YAAM,QAAQ,IAAI,MAAM,OAAO;AAC/B,WAAK,mBAAmB,KAAK;AAC7B,YAAM;WACH;AACH,WAAK,QAAQ,IAAI,SAAS,OAAO,4BAA4B;;AAGjE,SAAK,mBAAkB;AACvB,WAAO;EACX;EAEQ,0BAAuB;AAC3B,QAAI,KAAK,WAAW,SAAS,mBAAmB;AAC5C;;AAKJ,SAAK,kBAAiB,oBAAI,KAAI,GAAG,QAAO,IAAK,KAAK;AAElD,SAAK,kBAAiB;EAC1B;EAEQ,sBAAmB;AACvB,QAAI,CAAC,KAAK,WAAW,YAAY,CAAC,KAAK,WAAW,SAAS,mBAAmB;AAE1E,WAAK,iBAAiB,WAAW,MAAM,KAAK,cAAa,GAAI,KAAK,2BAA2B;AAG7F,UAAI,KAAK,sBAAsB,QAC/B;AACI,YAAI,WAAW,KAAK,kBAAiB,oBAAI,KAAI,GAAG,QAAO;AACvD,YAAI,WAAW,GAAG;AACd,qBAAW;;AAIf,aAAK,oBAAoB,WAAW,YAAW;AAC3C,cAAI,KAAK,qBAAqB,mBAAmB,WAAW;AACxD,gBAAI;AACA,oBAAM,KAAK,aAAa,KAAK,kBAAkB;oBAC3C;AAGJ,mBAAK,kBAAiB;;;QAGlC,GAAG,QAAQ;;;EAGvB;;EAGQ,gBAAa;AAIjB,SAAK,WAAW,KAAK,IAAI,MAAM,qEAAqE,CAAC;EACzG;EAEQ,MAAM,oBAAoB,mBAAoC;AAClE,UAAM,aAAa,kBAAkB,OAAO,YAAW;AACvD,UAAM,UAAU,KAAK,SAAS,UAAU;AACxC,QAAI,CAAC,SAAS;AACV,WAAK,QAAQ,IAAI,SAAS,SAAS,mCAAmC,UAAU,UAAU;AAG1F,UAAI,kBAAkB,cAAc;AAChC,aAAK,QAAQ,IAAI,SAAS,SAAS,wBAAwB,UAAU,+BAA+B,kBAAkB,YAAY,IAAI;AACtI,cAAM,KAAK,kBAAkB,KAAK,yBAAyB,kBAAkB,cAAc,mCAAmC,IAAI,CAAC;;AAEvI;;AAIJ,UAAM,cAAc,QAAQ,MAAK;AAGjC,UAAM,kBAAkB,kBAAkB,eAAe,OAAO;AAEhE,QAAI;AACJ,QAAI;AACJ,QAAI;AACJ,eAAW,KAAK,aAAa;AACzB,UAAI;AACA,cAAM,UAAU;AAChB,cAAM,MAAM,EAAE,MAAM,MAAM,kBAAkB,SAAS;AACrD,YAAI,mBAAmB,OAAO,SAAS;AACnC,eAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,UAAU,6BAA6B;AAC1G,8BAAoB,KAAK,yBAAyB,kBAAkB,cAAe,qCAAqC,IAAI;;AAGhI,oBAAY;eACP,GAAG;AACR,oBAAY;AACZ,aAAK,QAAQ,IAAI,SAAS,OAAO,8BAA8B,UAAU,kBAAkB,CAAC,IAAI;;;AAGxG,QAAI,mBAAmB;AACnB,YAAM,KAAK,kBAAkB,iBAAiB;eACvC,iBAAiB;AAExB,UAAI,WAAW;AACX,4BAAoB,KAAK,yBAAyB,kBAAkB,cAAe,GAAG,SAAS,IAAI,IAAI;iBAChG,QAAQ,QAAW;AAC1B,4BAAoB,KAAK,yBAAyB,kBAAkB,cAAe,MAAM,GAAG;aACzF;AACH,aAAK,QAAQ,IAAI,SAAS,SAAS,wBAAwB,UAAU,+BAA+B,kBAAkB,YAAY,IAAI;AAEtI,4BAAoB,KAAK,yBAAyB,kBAAkB,cAAe,mCAAmC,IAAI;;AAE9H,YAAM,KAAK,kBAAkB,iBAAiB;WAC3C;AACH,UAAI,KAAK;AACL,aAAK,QAAQ,IAAI,SAAS,OAAO,qBAAqB,UAAU,gDAAgD;;;EAG5H;EAEQ,kBAAkB,OAAa;AACnC,SAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,KAAK,2BAA2B,KAAK,gBAAgB,GAAG;AAG3H,SAAK,wBAAwB,KAAK,yBAAyB,SAAS,IAAI,WAAW,+EAA+E;AAIlK,QAAI,KAAK,oBAAoB;AACzB,WAAK,mBAAkB;;AAG3B,SAAK,0BAA0B,SAAS,IAAI,MAAM,oEAAoE,CAAC;AAEvH,SAAK,gBAAe;AACpB,SAAK,kBAAiB;AAEtB,QAAI,KAAK,qBAAqB,mBAAmB,eAAe;AAC5D,WAAK,eAAe,KAAK;eAClB,KAAK,qBAAqB,mBAAmB,aAAa,KAAK,kBAAkB;AAExF,WAAK,WAAW,KAAK;eACd,KAAK,qBAAqB,mBAAmB,WAAW;AAC/D,WAAK,eAAe,KAAK;;EAQjC;EAEQ,eAAe,OAAa;AAChC,QAAI,KAAK,oBAAoB;AACzB,WAAK,mBAAmB,mBAAmB;AAC3C,WAAK,qBAAqB;AAE1B,UAAI,SAAS,WAAW;AACpB,eAAO,SAAS,oBAAoB,UAAU,KAAK,oBAAoB;;AAG3E,UAAI;AACA,aAAK,iBAAiB,QAAQ,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC;eACtD,GAAG;AACR,aAAK,QAAQ,IAAI,SAAS,OAAO,0CAA0C,KAAK,kBAAkB,CAAC,IAAI;;;EAGnH;EAEQ,MAAM,WAAW,OAAa;AAClC,UAAM,qBAAqB,KAAK,IAAG;AACnC,QAAI,4BAA4B;AAChC,QAAI,aAAa,UAAU,SAAY,QAAQ,IAAI,MAAM,iDAAiD;AAE1G,QAAI,iBAAiB,KAAK,mBAAmB,6BAA6B,GAAG,UAAU;AAEvF,QAAI,mBAAmB,MAAM;AACzB,WAAK,QAAQ,IAAI,SAAS,OAAO,oGAAoG;AACrI,WAAK,eAAe,KAAK;AACzB;;AAGJ,SAAK,mBAAmB,mBAAmB;AAE3C,QAAI,OAAO;AACP,WAAK,QAAQ,IAAI,SAAS,aAAa,6CAA6C,KAAK,IAAI;WAC1F;AACH,WAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B;;AAGrE,QAAI,KAAK,uBAAuB,WAAW,GAAG;AAC1C,UAAI;AACA,aAAK,uBAAuB,QAAQ,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,KAAK,CAAC,CAAC;eAC5D,GAAG;AACR,aAAK,QAAQ,IAAI,SAAS,OAAO,iDAAiD,KAAK,kBAAkB,CAAC,IAAI;;AAIlH,UAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC3D,aAAK,QAAQ,IAAI,SAAS,OAAO,uFAAuF;AACxH;;;AAIR,WAAO,mBAAmB,MAAM;AAC5B,WAAK,QAAQ,IAAI,SAAS,aAAa,4BAA4B,yBAAyB,kBAAkB,cAAc,MAAM;AAElI,YAAM,IAAI,QAAQ,CAAC,YAAW;AAC1B,aAAK,wBAAwB,WAAW,SAAS,cAAe;MACpE,CAAC;AACD,WAAK,wBAAwB;AAE7B,UAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC3D,aAAK,QAAQ,IAAI,SAAS,OAAO,mFAAmF;AACpH;;AAGJ,UAAI;AACA,cAAM,KAAK,eAAc;AAEzB,aAAK,mBAAmB,mBAAmB;AAC3C,aAAK,QAAQ,IAAI,SAAS,aAAa,yCAAyC;AAEhF,YAAI,KAAK,sBAAsB,WAAW,GAAG;AACzC,cAAI;AACA,iBAAK,sBAAsB,QAAQ,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC,KAAK,WAAW,YAAY,CAAC,CAAC;mBAClF,GAAG;AACR,iBAAK,QAAQ,IAAI,SAAS,OAAO,uDAAuD,KAAK,WAAW,YAAY,kBAAkB,CAAC,IAAI;;;AAInJ;eACK,GAAG;AACR,aAAK,QAAQ,IAAI,SAAS,aAAa,8CAA8C,CAAC,IAAI;AAE1F,YAAI,KAAK,qBAAqB,mBAAmB,cAAc;AAC3D,eAAK,QAAQ,IAAI,SAAS,OAAO,4BAA4B,KAAK,gBAAgB,4EAA4E;AAE9J,cAAI,KAAK,qBAA4B,mBAAmB,eAAe;AACnE,iBAAK,eAAc;;AAEvB;;AAGJ,qBAAa,aAAa,QAAQ,IAAI,IAAI,MAAM,EAAE,SAAQ,CAAE;AAC5D,yBAAiB,KAAK,mBAAmB,6BAA6B,KAAK,IAAG,IAAK,oBAAoB,UAAU;;;AAIzH,SAAK,QAAQ,IAAI,SAAS,aAAa,+CAA+C,KAAK,IAAG,IAAK,kBAAkB,WAAW,yBAAyB,6CAA6C;AAEtM,SAAK,eAAc;EACvB;EAEQ,mBAAmB,oBAA4B,qBAA6B,aAAkB;AAClG,QAAI;AACA,aAAO,KAAK,iBAAkB,6BAA6B;QACvD;QACA;QACA;OACH;aACI,GAAG;AACR,WAAK,QAAQ,IAAI,SAAS,OAAO,6CAA6C,kBAAkB,KAAK,mBAAmB,kBAAkB,CAAC,IAAI;AAC/I,aAAO;;EAEf;EAEQ,0BAA0B,OAAY;AAC1C,UAAM,YAAY,KAAK;AACvB,SAAK,aAAa,CAAA;AAElB,WAAO,KAAK,SAAS,EAChB,QAAQ,CAAC,QAAO;AACb,YAAM,WAAW,UAAU,GAAG;AAC9B,UAAI;AACA,iBAAS,MAAM,KAAK;eACf,GAAG;AACR,aAAK,QAAQ,IAAI,SAAS,OAAO,wCAAwC,KAAK,kBAAkB,eAAe,CAAC,CAAC,EAAE;;IAE3H,CAAC;EACT;EAEQ,oBAAiB;AACrB,QAAI,KAAK,mBAAmB;AACxB,mBAAa,KAAK,iBAAiB;AACnC,WAAK,oBAAoB;;EAEjC;EAEQ,kBAAe;AACnB,QAAI,KAAK,gBAAgB;AACrB,mBAAa,KAAK,cAAc;;EAExC;EAEQ,kBAAkB,YAAoB,MAAa,aAAsB,WAAmB;AAChG,QAAI,aAAa;AACb,UAAI,UAAU,WAAW,GAAG;AACxB,eAAO;UACH,WAAW;UACX;UACA,QAAQ;UACR,MAAM,YAAY;;aAEnB;AACH,eAAO;UACH,WAAW;UACX,QAAQ;UACR,MAAM,YAAY;;;WAGvB;AACH,YAAM,eAAe,KAAK;AAC1B,WAAK;AAEL,UAAI,UAAU,WAAW,GAAG;AACxB,eAAO;UACH,WAAW;UACX,cAAc,aAAa,SAAQ;UACnC;UACA,QAAQ;UACR,MAAM,YAAY;;aAEnB;AACH,eAAO;UACH,WAAW;UACX,cAAc,aAAa,SAAQ;UACnC,QAAQ;UACR,MAAM,YAAY;;;;EAIlC;EAEQ,eAAe,SAA+B,cAA2B;AAC7E,QAAI,QAAQ,WAAW,GAAG;AACtB;;AAIJ,QAAI,CAAC,cAAc;AACf,qBAAe,QAAQ,QAAO;;AAKlC,eAAW,YAAY,SAAS;AAC5B,cAAQ,QAAQ,EAAE,UAAU;QACxB,UAAU,MAAK;AACX,yBAAe,aAAa,KAAK,MAAM,KAAK,kBAAkB,KAAK,yBAAyB,QAAQ,CAAC,CAAC;QAC1G;QACA,OAAO,CAAC,QAAO;AACX,cAAI;AACJ,cAAI,eAAe,OAAO;AACtB,sBAAU,IAAI;qBACP,OAAO,IAAI,UAAU;AAC5B,sBAAU,IAAI,SAAQ;iBACnB;AACH,sBAAU;;AAGd,yBAAe,aAAa,KAAK,MAAM,KAAK,kBAAkB,KAAK,yBAAyB,UAAU,OAAO,CAAC,CAAC;QACnH;QACA,MAAM,CAAC,SAAQ;AACX,yBAAe,aAAa,KAAK,MAAM,KAAK,kBAAkB,KAAK,yBAAyB,UAAU,IAAI,CAAC,CAAC;QAChH;OACH;;EAET;EAEQ,wBAAwB,MAAW;AACvC,UAAM,UAAgC,CAAA;AACtC,UAAM,YAAsB,CAAA;AAC5B,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClC,YAAM,WAAW,KAAK,CAAC;AACvB,UAAI,KAAK,cAAc,QAAQ,GAAG;AAC9B,cAAM,WAAW,KAAK;AACtB,aAAK;AAEL,gBAAQ,QAAQ,IAAI;AACpB,kBAAU,KAAK,SAAS,SAAQ,CAAE;AAGlC,aAAK,OAAO,GAAG,CAAC;;;AAIxB,WAAO,CAAC,SAAS,SAAS;EAC9B;EAEQ,cAAc,KAAQ;AAE1B,WAAO,OAAO,IAAI,aAAa,OAAO,IAAI,cAAc;EAC5D;EAEQ,wBAAwB,YAAoB,MAAa,WAAmB;AAChF,UAAM,eAAe,KAAK;AAC1B,SAAK;AAEL,QAAI,UAAU,WAAW,GAAG;AACxB,aAAO;QACH,WAAW;QACX,cAAc,aAAa,SAAQ;QACnC;QACA,QAAQ;QACR,MAAM,YAAY;;WAEnB;AACH,aAAO;QACH,WAAW;QACX,cAAc,aAAa,SAAQ;QACnC,QAAQ;QACR,MAAM,YAAY;;;EAG9B;EAEQ,wBAAwB,IAAU;AACtC,WAAO;MACH,cAAc;MACd,MAAM,YAAY;;EAE1B;EAEQ,yBAAyB,IAAY,MAAS;AAClD,WAAO;MACH,cAAc;MACd;MACA,MAAM,YAAY;;EAE1B;EAEQ,yBAAyB,IAAY,OAAa,QAAY;AAClE,QAAI,OAAO;AACP,aAAO;QACH;QACA,cAAc;QACd,MAAM,YAAY;;;AAI1B,WAAO;MACH,cAAc;MACd;MACA,MAAM,YAAY;;EAE1B;;;;ACpiCJ,IAAM,uCAAuC,CAAC,GAAG,KAAM,KAAO,KAAO,IAAI;AAGnE,IAAO,yBAAP,MAA6B;EAG/B,YAAY,aAAsB;AAC9B,SAAK,eAAe,gBAAgB,SAAY,CAAC,GAAG,aAAa,IAAI,IAAI;EAC7E;EAEO,6BAA6B,cAA0B;AAC1D,WAAO,KAAK,aAAa,aAAa,kBAAkB;EAC5D;;;;ACfE,IAAgB,cAAhB,MAA2B;;AACb,YAAA,gBAAgB;AAChB,YAAA,SAAS;;;ACEvB,IAAO,wBAAP,cAAqC,WAAU;EAKjD,YAAY,aAAyB,oBAAgE;AACjG,UAAK;AAEL,SAAK,eAAe;AACpB,SAAK,sBAAsB;EAC/B;EAEO,MAAM,KAAK,SAAoB;AAClC,QAAI,aAAa;AACjB,QAAI,KAAK,wBAAwB,CAAC,KAAK,gBAAiB,QAAQ,OAAO,QAAQ,IAAI,QAAQ,aAAa,IAAI,IAAK;AAE7G,mBAAa;AACb,WAAK,eAAe,MAAM,KAAK,oBAAmB;;AAEtD,SAAK,wBAAwB,OAAO;AACpC,UAAM,WAAW,MAAM,KAAK,aAAa,KAAK,OAAO;AAErD,QAAI,cAAc,SAAS,eAAe,OAAO,KAAK,qBAAqB;AACvE,WAAK,eAAe,MAAM,KAAK,oBAAmB;AAClD,WAAK,wBAAwB,OAAO;AACpC,aAAO,MAAM,KAAK,aAAa,KAAK,OAAO;;AAE/C,WAAO;EACX;EAEQ,wBAAwB,SAAoB;AAChD,QAAI,CAAC,QAAQ,SAAS;AAClB,cAAQ,UAAU,CAAA;;AAEtB,QAAI,KAAK,cAAc;AACnB,cAAQ,QAAQ,YAAY,aAAa,IAAI,UAAU,KAAK,YAAY;eAGnE,KAAK,qBAAqB;AAC/B,UAAI,QAAQ,QAAQ,YAAY,aAAa,GAAG;AAC5C,eAAO,QAAQ,QAAQ,YAAY,aAAa;;;EAG5D;EAEO,gBAAgB,KAAW;AAC9B,WAAO,KAAK,aAAa,gBAAgB,GAAG;EAChD;;;;ACjDJ,IAAY;CAAZ,SAAYC,oBAAiB;AAEzB,EAAAA,mBAAAA,mBAAA,MAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,mBAAAA,mBAAA,YAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,mBAAAA,mBAAA,kBAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,mBAAAA,mBAAA,aAAA,IAAA,CAAA,IAAA;AACJ,GATY,sBAAA,oBAAiB,CAAA,EAAA;AAY7B,IAAY;CAAZ,SAAYC,iBAAc;AAEtB,EAAAA,gBAAAA,gBAAA,MAAA,IAAA,CAAA,IAAA;AAEA,EAAAA,gBAAAA,gBAAA,QAAA,IAAA,CAAA,IAAA;AACJ,GALY,mBAAA,iBAAc,CAAA,EAAA;;;ACRpB,IAAOC,mBAAP,MAAsB;EAA5B,cAAA;AACY,SAAA,aAAsB;AACvB,SAAA,UAA+B;EAkB1C;EAhBW,QAAK;AACR,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa;AAClB,UAAI,KAAK,SAAS;AACd,aAAK,QAAO;;;EAGxB;EAEA,IAAI,SAAM;AACN,WAAO;EACX;EAEA,IAAI,UAAO;AACP,WAAO,KAAK;EAChB;;;;ACfE,IAAO,uBAAP,MAA2B;EAmB7B,YAAY,YAAwB,QAAiB,SAA+B;AAChF,SAAK,cAAc;AACnB,SAAK,UAAU;AACf,SAAK,aAAa,IAAIC,iBAAe;AACrC,SAAK,WAAW;AAEhB,SAAK,WAAW;AAEhB,SAAK,YAAY;AACjB,SAAK,UAAU;EACnB;;EAdA,IAAW,cAAW;AAClB,WAAO,KAAK,WAAW;EAC3B;EAcO,MAAM,QAAQ,KAAa,gBAA8B;AAC5D,QAAI,WAAW,KAAK,KAAK;AACzB,QAAI,WAAW,gBAAgB,gBAAgB;AAC/C,QAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AAEzD,SAAK,OAAO;AAEZ,SAAK,QAAQ,IAAI,SAAS,OAAO,qCAAqC;AAGtE,QAAI,mBAAmB,eAAe,WACjC,OAAO,mBAAmB,eAAe,OAAO,IAAI,eAAc,EAAG,iBAAiB,WAAW;AAClG,YAAM,IAAI,MAAM,4FAA4F;;AAGhH,UAAM,CAAC,MAAM,KAAK,IAAI,mBAAkB;AACxC,UAAM,UAAU,EAAE,CAAC,IAAI,GAAG,OAAO,GAAG,KAAK,SAAS,QAAO;AAEzD,UAAM,cAA2B;MAC7B,aAAa,KAAK,WAAW;MAC7B;MACA,SAAS;MACT,iBAAiB,KAAK,SAAS;;AAGnC,QAAI,mBAAmB,eAAe,QAAQ;AAC1C,kBAAY,eAAe;;AAK/B,UAAM,UAAU,GAAG,GAAG,MAAM,KAAK,IAAG,CAAE;AACtC,SAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC,OAAO,GAAG;AAC/E,UAAM,WAAW,MAAM,KAAK,YAAY,IAAI,SAAS,WAAW;AAChE,QAAI,SAAS,eAAe,KAAK;AAC7B,WAAK,QAAQ,IAAI,SAAS,OAAO,qDAAqD,SAAS,UAAU,GAAG;AAG5G,WAAK,cAAc,IAAI,UAAU,SAAS,cAAc,IAAI,SAAS,UAAU;AAC/E,WAAK,WAAW;WACb;AACH,WAAK,WAAW;;AAGpB,SAAK,aAAa,KAAK,MAAM,KAAK,MAAM,WAAW;EACvD;EAEQ,MAAM,MAAM,KAAa,aAAwB;AACrD,QAAI;AACA,aAAO,KAAK,UAAU;AAClB,YAAI;AACA,gBAAM,UAAU,GAAG,GAAG,MAAM,KAAK,IAAG,CAAE;AACtC,eAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC,OAAO,GAAG;AAC/E,gBAAM,WAAW,MAAM,KAAK,YAAY,IAAI,SAAS,WAAW;AAEhE,cAAI,SAAS,eAAe,KAAK;AAC7B,iBAAK,QAAQ,IAAI,SAAS,aAAa,oDAAoD;AAE3F,iBAAK,WAAW;qBACT,SAAS,eAAe,KAAK;AACpC,iBAAK,QAAQ,IAAI,SAAS,OAAO,qDAAqD,SAAS,UAAU,GAAG;AAG5G,iBAAK,cAAc,IAAI,UAAU,SAAS,cAAc,IAAI,SAAS,UAAU;AAC/E,iBAAK,WAAW;iBACb;AAEH,gBAAI,SAAS,SAAS;AAClB,mBAAK,QAAQ,IAAI,SAAS,OAAO,0CAA0C,cAAc,SAAS,SAAS,KAAK,SAAS,iBAAkB,CAAC,GAAG;AAC/I,kBAAI,KAAK,WAAW;AAChB,qBAAK,UAAU,SAAS,OAAO;;mBAEhC;AAEH,mBAAK,QAAQ,IAAI,SAAS,OAAO,oDAAoD;;;iBAGxF,GAAG;AACR,cAAI,CAAC,KAAK,UAAU;AAEhB,iBAAK,QAAQ,IAAI,SAAS,OAAO,wDAAwD,EAAE,OAAO,EAAE;iBACjG;AACH,gBAAI,aAAa,cAAc;AAE3B,mBAAK,QAAQ,IAAI,SAAS,OAAO,oDAAoD;mBAClF;AAEH,mBAAK,cAAc;AACnB,mBAAK,WAAW;;;;;;AAMhC,WAAK,QAAQ,IAAI,SAAS,OAAO,2CAA2C;AAI5E,UAAI,CAAC,KAAK,aAAa;AACnB,aAAK,cAAa;;;EAG9B;EAEO,MAAM,KAAK,MAAS;AACvB,QAAI,CAAC,KAAK,UAAU;AAChB,aAAO,QAAQ,OAAO,IAAI,MAAM,8CAA8C,CAAC;;AAEnF,WAAO,YAAY,KAAK,SAAS,eAAe,KAAK,aAAa,KAAK,MAAO,MAAM,KAAK,QAAQ;EACrG;EAEO,MAAM,OAAI;AACb,SAAK,QAAQ,IAAI,SAAS,OAAO,2CAA2C;AAG5E,SAAK,WAAW;AAChB,SAAK,WAAW,MAAK;AAErB,QAAI;AACA,YAAM,KAAK;AAGX,WAAK,QAAQ,IAAI,SAAS,OAAO,qDAAqD,KAAK,IAAI,GAAG;AAElG,YAAM,UAAiC,CAAA;AACvC,YAAM,CAAC,MAAM,KAAK,IAAI,mBAAkB;AACxC,cAAQ,IAAI,IAAI;AAEhB,YAAM,gBAA6B;QAC/B,SAAS,EAAE,GAAG,SAAS,GAAG,KAAK,SAAS,QAAO;QAC/C,SAAS,KAAK,SAAS;QACvB,iBAAiB,KAAK,SAAS;;AAEnC,YAAM,KAAK,YAAY,OAAO,KAAK,MAAO,aAAa;AAEvD,WAAK,QAAQ,IAAI,SAAS,OAAO,8CAA8C;;AAE/E,WAAK,QAAQ,IAAI,SAAS,OAAO,wCAAwC;AAIzE,WAAK,cAAa;;EAE1B;EAEQ,gBAAa;AACjB,QAAI,KAAK,SAAS;AACd,UAAI,aAAa;AACjB,UAAI,KAAK,aAAa;AAClB,sBAAc,aAAa,KAAK;;AAEpC,WAAK,QAAQ,IAAI,SAAS,OAAO,UAAU;AAC3C,WAAK,QAAQ,KAAK,WAAW;;EAErC;;;;AC3LE,IAAO,4BAAP,MAAgC;EAWlC,YAAY,YAAwB,aAAiC,QACzD,SAA+B;AACvC,SAAK,cAAc;AACnB,SAAK,eAAe;AACpB,SAAK,UAAU;AACf,SAAK,WAAW;AAEhB,SAAK,YAAY;AACjB,SAAK,UAAU;EACnB;EAEO,MAAM,QAAQ,KAAa,gBAA8B;AAC5D,QAAI,WAAW,KAAK,KAAK;AACzB,QAAI,WAAW,gBAAgB,gBAAgB;AAC/C,QAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AAEzD,SAAK,QAAQ,IAAI,SAAS,OAAO,6BAA6B;AAG9D,SAAK,OAAO;AAEZ,QAAI,KAAK,cAAc;AACnB,cAAQ,IAAI,QAAQ,GAAG,IAAI,IAAI,MAAM,OAAO,gBAAgB,mBAAmB,KAAK,YAAY,CAAC;;AAGrG,WAAO,IAAI,QAAc,CAAC,SAAS,WAAU;AACzC,UAAI,SAAS;AACb,UAAI,mBAAmB,eAAe,MAAM;AACxC,eAAO,IAAI,MAAM,2EAA2E,CAAC;AAC7F;;AAGJ,UAAI;AACJ,UAAI,SAAS,aAAa,SAAS,aAAa;AAC5C,sBAAc,IAAI,KAAK,SAAS,YAAa,KAAK,EAAE,iBAAiB,KAAK,SAAS,gBAAe,CAAE;aACjG;AAEH,cAAM,UAAU,KAAK,YAAY,gBAAgB,GAAG;AACpD,cAAM,UAA0B,CAAA;AAChC,gBAAQ,SAAS;AACjB,cAAM,CAAC,MAAM,KAAK,IAAI,mBAAkB;AACxC,gBAAQ,IAAI,IAAI;AAEhB,sBAAc,IAAI,KAAK,SAAS,YAAa,KAAK,EAAE,iBAAiB,KAAK,SAAS,iBAAiB,SAAS,EAAE,GAAG,SAAS,GAAG,KAAK,SAAS,QAAO,EAAC,CAAqB;;AAG7K,UAAI;AACA,oBAAY,YAAY,CAAC,MAAmB;AACxC,cAAI,KAAK,WAAW;AAChB,gBAAI;AACA,mBAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,cAAc,EAAE,MAAM,KAAK,SAAS,iBAAkB,CAAC,GAAG;AAC7H,mBAAK,UAAU,EAAE,IAAI;qBAChB,OAAO;AACZ,mBAAK,OAAO,KAAK;AACjB;;;QAGZ;AAGA,oBAAY,UAAU,CAAC,MAAY;AAE/B,cAAI,QAAQ;AACR,iBAAK,OAAM;iBACR;AACH,mBAAO,IAAI,MAAM,8PAEwD,CAAC;;QAElF;AAEA,oBAAY,SAAS,MAAK;AACtB,eAAK,QAAQ,IAAI,SAAS,aAAa,oBAAoB,KAAK,IAAI,EAAE;AACtE,eAAK,eAAe;AACpB,mBAAS;AACT,kBAAO;QACX;eACK,GAAG;AACR,eAAO,CAAC;AACR;;IAER,CAAC;EACL;EAEO,MAAM,KAAK,MAAS;AACvB,QAAI,CAAC,KAAK,cAAc;AACpB,aAAO,QAAQ,OAAO,IAAI,MAAM,8CAA8C,CAAC;;AAEnF,WAAO,YAAY,KAAK,SAAS,OAAO,KAAK,aAAa,KAAK,MAAO,MAAM,KAAK,QAAQ;EAC7F;EAEO,OAAI;AACP,SAAK,OAAM;AACX,WAAO,QAAQ,QAAO;EAC1B;EAEQ,OAAO,GAAS;AACpB,QAAI,KAAK,cAAc;AACnB,WAAK,aAAa,MAAK;AACvB,WAAK,eAAe;AAEpB,UAAI,KAAK,SAAS;AACd,aAAK,QAAQ,CAAC;;;EAG1B;;;;ACnHE,IAAO,qBAAP,MAAyB;EAY3B,YAAY,YAAwB,oBAAkE,QAC1F,mBAA4B,sBAA4C,SAAuB;AACvG,SAAK,UAAU;AACf,SAAK,sBAAsB;AAC3B,SAAK,qBAAqB;AAC1B,SAAK,wBAAwB;AAC7B,SAAK,cAAc;AAEnB,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,WAAW;EACpB;EAEO,MAAM,QAAQ,KAAa,gBAA8B;AAC5D,QAAI,WAAW,KAAK,KAAK;AACzB,QAAI,WAAW,gBAAgB,gBAAgB;AAC/C,QAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AACzD,SAAK,QAAQ,IAAI,SAAS,OAAO,oCAAoC;AAErE,QAAI;AACJ,QAAI,KAAK,qBAAqB;AAC1B,cAAQ,MAAM,KAAK,oBAAmB;;AAG1C,WAAO,IAAI,QAAc,CAAC,SAAS,WAAU;AACzC,YAAM,IAAI,QAAQ,SAAS,IAAI;AAC/B,UAAI;AACJ,YAAM,UAAU,KAAK,YAAY,gBAAgB,GAAG;AACpD,UAAI,SAAS;AAEb,UAAI,SAAS,UAAU,SAAS,eAAe;AAC3C,cAAM,UAAiC,CAAA;AACvC,cAAM,CAAC,MAAM,KAAK,IAAI,mBAAkB;AACxC,gBAAQ,IAAI,IAAI;AAChB,YAAI,OAAO;AACP,kBAAQ,YAAY,aAAa,IAAI,UAAU,KAAK;;AAGxD,YAAI,SAAS;AACT,kBAAQ,YAAY,MAAM,IAAI;;AAIlC,oBAAY,IAAI,KAAK,sBAAsB,KAAK,QAAW;UACvD,SAAS,EAAE,GAAG,SAAS,GAAG,KAAK,SAAQ;SAC1C;aAGL;AACI,YAAI,OAAO;AACP,kBAAQ,IAAI,QAAQ,GAAG,IAAI,IAAI,MAAM,OAAO,gBAAgB,mBAAmB,KAAK,CAAC;;;AAI7F,UAAI,CAAC,WAAW;AAEZ,oBAAY,IAAI,KAAK,sBAAsB,GAAG;;AAGlD,UAAI,mBAAmB,eAAe,QAAQ;AAC1C,kBAAU,aAAa;;AAG3B,gBAAU,SAAS,CAAC,WAAiB;AACjC,aAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B,GAAG,GAAG;AACvE,aAAK,aAAa;AAClB,iBAAS;AACT,gBAAO;MACX;AAEA,gBAAU,UAAU,CAAC,UAAgB;AACjC,YAAI,QAAa;AAEjB,YAAI,OAAO,eAAe,eAAe,iBAAiB,YAAY;AAClE,kBAAQ,MAAM;eACX;AACH,kBAAQ;;AAGZ,aAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B,KAAK,GAAG;MAC7E;AAEA,gBAAU,YAAY,CAAC,YAAyB;AAC5C,aAAK,QAAQ,IAAI,SAAS,OAAO,yCAAyC,cAAc,QAAQ,MAAM,KAAK,kBAAkB,CAAC,GAAG;AACjI,YAAI,KAAK,WAAW;AAChB,cAAI;AACA,iBAAK,UAAU,QAAQ,IAAI;mBACtB,OAAO;AACZ,iBAAK,OAAO,KAAK;AACjB;;;MAGZ;AAEA,gBAAU,UAAU,CAAC,UAAqB;AAGtC,YAAI,QAAQ;AACR,eAAK,OAAO,KAAK;eACd;AACH,cAAI,QAAa;AAEjB,cAAI,OAAO,eAAe,eAAe,iBAAiB,YAAY;AAClE,oBAAQ,MAAM;iBACX;AACH,oBAAQ;;AAMZ,iBAAO,IAAI,MAAM,KAAK,CAAC;;MAE/B;IACJ,CAAC;EACL;EAEO,KAAK,MAAS;AACjB,QAAI,KAAK,cAAc,KAAK,WAAW,eAAe,KAAK,sBAAsB,MAAM;AACnF,WAAK,QAAQ,IAAI,SAAS,OAAO,wCAAwC,cAAc,MAAM,KAAK,kBAAkB,CAAC,GAAG;AACxH,WAAK,WAAW,KAAK,IAAI;AACzB,aAAO,QAAQ,QAAO;;AAG1B,WAAO,QAAQ,OAAO,oCAAoC;EAC9D;EAEO,OAAI;AACP,QAAI,KAAK,YAAY;AAGjB,WAAK,OAAO,MAAS;;AAGzB,WAAO,QAAQ,QAAO;EAC1B;EAEQ,OAAO,OAA0B;AAErC,QAAI,KAAK,YAAY;AAEjB,WAAK,WAAW,UAAU,MAAK;MAAE;AACjC,WAAK,WAAW,YAAY,MAAK;MAAE;AACnC,WAAK,WAAW,UAAU,MAAK;MAAE;AACjC,WAAK,WAAW,MAAK;AACrB,WAAK,aAAa;;AAGtB,SAAK,QAAQ,IAAI,SAAS,OAAO,uCAAuC;AACxE,QAAI,KAAK,SAAS;AACd,UAAI,KAAK,cAAc,KAAK,MAAM,MAAM,aAAa,SAAS,MAAM,SAAS,MAAO;AAChF,aAAK,QAAQ,IAAI,MAAM,sCAAsC,MAAM,IAAI,KAAK,MAAM,UAAU,iBAAiB,IAAI,CAAC;iBAC3G,iBAAiB,OAAO;AAC/B,aAAK,QAAQ,KAAK;aACf;AACH,aAAK,QAAO;;;EAGxB;EAEQ,cAAc,OAAW;AAC7B,WAAO,SAAS,OAAO,MAAM,aAAa,aAAa,OAAO,MAAM,SAAS;EACjF;;;;AClJJ,IAAM,gBAAgB;AAGhB,IAAO,iBAAP,MAAqB;EA0BvB,YAAY,KAAa,UAAkC,CAAA,GAAE;AAbrD,SAAA,uBAA4D,MAAK;IAAE;AAK3D,SAAA,WAAgB,CAAA;AAMf,SAAA,oBAA4B;AAGzC,QAAI,WAAW,KAAK,KAAK;AAEzB,SAAK,UAAU,aAAa,QAAQ,MAAM;AAC1C,SAAK,UAAU,KAAK,YAAY,GAAG;AAEnC,cAAU,WAAW,CAAA;AACrB,YAAQ,oBAAoB,QAAQ,sBAAsB,SAAY,QAAQ,QAAQ;AACtF,QAAI,OAAO,QAAQ,oBAAoB,aAAa,QAAQ,oBAAoB,QAAW;AACvF,cAAQ,kBAAkB,QAAQ,oBAAoB,SAAY,OAAO,QAAQ;WAC9E;AACH,YAAM,IAAI,MAAM,iEAAiE;;AAErF,YAAQ,UAAU,QAAQ,YAAY,SAAY,MAAM,MAAO,QAAQ;AAEvE,QAAI,kBAAuB;AAC3B,QAAI,oBAAyB;AAE7B,QAAI,SAAS,UAAU,OAAO,cAAY,aAAa;AAGnD,YAAM,cAAc,OAAO,wBAAwB,aAAa,0BAA0B;AAC1F,wBAAkB,YAAY,IAAI;AAClC,0BAAoB,YAAY,aAAa;;AAGjD,QAAI,CAAC,SAAS,UAAU,OAAO,cAAc,eAAe,CAAC,QAAQ,WAAW;AAC5E,cAAQ,YAAY;eACb,SAAS,UAAU,CAAC,QAAQ,WAAW;AAC9C,UAAI,iBAAiB;AACjB,gBAAQ,YAAY;;;AAI5B,QAAI,CAAC,SAAS,UAAU,OAAO,gBAAgB,eAAe,CAAC,QAAQ,aAAa;AAChF,cAAQ,cAAc;eACf,SAAS,UAAU,CAAC,QAAQ,aAAa;AAChD,UAAI,OAAO,sBAAsB,aAAa;AAC1C,gBAAQ,cAAc;;;AAI9B,SAAK,cAAc,IAAI,sBAAsB,QAAQ,cAAc,IAAI,kBAAkB,KAAK,OAAO,GAAG,QAAQ,kBAAkB;AAClI,SAAK,mBAAgB;AACrB,SAAK,qBAAqB;AAC1B,SAAK,WAAW;AAEhB,SAAK,YAAY;AACjB,SAAK,UAAU;EACnB;EAIO,MAAM,MAAM,gBAA+B;AAC9C,qBAAiB,kBAAkB,eAAe;AAElD,QAAI,KAAK,gBAAgB,gBAAgB,gBAAgB;AAEzD,SAAK,QAAQ,IAAI,SAAS,OAAO,6CAA6C,eAAe,cAAc,CAAC,IAAI;AAEhH,QAAI,KAAK,qBAAgB,gBAAmC;AACxD,aAAO,QAAQ,OAAO,IAAI,MAAM,yEAAyE,CAAC;;AAG9G,SAAK,mBAAgB;AAErB,SAAK,wBAAwB,KAAK,eAAe,cAAc;AAC/D,UAAM,KAAK;AAGX,QAAI,KAAK,qBAAuB,iBAAoC;AAEhE,YAAM,UAAU;AAChB,WAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AAGxC,YAAM,KAAK;AAEX,aAAO,QAAQ,OAAO,IAAI,WAAW,OAAO,CAAC;eACtC,KAAK,qBAAuB,aAAgC;AAEnE,YAAM,UAAU;AAChB,WAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AACxC,aAAO,QAAQ,OAAO,IAAI,WAAW,OAAO,CAAC;;AAGjD,SAAK,qBAAqB;EAC9B;EAEO,KAAK,MAA0B;AAClC,QAAI,KAAK,qBAAgB,aAAgC;AACrD,aAAO,QAAQ,OAAO,IAAI,MAAM,qEAAqE,CAAC;;AAG1G,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa,IAAI,mBAAmB,KAAK,SAAU;;AAI5D,WAAO,KAAK,WAAW,KAAK,IAAI;EACpC;EAEO,MAAM,KAAK,OAAa;AAC3B,QAAI,KAAK,qBAAgB,gBAAmC;AACxD,WAAK,QAAQ,IAAI,SAAS,OAAO,+BAA+B,KAAK,wEAAwE;AAC7I,aAAO,QAAQ,QAAO;;AAG1B,QAAI,KAAK,qBAAgB,iBAAoC;AACzD,WAAK,QAAQ,IAAI,SAAS,OAAO,+BAA+B,KAAK,yEAAyE;AAC9I,aAAO,KAAK;;AAGhB,SAAK,mBAAgB;AAErB,SAAK,eAAe,IAAI,QAAQ,CAAC,YAAW;AAExC,WAAK,uBAAuB;IAChC,CAAC;AAGD,UAAM,KAAK,cAAc,KAAK;AAC9B,UAAM,KAAK;EACf;EAEQ,MAAM,cAAc,OAAa;AAIrC,SAAK,aAAa;AAElB,QAAI;AACA,YAAM,KAAK;aACN,GAAG;;AAOZ,QAAI,KAAK,WAAW;AAChB,UAAI;AACA,cAAM,KAAK,UAAU,KAAI;eACpB,GAAG;AACR,aAAK,QAAQ,IAAI,SAAS,OAAO,gDAAgD,CAAC,IAAI;AACtF,aAAK,gBAAe;;AAGxB,WAAK,YAAY;WACd;AACH,WAAK,QAAQ,IAAI,SAAS,OAAO,wFAAwF;;EAEjI;EAEQ,MAAM,eAAe,gBAA8B;AAGvD,QAAI,MAAM,KAAK;AACf,SAAK,sBAAsB,KAAK,SAAS;AACzC,SAAK,YAAY,sBAAsB,KAAK;AAE5C,QAAI;AACA,UAAI,KAAK,SAAS,iBAAiB;AAC/B,YAAI,KAAK,SAAS,cAAc,kBAAkB,YAAY;AAE1D,eAAK,YAAY,KAAK,oBAAoB,kBAAkB,UAAU;AAGtE,gBAAM,KAAK,gBAAgB,KAAK,cAAc;eAC3C;AACH,gBAAM,IAAI,MAAM,8EAA8E;;aAE/F;AACH,YAAI,oBAA+C;AACnD,YAAI,YAAY;AAEhB,WAAG;AACC,8BAAoB,MAAM,KAAK,wBAAwB,GAAG;AAE1D,cAAI,KAAK,qBAAgB,mBAAsC,KAAK,qBAAgB,gBAAmC;AACnH,kBAAM,IAAI,WAAW,gDAAgD;;AAGzE,cAAI,kBAAkB,OAAO;AACzB,kBAAM,IAAI,MAAM,kBAAkB,KAAK;;AAG3C,cAAK,kBAA0B,iBAAiB;AAC5C,kBAAM,IAAI,MAAM,8LAA8L;;AAGlN,cAAI,kBAAkB,KAAK;AACvB,kBAAM,kBAAkB;;AAG5B,cAAI,kBAAkB,aAAa;AAG/B,kBAAM,cAAc,kBAAkB;AACtC,iBAAK,sBAAsB,MAAM;AAEjC,iBAAK,YAAY,eAAe;AAChC,iBAAK,YAAY,sBAAsB;;AAG3C;iBAEG,kBAAkB,OAAO,YAAY;AAE5C,YAAI,cAAc,iBAAiB,kBAAkB,KAAK;AACtD,gBAAM,IAAI,MAAM,uCAAuC;;AAG3D,cAAM,KAAK,iBAAiB,KAAK,KAAK,SAAS,WAAW,mBAAmB,cAAc;;AAG/F,UAAI,KAAK,qBAAqB,sBAAsB;AAChD,aAAK,SAAS,oBAAoB;;AAGtC,UAAI,KAAK,qBAAgB,cAAiC;AAGtD,aAAK,QAAQ,IAAI,SAAS,OAAO,4CAA4C;AAC7E,aAAK,mBAAgB;;aAMpB,GAAG;AACR,WAAK,QAAQ,IAAI,SAAS,OAAO,qCAAqC,CAAC;AACvE,WAAK,mBAAgB;AACrB,WAAK,YAAY;AAGjB,WAAK,qBAAoB;AACzB,aAAO,QAAQ,OAAO,CAAC;;EAE/B;EAEQ,MAAM,wBAAwB,KAAW;AAC7C,UAAM,UAAiC,CAAA;AACvC,UAAM,CAAC,MAAM,KAAK,IAAI,mBAAkB;AACxC,YAAQ,IAAI,IAAI;AAEhB,UAAM,eAAe,KAAK,qBAAqB,GAAG;AAClD,SAAK,QAAQ,IAAI,SAAS,OAAO,gCAAgC,YAAY,GAAG;AAChF,QAAI;AACA,YAAM,WAAW,MAAM,KAAK,YAAY,KAAK,cAAc;QACvD,SAAS;QACT,SAAS,EAAE,GAAG,SAAS,GAAG,KAAK,SAAS,QAAO;QAC/C,SAAS,KAAK,SAAS;QACvB,iBAAiB,KAAK,SAAS;OAClC;AAED,UAAI,SAAS,eAAe,KAAK;AAC7B,eAAO,QAAQ,OAAO,IAAI,MAAM,mDAAmD,SAAS,UAAU,GAAG,CAAC;;AAG9G,YAAM,oBAAoB,KAAK,MAAM,SAAS,OAAiB;AAC/D,UAAI,CAAC,kBAAkB,oBAAoB,kBAAkB,mBAAmB,GAAG;AAG/E,0BAAkB,kBAAkB,kBAAkB;;AAE1D,aAAO;aACF,GAAG;AACR,UAAI,eAAe,qDAAqD;AACxE,UAAI,aAAa,WAAW;AACxB,YAAI,EAAE,eAAe,KAAK;AACtB,yBAAe,eAAe;;;AAGtC,WAAK,QAAQ,IAAI,SAAS,OAAO,YAAY;AAE7C,aAAO,QAAQ,OAAO,IAAI,iCAAiC,YAAY,CAAC;;EAEhF;EAEQ,kBAAkB,KAAa,iBAA0C;AAC7E,QAAI,CAAC,iBAAiB;AAClB,aAAO;;AAGX,WAAO,OAAO,IAAI,QAAQ,GAAG,MAAM,KAAK,MAAM,OAAO,MAAM,eAAe;EAC9E;EAEQ,MAAM,iBAAiB,KAAa,oBAAgE,mBAAuC,yBAAuC;AACtL,QAAI,aAAa,KAAK,kBAAkB,KAAK,kBAAkB,eAAe;AAC9E,QAAI,KAAK,cAAc,kBAAkB,GAAG;AACxC,WAAK,QAAQ,IAAI,SAAS,OAAO,yEAAyE;AAC1G,WAAK,YAAY;AACjB,YAAM,KAAK,gBAAgB,YAAY,uBAAuB;AAE9D,WAAK,eAAe,kBAAkB;AACtC;;AAGJ,UAAM,sBAA6B,CAAA;AACnC,UAAM,aAAa,kBAAkB,uBAAuB,CAAA;AAC5D,QAAI,YAA4C;AAChD,eAAW,YAAY,YAAY;AAC/B,YAAM,mBAAmB,KAAK,yBAAyB,UAAU,oBAAoB,uBAAuB;AAC5G,UAAI,4BAA4B,OAAO;AAEnC,4BAAoB,KAAK,GAAG,SAAS,SAAS,UAAU;AACxD,4BAAoB,KAAK,gBAAgB;iBAClC,KAAK,cAAc,gBAAgB,GAAG;AAC7C,aAAK,YAAY;AACjB,YAAI,CAAC,WAAW;AACZ,cAAI;AACA,wBAAY,MAAM,KAAK,wBAAwB,GAAG;mBAC7C,IAAI;AACT,mBAAO,QAAQ,OAAO,EAAE;;AAE5B,uBAAa,KAAK,kBAAkB,KAAK,UAAU,eAAe;;AAEtE,YAAI;AACA,gBAAM,KAAK,gBAAgB,YAAY,uBAAuB;AAC9D,eAAK,eAAe,UAAU;AAC9B;iBACK,IAAI;AACT,eAAK,QAAQ,IAAI,SAAS,OAAO,kCAAkC,SAAS,SAAS,MAAM,EAAE,EAAE;AAC/F,sBAAY;AACZ,8BAAoB,KAAK,IAAI,4BAA4B,GAAG,SAAS,SAAS,YAAY,EAAE,IAAI,kBAAkB,SAAS,SAAS,CAAC,CAAC;AAEtI,cAAI,KAAK,qBAAgB,cAAiC;AACtD,kBAAM,UAAU;AAChB,iBAAK,QAAQ,IAAI,SAAS,OAAO,OAAO;AACxC,mBAAO,QAAQ,OAAO,IAAI,WAAW,OAAO,CAAC;;;;;AAM7D,QAAI,oBAAoB,SAAS,GAAG;AAChC,aAAO,QAAQ,OAAO,IAAI,gBAAgB,yEAAyE,oBAAoB,KAAK,GAAG,CAAC,IAAI,mBAAmB,CAAC;;AAE5K,WAAO,QAAQ,OAAO,IAAI,MAAM,6EAA6E,CAAC;EAClH;EAEQ,oBAAoB,WAA4B;AACpD,YAAQ,WAAW;MACf,KAAK,kBAAkB;AACnB,YAAI,CAAC,KAAK,SAAS,WAAW;AAC1B,gBAAM,IAAI,MAAM,mDAAmD;;AAEvE,eAAO,IAAI,mBAAmB,KAAK,aAAa,KAAK,qBAAqB,KAAK,SAAS,KAAK,SAAS,mBAAoB,KAAK,SAAS,WAAW,KAAK,SAAS,WAAW,CAAA,CAAE;MAClL,KAAK,kBAAkB;AACnB,YAAI,CAAC,KAAK,SAAS,aAAa;AAC5B,gBAAM,IAAI,MAAM,qDAAqD;;AAEzE,eAAO,IAAI,0BAA0B,KAAK,aAAa,KAAK,YAAY,cAAc,KAAK,SAAS,KAAK,QAAQ;MACrH,KAAK,kBAAkB;AACnB,eAAO,IAAI,qBAAqB,KAAK,aAAa,KAAK,SAAS,KAAK,QAAQ;MACjF;AACI,cAAM,IAAI,MAAM,sBAAsB,SAAS,GAAG;;EAE9D;EAEQ,gBAAgB,KAAa,gBAA8B;AAC/D,SAAK,UAAW,YAAY,KAAK;AACjC,SAAK,UAAW,UAAU,CAAC,MAAM,KAAK,gBAAgB,CAAC;AACvD,WAAO,KAAK,UAAW,QAAQ,KAAK,cAAc;EACtD;EAEQ,yBAAyB,UAA+B,oBAAmD,yBAAuC;AACtJ,UAAM,YAAY,kBAAkB,SAAS,SAAS;AACtD,QAAI,cAAc,QAAQ,cAAc,QAAW;AAC/C,WAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,SAAS,SAAS,+CAA+C;AACzH,aAAO,IAAI,MAAM,uBAAuB,SAAS,SAAS,+CAA+C;WACtG;AACH,UAAI,iBAAiB,oBAAoB,SAAS,GAAG;AACjD,cAAM,kBAAkB,SAAS,gBAAgB,IAAI,CAAC,MAAM,eAAe,CAAC,CAAC;AAC7E,YAAI,gBAAgB,QAAQ,uBAAuB,KAAK,GAAG;AACvD,cAAK,cAAc,kBAAkB,cAAc,CAAC,KAAK,SAAS,aAC7D,cAAc,kBAAkB,oBAAoB,CAAC,KAAK,SAAS,aAAc;AAClF,iBAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,kBAAkB,SAAS,CAAC,qDAAqD;AACzI,mBAAO,IAAI,0BAA0B,IAAI,kBAAkB,SAAS,CAAC,2CAA2C,SAAS;iBACtH;AACH,iBAAK,QAAQ,IAAI,SAAS,OAAO,wBAAwB,kBAAkB,SAAS,CAAC,IAAI;AACzF,gBAAI;AACA,qBAAO,KAAK,oBAAoB,SAAS;qBACpC,IAAI;AACT,qBAAO;;;eAGZ;AACH,eAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,kBAAkB,SAAS,CAAC,gEAAgE,eAAe,uBAAuB,CAAC,IAAI;AAC/L,iBAAO,IAAI,MAAM,IAAI,kBAAkB,SAAS,CAAC,sBAAsB,eAAe,uBAAuB,CAAC,GAAG;;aAElH;AACH,aAAK,QAAQ,IAAI,SAAS,OAAO,uBAAuB,kBAAkB,SAAS,CAAC,0CAA0C;AAC9H,eAAO,IAAI,uBAAuB,IAAI,kBAAkB,SAAS,CAAC,gCAAgC,SAAS;;;EAGvH;EAEQ,cAAc,WAAc;AAChC,WAAO,aAAa,OAAQ,cAAe,YAAY,aAAa;EACxE;EAEQ,gBAAgB,OAAa;AACjC,SAAK,QAAQ,IAAI,SAAS,OAAO,iCAAiC,KAAK,2BAA2B,KAAK,gBAAgB,GAAG;AAE1H,SAAK,YAAY;AAGjB,YAAQ,KAAK,cAAc;AAC3B,SAAK,aAAa;AAElB,QAAI,KAAK,qBAAgB,gBAAmC;AACxD,WAAK,QAAQ,IAAI,SAAS,OAAO,yCAAyC,KAAK,4EAA4E;AAC3J;;AAGJ,QAAI,KAAK,qBAAgB,cAAiC;AACtD,WAAK,QAAQ,IAAI,SAAS,SAAS,yCAAyC,KAAK,wEAAwE;AACzJ,YAAM,IAAI,MAAM,iCAAiC,KAAK,qEAAqE;;AAG/H,QAAI,KAAK,qBAAgB,iBAAoC;AAGzD,WAAK,qBAAoB;;AAG7B,QAAI,OAAO;AACP,WAAK,QAAQ,IAAI,SAAS,OAAO,uCAAuC,KAAK,IAAI;WAC9E;AACH,WAAK,QAAQ,IAAI,SAAS,aAAa,0BAA0B;;AAGrE,QAAI,KAAK,YAAY;AACjB,WAAK,WAAW,KAAI,EAAG,MAAM,CAAC,MAAK;AAC/B,aAAK,QAAQ,IAAI,SAAS,OAAO,0CAA0C,CAAC,IAAI;MACpF,CAAC;AACD,WAAK,aAAa;;AAGtB,SAAK,eAAe;AACpB,SAAK,mBAAgB;AAErB,QAAI,KAAK,oBAAoB;AACzB,WAAK,qBAAqB;AAC1B,UAAI;AACA,YAAI,KAAK,SAAS;AACd,eAAK,QAAQ,KAAK;;eAEjB,GAAG;AACR,aAAK,QAAQ,IAAI,SAAS,OAAO,0BAA0B,KAAK,kBAAkB,CAAC,IAAI;;;EAGnG;EAEQ,YAAY,KAAW;AAE3B,QAAI,IAAI,YAAY,YAAY,CAAC,MAAM,KAAK,IAAI,YAAY,WAAW,CAAC,MAAM,GAAG;AAC7E,aAAO;;AAGX,QAAI,CAAC,SAAS,WAAW;AACrB,YAAM,IAAI,MAAM,mBAAmB,GAAG,IAAI;;AAQ9C,UAAM,OAAO,OAAO,SAAS,cAAc,GAAG;AAC9C,SAAK,OAAO;AAEZ,SAAK,QAAQ,IAAI,SAAS,aAAa,gBAAgB,GAAG,SAAS,KAAK,IAAI,IAAI;AAChF,WAAO,KAAK;EAChB;EAEQ,qBAAqB,KAAW;AACpC,UAAM,QAAQ,IAAI,QAAQ,GAAG;AAC7B,QAAI,eAAe,IAAI,UAAU,GAAG,UAAU,KAAK,IAAI,SAAS,KAAK;AACrE,QAAI,aAAa,aAAa,SAAS,CAAC,MAAM,KAAK;AAC/C,sBAAgB;;AAEpB,oBAAgB;AAChB,oBAAgB,UAAU,KAAK,KAAK,IAAI,UAAU,KAAK;AAEvD,QAAI,aAAa,QAAQ,kBAAkB,MAAM,IAAI;AACjD,sBAAgB,UAAU,KAAK,MAAM;AACrC,sBAAgB,sBAAsB,KAAK;;AAE/C,WAAO;EACX;;AAGJ,SAAS,iBAAiB,oBAAmD,iBAAkC;AAC3G,SAAO,CAAC,uBAAwB,kBAAkB,wBAAwB;AAC9E;AAGM,IAAO,qBAAP,MAAO,oBAAkB;EAO3B,YAA6B,YAAsB;AAAtB,SAAA,aAAA;AANrB,SAAA,UAAiB,CAAA;AAEjB,SAAA,aAAsB;AAK1B,SAAK,oBAAoB,IAAI,cAAa;AAC1C,SAAK,mBAAmB,IAAI,cAAa;AAEzC,SAAK,mBAAmB,KAAK,UAAS;EAC1C;EAEO,KAAK,MAA0B;AAClC,SAAK,YAAY,IAAI;AACrB,QAAI,CAAC,KAAK,kBAAkB;AACxB,WAAK,mBAAmB,IAAI,cAAa;;AAE7C,WAAO,KAAK,iBAAiB;EACjC;EAEO,OAAI;AACP,SAAK,aAAa;AAClB,SAAK,kBAAkB,QAAO;AAC9B,WAAO,KAAK;EAChB;EAEQ,YAAY,MAA0B;AAC1C,QAAI,KAAK,QAAQ,UAAU,OAAO,KAAK,QAAQ,CAAC,MAAO,OAAO,MAAO;AACjE,YAAM,IAAI,MAAM,+BAA+B,OAAO,KAAK,OAAQ,oBAAoB,OAAO,IAAK,EAAE;;AAGzG,SAAK,QAAQ,KAAK,IAAI;AACtB,SAAK,kBAAkB,QAAO;EAClC;EAEQ,MAAM,YAAS;AACnB,WAAO,MAAM;AACT,YAAM,KAAK,kBAAkB;AAE7B,UAAI,CAAC,KAAK,YAAY;AAClB,YAAI,KAAK,kBAAkB;AACvB,eAAK,iBAAiB,OAAO,qBAAqB;;AAGtD;;AAGJ,WAAK,oBAAoB,IAAI,cAAa;AAE1C,YAAM,kBAAkB,KAAK;AAC7B,WAAK,mBAAmB;AAExB,YAAM,OAAO,OAAO,KAAK,QAAQ,CAAC,MAAO,WACrC,KAAK,QAAQ,KAAK,EAAE,IACpB,oBAAmB,eAAe,KAAK,OAAO;AAElD,WAAK,QAAQ,SAAS;AAEtB,UAAI;AACA,cAAM,KAAK,WAAW,KAAK,IAAI;AAC/B,wBAAgB,QAAO;eAClB,OAAO;AACZ,wBAAgB,OAAO,KAAK;;;EAGxC;EAEQ,OAAO,eAAe,cAA2B;AACrD,UAAM,cAAc,aAAa,IAAI,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,CAAC,GAAG,MAAM,IAAI,CAAC;AAChF,UAAM,SAAS,IAAI,WAAW,WAAW;AACzC,QAAI,SAAS;AACb,eAAW,QAAQ,cAAc;AAC7B,aAAO,IAAI,IAAI,WAAW,IAAI,GAAG,MAAM;AACvC,gBAAU,KAAK;;AAGnB,WAAO,OAAO;EAClB;;AAGJ,IAAM,gBAAN,MAAmB;EAKf,cAAA;AACI,SAAK,UAAU,IAAI,QAAQ,CAAC,SAAS,WAAW,CAAC,KAAK,WAAW,KAAK,SAAS,IAAI,CAAC,SAAS,MAAM,CAAC;EACxG;EAEO,UAAO;AACV,SAAK,UAAU;EACnB;EAEO,OAAO,QAAY;AACtB,SAAK,UAAW,MAAM;EAC1B;;;;ACjpBJ,IAAM,yBAAiC;AAGjC,IAAO,kBAAP,MAAsB;EAA5B,cAAA;AAGoB,SAAA,OAAe;AAEf,SAAA,UAAkB;AAGlB,SAAA,iBAAiC,eAAe;EAmGpE;;;;;;EA5FW,cAAc,OAAe,QAAe;AAE/C,QAAI,OAAO,UAAU,UAAU;AAC3B,YAAM,IAAI,MAAM,yDAAyD;;AAG7E,QAAI,CAAC,OAAO;AACR,aAAO,CAAA;;AAGX,QAAI,WAAW,MAAM;AACjB,eAAS,WAAW;;AAIxB,UAAM,WAAW,kBAAkB,MAAM,KAAK;AAE9C,UAAM,cAAc,CAAA;AACpB,eAAW,WAAW,UAAU;AAC5B,YAAM,gBAAgB,KAAK,MAAM,OAAO;AACxC,UAAI,OAAO,cAAc,SAAS,UAAU;AACxC,cAAM,IAAI,MAAM,kBAAkB;;AAEtC,cAAQ,cAAc,MAAM;QACxB,KAAK,YAAY;AACb,eAAK,qBAAqB,aAAa;AACvC;QACJ,KAAK,YAAY;AACb,eAAK,qBAAqB,aAAa;AACvC;QACJ,KAAK,YAAY;AACb,eAAK,qBAAqB,aAAa;AACvC;QACJ,KAAK,YAAY;AAEb;QACJ,KAAK,YAAY;AAEb;QACJ;AAEI,iBAAO,IAAI,SAAS,aAAa,2BAA2B,cAAc,OAAO,YAAY;AAC7F;;AAER,kBAAY,KAAK,aAAa;;AAGlC,WAAO;EACX;;;;;;EAOO,aAAa,SAAmB;AACnC,WAAO,kBAAkB,MAAM,KAAK,UAAU,OAAO,CAAC;EAC1D;EAEQ,qBAAqB,SAA0B;AACnD,SAAK,sBAAsB,QAAQ,QAAQ,yCAAyC;AAEpF,QAAI,QAAQ,iBAAiB,QAAW;AACpC,WAAK,sBAAsB,QAAQ,cAAc,yCAAyC;;EAElG;EAEQ,qBAAqB,SAA0B;AACnD,SAAK,sBAAsB,QAAQ,cAAc,yCAAyC;AAE1F,QAAI,QAAQ,SAAS,QAAW;AAC5B,YAAM,IAAI,MAAM,yCAAyC;;EAEjE;EAEQ,qBAAqB,SAA0B;AACnD,QAAI,QAAQ,UAAU,QAAQ,OAAO;AACjC,YAAM,IAAI,MAAM,yCAAyC;;AAG7D,QAAI,CAAC,QAAQ,UAAU,QAAQ,OAAO;AAClC,WAAK,sBAAsB,QAAQ,OAAO,yCAAyC;;AAGvF,SAAK,sBAAsB,QAAQ,cAAc,yCAAyC;EAC9F;EAEQ,sBAAsB,OAAY,cAAoB;AAC1D,QAAI,OAAO,UAAU,YAAY,UAAU,IAAI;AAC3C,YAAM,IAAI,MAAM,YAAY;;EAEpC;;;;ACvGJ,IAAM,sBAA+C;EACjD,OAAO,SAAS;EAChB,OAAO,SAAS;EAChB,MAAM,SAAS;EACf,aAAa,SAAS;EACtB,MAAM,SAAS;EACf,SAAS,SAAS;EAClB,OAAO,SAAS;EAChB,UAAU,SAAS;EACnB,MAAM,SAAS;;AAGnB,SAAS,cAAc,MAAY;AAI/B,QAAM,UAAU,oBAAoB,KAAK,YAAW,CAAE;AACtD,MAAI,OAAO,YAAY,aAAa;AAChC,WAAO;SACJ;AACH,UAAM,IAAI,MAAM,sBAAsB,IAAI,EAAE;;AAEpD;AAGM,IAAO,uBAAP,MAA2B;EA0CtB,iBAAiB,SAAoC;AACxD,QAAI,WAAW,SAAS,SAAS;AAEjC,QAAI,SAAS,OAAO,GAAG;AACnB,WAAK,SAAS;eACP,OAAO,YAAY,UAAU;AACpC,YAAM,WAAW,cAAc,OAAO;AACtC,WAAK,SAAS,IAAI,cAAc,QAAQ;WACrC;AACH,WAAK,SAAS,IAAI,cAAc,OAAO;;AAG3C,WAAO;EACX;EA0BO,QAAQ,KAAa,wBAAmE;AAC3F,QAAI,WAAW,KAAK,KAAK;AACzB,QAAI,WAAW,KAAK,KAAK;AAEzB,SAAK,MAAM;AAIX,QAAI,OAAO,2BAA2B,UAAU;AAC5C,WAAK,wBAAwB,EAAE,GAAG,KAAK,uBAAuB,GAAG,uBAAsB;WACpF;AACH,WAAK,wBAAwB;QACzB,GAAG,KAAK;QACR,WAAW;;;AAInB,WAAO;EACX;;;;;EAMO,gBAAgB,UAAsB;AACzC,QAAI,WAAW,UAAU,UAAU;AAEnC,SAAK,WAAW;AAChB,WAAO;EACX;EAmBO,uBAAuB,8BAAsD;AAChF,QAAI,KAAK,iBAAiB;AACtB,YAAM,IAAI,MAAM,yCAAyC;;AAG7D,QAAI,CAAC,8BAA8B;AAC/B,WAAK,kBAAkB,IAAI,uBAAsB;eAC1C,MAAM,QAAQ,4BAA4B,GAAG;AACpD,WAAK,kBAAkB,IAAI,uBAAuB,4BAA4B;WAC3E;AACH,WAAK,kBAAkB;;AAG3B,WAAO;EACX;;;;;EAMO,QAAK;AAGR,UAAM,wBAAwB,KAAK,yBAAyB,CAAA;AAG5D,QAAI,sBAAsB,WAAW,QAAW;AAE5C,4BAAsB,SAAS,KAAK;;AAIxC,QAAI,CAAC,KAAK,KAAK;AACX,YAAM,IAAI,MAAM,0FAA0F;;AAE9G,UAAM,aAAa,IAAI,eAAe,KAAK,KAAK,qBAAqB;AAErE,WAAO,cAAc,OACjB,YACA,KAAK,UAAU,WAAW,UAC1B,KAAK,YAAY,IAAI,gBAAe,GACpC,KAAK,eAAe;EAC5B;;AAGJ,SAAS,SAAS,QAAW;AACzB,SAAO,OAAO,QAAQ;AAC1B;", "names": ["LogLevel", "MessageType", "HubConnectionState", "HttpTransportType", "TransferFormat", "AbortController", "AbortController"]}