import presetIcons from '@unocss/preset-icons'
import {
  defineConfig,
  presetAttributify,
  presetTypography,
  presetUno,
  transformerDirectives,
  transformerVariantGroup,
} from 'unocss'
import { getColors } from './src/theme/utils'

export default defineConfig({
  theme: {
    colors: getColors(),
  },
  extendTheme: (theme: any) => {
    return {
      ...theme,
      breakpoints: {
        ...theme.breakpoints,
        '3xl': '1920px', // 额外超大屏幕
        '4xl': '2048px', // 额外超大屏幕
      },
    }
  },
  rules: [
    [/^c-bg$/, () => ({ 'background-color': 'var(--ch2-color-bg-container)' })], // 组件背景色
    [/^bg$/, () => ({ 'background-color': 'var(--ch2-color-bg-container)' })], // 组件背景色
    [/^body-bg$/, () => ({ 'background-color': 'var(--color-ch2-bg-container)' })], // 块背景色
    [/^text-color$/, () => ({ color: 'var(--ch2-color-primary:)' })], // 主文字色
    [/^flex-(\d+)$/, ([, d]) => ({ flex: d })],
  ],
  shortcuts: [
    ['center', 'flex items-center justify-center'],
    ['un-btn', 'px-4 py-1 rounded inline-block bg-primary text-color cursor-pointer !outline-none hover:bg-primary-hover disabled:cursor-default disabled:bg-gray-1 disabled:opacity-50'],
    ['icon-btn', 'inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-primary-hover'],
    ['icon', 'inline-block vertical-sub'],
  ],
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
      autoInstall: true,
    }),
    presetTypography(),
  ],
  transformers: [
    transformerDirectives(),
    transformerVariantGroup(),
  ],
  safelist: 'prose m-auto text-left'.split(' '),
})
