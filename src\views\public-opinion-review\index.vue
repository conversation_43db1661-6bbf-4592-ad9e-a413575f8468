<template>
  <div class="flex space-x-4">
    <a-card title="疑似舆情" style="width: 360px">
      <template #extra><a>重置搜索</a></template>
      <div class="space-y-4">
        <a-input-search
          v-model:value="postParams.keyword!"
          placeholder="请输入关键字"
          style="width: 100%"
          size="large"
          @search="tableRef?.fetchData"
        />
        <div>
          <div class="text-base font-500">优先审核</div>
          <div class="mt-2 w-100% flex justify-between">
            <a-checkbox :checked="postParams.cooperateDept!" @change="isCooperateChange">合作高校</a-checkbox>
            <span>{{ eventTypes.cooperativeUnits }}</span>
          </div>
        </div>
        <div>
          <div class="text-base font-500">风险等级</div>
          <a-checkbox-group v-model:value="postParams.riskLevel!" class="w-100%" @change="tableRef?.fetchData">
            <div v-for="(item, key) in eventTypes.riskLevel" :key="key" class="mt-2 w-100% flex justify-between">
              <a-checkbox :value="RiskLevel[key]">{{ key }}</a-checkbox>
              <span class="text-sm text-gray-500">{{ item }}</span>
            </div>
          </a-checkbox-group>
        </div>
        <div>
          <div class="text-base font-500">舆情类别</div>
          <a-checkbox-group v-model:value="postParams.mainCategory!" @change="tableRef?.fetchData">
            <div v-for="(item, key) in eventTypes.tagType" :key="key" class="mt-2 w-100% flex justify-between">
              <a-checkbox :value="key">{{ key }}</a-checkbox>
              <span class="text-sm text-gray-500">{{ item }}</span>
            </div>
          </a-checkbox-group>
        </div>
        <div>
          <div class="text-base font-500">发布时间</div>
          <a-checkbox-group
            v-model:value="selectedValue"
            style="width: 100%"
            @change="handleChange"
          >
            <div
              v-for="option in timeOptions"
              :key="option"
              class="mt-2 w-100%"
            >
              <a-checkbox :value="option">
                <span class="text-base">{{ option }}</span>
              </a-checkbox>
            </div>
          </a-checkbox-group>
          <div
            v-show="selectedValue?.includes('自定义时间段')"
            class="mt-2 flex items-center gap-1"
          >
            <a-range-picker v-model:value="dateRange" @change="panelChange" />
          </div>
        </div>
      </div>
    </a-card>
    <!-- :row-class-name="(_record, index) => (index % 2 === 1 ? 'c2-table-striped' : 'c2-table-prototype')" -->
    <div class="w-100% flex-1 space-y-4">
      <a-card :title="`待审核列队(${eventTypes.pendingApproval})`">
        <template #extra><a @click="router.push('/public-opinion-review/history')">审核历史</a></template>
        <c-table
          ref="tableRef"
          :columns="columns"
          :post-params="postParams"
          :custom-row="customRow"
          immediate
          :api="api.PublicOpinionSubmissions.GetSubmissionList_PostAsync"
          :row-class-name="(record) => record.id === currentId ? 'bg-info-bg-hover' : ''"
          size="small"
          :scroll="{ y: 260 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'published'">
              <a>
                {{ dateTime(record.published, 'YYYY-MM-DD HH:mm') }}
              </a>
            </template>
            <template v-if="column.dataIndex === 'riskLevel'">
              <a-tag :color="record.riskLevel === RiskLevel.高风险 ? 'error' : record.riskLevel === RiskLevel.中风险 ? 'warning' : 'processing'">{{ RiskLevel[record.riskLevel] }}</a-tag>
            </template>
            <template v-if="column.dataIndex === 'auditStatus'">
              <a-tag color="default">{{ PublicOpinionSubmissionAuditType[record.auditStatus] }}</a-tag>
            </template>
          </template>
        </c-table>
      </a-card>
      <a-card title="舆情审核">
        <template v-if="Guid.isNotNull(detail.id)">
          <div class="space-y-2">
            <div class="">
              <span class="inline-block w-110px text-right">舆情ID：</span>
              <span class="c-text-secondary">{{ detail.id || '--' }}</span>
            </div>
            <div class="">
              <span class="inline-block w-110px text-right">发布时间：</span>
              <span class="c-text-secondary">{{ dateTime(detail.published, 'YYYY-MM-DD HH:mm') }}</span>
            </div>
            <div class="">
              <span class="inline-block w-110px text-right">来源：</span>
              <span class="c-text-secondary">{{ detail.source || '--' }}</span>
            </div>
            <div class="">
              <span class="inline-block w-110px text-right">涉及单位：</span>
              <span class="c-text-secondary">{{ detail.dept?.name || '--' }} <span v-show="detail.dept?.isCooperate" class="inline-block w-70px rounded-md bg-#F0B800 px-1 text-center c-#fff">合作单位</span></span>
            </div>
            <div class="">
              <span class="inline-block w-110px text-right">原文链接：</span>
              <c-open-link :href="detail.url || ''" :max-length="100" />
            </div>
            <div class="flex">
              <span class="inline-block w-110px text-right">舆情标题：</span>
              <span class="c-text-secondary"><c-truncated-text :text="detail.summary || ''" :max-lines="1" type="launch" /></span>
            </div>
            <div class="flex">
              <span class="inline-block min-w-110px text-right">舆情原文：</span>
              <span class="c-text-secondary"><c-truncated-text :text="detail.content || ''" :max-lines="2" type="launch" /></span>
            </div>
            <div class="">
              <span class="inline-block w-110px text-right">舆情分类：</span>
              <span class="c-text-secondary">{{ detail.mainCategory }} -- {{ detail.subCategory }}</span>
            </div>
            <div class="">
              <span class="inline-block w-110px text-right">风险等级：</span>
              <span class="c-text-secondary">  <a-tag :color="detail.riskLevel === RiskLevel.高风险 ? 'error' : detail.riskLevel === RiskLevel.中风险 ? 'warning' : 'processing'">{{ RiskLevel[detail.riskLevel] }}</a-tag></span>
            </div>
            <div class="">
              <span class="inline-block w-110px text-right">上报判定理由：</span>
              <span class="c-text-secondary">{{ detail.reason || '--' }}</span>
            </div>
            <div class="">
              <span class="inline-block w-110px text-right">审核状态：</span>
              <span class="c-text-secondary">
                <a-tag :color="detail.auditStatus === PublicOpinionSubmissionAuditType.入库 ? 'success' : detail.auditStatus === PublicOpinionSubmissionAuditType.忽略 ? 'warning' : 'default'">{{ PublicOpinionSubmissionAuditType[detail.auditStatus] }}</a-tag>
              </span>
            </div>
          </div>
          <div v-if="detail.auditStatus === PublicOpinionSubmissionAuditType.待审核" class="mt-4 text-center">
            <a-button type="primary" @click="toExamine(PublicOpinionSubmissionAuditType.入库)">确认为舆情</a-button>
            <a-button type="primary" danger class="ml-4" @click="toExamine(PublicOpinionSubmissionAuditType.忽略)">非舆情/忽略</a-button>
          </div>
        </template>
        <a-empty v-else />
      </a-card>
    </div>
  </div>

  <EditForm v-model:open="open" v-model="currentObj" is-audit :audit-save="onAuditSave" />
</template>

<script lang='ts' setup>
import type { PublicOpinionSubmissionListItem } from '@/api/models'
import * as api from '@/api'
import { PublicOpinionEditModel, PublicOpinionSubmissionAuditType, PublicOpinionSubmissionStatistics, PublicOpinionSubmissionViewModel, RiskLevel, SubmissionQueryRequest } from '@/api/models'
import { Guid } from '@/utils/GUID'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message, Modal } from 'ant-design-vue'
import { createVNode } from 'vue'
import { useRouter } from 'vue-router'
import EditForm from '../public-opinion/components/EditForm.vue'

definePage({
  meta: {
    title: '舆情审核',
    icon: 'AuditOutlined',
    local: true,
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '舆情审核',
        local: true,
        icon: 'AuditOutlined',
        order: 7,
      },
    },
  },
})

const router = useRouter()

const { timeOptions, selectedValue, handleChange, panelChange, dateRange } = generateTimeRangesHook()

const open = ref(false)

const currentObj = ref(new PublicOpinionEditModel())

const detail = ref(new PublicOpinionSubmissionViewModel())

const tableRef = useTemplateRef('tableRef')

const postParams = ref({ ...new SubmissionQueryRequest(), auditStatus: PublicOpinionSubmissionAuditType.待审核 })

const eventTypes = ref(new PublicOpinionSubmissionStatistics())

const columns = ref([
  { title: '舆情原文', dataIndex: 'content' },
  { title: '分类', dataIndex: 'mainCategory', width: 120 },
  { title: '风险等级', dataIndex: 'riskLevel', width: 120 },
  { title: '发布时间', dataIndex: 'published', width: 180 },
  { title: '审核状态', dataIndex: 'auditStatus', width: 120, align: 'center' },
])

const currentId = ref('')

async function getDetail() {
  detail.value = await api.PublicOpinionSubmissions.GetByIdAsync({ id: currentId.value })
}

function customRow(record: PublicOpinionSubmissionListItem) {
  return {
    onClick: async () => {
      currentId.value = record.id!
      currentObj.value = viewModelToEditModel(record, new PublicOpinionEditModel())
      currentObj.value.id = Guid.empty
      getDetail()
    },
  }
}

function isCooperateChange(e: any) {
  const checked = e.target.checked
  if (!checked)
    postParams.value.cooperateDept = undefined
  else postParams.value.cooperateDept = checked
}

async function getEventType() {
  eventTypes.value = await api.PublicOpinionSubmissions.GetStatistics_PostAsync({ auditStatus: PublicOpinionSubmissionAuditType.待审核 })
}

async function onAudit(type: PublicOpinionSubmissionAuditType, data: PublicOpinionEditModel) {
  const res = await api.PublicOpinionSubmissions.AuditSubmission_PostAsync({ id: detail.value.id, auditStatus: type }, data)
  tableRef.value?.fetchData()
  getDetail()
  message.success('审核成功')
  return res
}

async function onAuditSave() {
  const data = await onAudit(PublicOpinionSubmissionAuditType.入库, currentObj.value)
  open.value = false
  return data
}

function toExamine(type: PublicOpinionSubmissionAuditType) {
  const text = type === PublicOpinionSubmissionAuditType.入库 ? '确定审核为舆情吗？' : '确认审核为非舆情/忽略吗？'

  if (type === PublicOpinionSubmissionAuditType.入库) {
    open.value = true
  }
  else {
    Modal.confirm({
      title: text,
      icon: createVNode(ExclamationCircleOutlined),
      async onOk() {
        onAudit(PublicOpinionSubmissionAuditType.忽略, {})
      },
    })
  }

  // tableRef.value?.fetchData()
}

onMounted(() => {
  getEventType()
})

function generateTimeRangesHook() {
  const dateRange = ref<[Dayjs, Dayjs]>([])

  const timeOptions = ref([
    '最近24小时',
    '最近1周',
    '最近1个月',
    '最近1年',
    '自定义时间段',
  ])

  const selectedValue = ref<string[]>()

  function handleChange(newCheckedList: string[]) {
    if (newCheckedList.length > 1) {
      selectedValue.value = [newCheckedList[newCheckedList.length - 1]]
    }
    switch (selectedValue.value?.[0]) {
      case '最近24小时':
        postParams.value.startTime = dayjs().subtract(1, 'day')
        postParams.value.endTime = dayjs()
        break
      case '最近1周':
        postParams.value.startTime = dayjs().subtract(1, 'week')
        postParams.value.endTime = dayjs()
        break
      case '最近1个月':
        postParams.value.startTime = dayjs().subtract(1, 'month')
        postParams.value.endTime = dayjs()
        break
      case '最近1年':
        postParams.value.startTime = dayjs().subtract(1, 'year')
        postParams.value.endTime = dayjs()
        break
      case '自定义时间段':
        postParams.value.startTime = dateRange.value?.[0]
        postParams.value.endTime = dateRange.value?.[1]
        break
    }
    tableRef.value?.fetchData()
  }

  function panelChange() {
    postParams.value.startTime = dateRange.value?.[0]
    postParams.value.endTime = dateRange.value?.[1]
    tableRef.value?.fetchData()
  }

  return { timeOptions, selectedValue, handleChange, panelChange, dateRange }
}
</script>

<style scoped lang="less">
.pro-table .table-main {
  margin-top: 0;
  padding: 0;
}
:deep(.c2-table-striped) td {
  background-color: @colorPrimaryBg;
}

:deep(.ant-table-thead tr th) {
  background: @colorPrimaryBgHover !important;
}
</style>
