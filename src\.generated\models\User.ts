import { IdentityUser } from "./IdentityUser";
import { UserRole } from "./UserRole";
import { Role } from "./Role";
import { Department } from "./Department";
import { DeptPushChannel } from "./DeptPushChannel";
import { PublicOpinion } from "./PublicOpinion";
/**用户*/
export class User extends IdentityUser<string> {
  /**用户角色关联表*/
  userRoles?: UserRole[] | null | undefined = [];
  /**用户角色*/
  roles?: Role[] | null | undefined = [];
  /**外键关联部门*/
  departmentId?: GUID = null;
  /**部门表*/
  department?: Department | null | undefined = null;
  /**职位名称*/
  position?: string | null | undefined = null;
  /**推送渠道*/
  deptPushChannels?: DeptPushChannel[] | null | undefined = [];
  /**安全团队*/
  securityTeam: boolean = false;
  createdOpinions?: PublicOpinion[] | null | undefined = [];
  modifiedOpinions?: PublicOpinion[] | null | undefined = [];
  handledOpinions?: PublicOpinion[] | null | undefined = [];
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
}
