import type { WebSource } from '@/api/models'
import type { Ref } from 'vue'
import * as api from '@/api'
import { ref } from 'vue'

const CACHE_DURATION = 30 * 60 * 1000 // 10 分钟（单位：毫秒）

const cache: Ref<WebSource[] | null> = ref(null)
const lastLoadedTime: Ref<number | null> = ref(null)
const loading = ref(false)
const loaded = ref(false)
const error = ref<Error | null>(null)

function isExpired(): boolean {
  if (!lastLoadedTime.value)
    return true
  return Date.now() - lastLoadedTime.value > CACHE_DURATION
}

export function useSocialMediaCache() {
  const load = async () => {
    // 若正在加载或未过期，跳过
    if (loading.value || (!isExpired() && loaded.value))
      return

    loading.value = true
    try {
      const data = await api.WebSources.List_GetAsync({})
      cache.value = data
      lastLoadedTime.value = Date.now()
      loaded.value = true
    }
    catch (err: any) {
      error.value = err
    }
    finally {
      loading.value = false
    }
  }

  const getAll = () => cache.value ?? []

  const getByDomain = (domain: string): WebSource | undefined => {
    return cache.value?.find(item => item.domain === domain)
  }

  const getById = (id: string): WebSource | undefined => {
    return cache.value?.find(item => item.id === id)
  }

  const getByName = (name: string): WebSource | undefined => {
    return cache.value?.find(item => item.name === name)
  }

  return {
    load,
    getAll,
    getByDomain,
    getById,
    getByName,
    loading,
    loaded,
    error,
    isExpired,
  }
}
