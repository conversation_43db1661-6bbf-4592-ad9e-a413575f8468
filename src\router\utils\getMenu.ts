import type { RouteRecordRaw } from 'vue-router'

export function getMenu(routers: RouteRecordRaw[], isHidden = true) {
  const paths = (nodes: RouteRecordRaw[]) => {
    nodes.sort((a, b) => {
      const orderA = a.meta?.order !== undefined ? a.meta.order : Number.NEGATIVE_INFINITY // 将 undefined 视为最小值
      const orderB = b.meta?.order !== undefined ? b.meta.order : Number.NEGATIVE_INFINITY // 将 undefined 视为最小值
      return orderB - orderA // 降序排序
    })
    return nodes.flatMap((v) => {
      if (isHidden && v.meta?.hidden)
        return []
      const temp = {
        ...v,
        path: v.meta!.fullPath!,

      }
      if (v.children) {
        if (temp.children?.length === 1 && !v.children[0]?.path) {
          const meta = { ...temp.meta || {} }
          Object.assign(temp, { ...v.children[0], path: v.children[0]?.meta?.fullPath, children: [] })

          Object.entries(meta!).forEach(([key]) => {
            if (!temp.meta![key])
              temp.meta![key] = meta[key] as any
          })
        }
        else {
          temp.children = paths(v.children)
        }
      }
      if (temp.children?.length === 1 && temp.children[0]?.path.startsWith('/'))
        temp.path = temp.children[0]!.meta!.fullPath!

      return temp
    })
  }
  const menu = paths(deepCopy(routers))
  return menu
}
