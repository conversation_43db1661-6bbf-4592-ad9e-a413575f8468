<template>
  <div>
    <div v-if="!files?.length" class="text-gray-400">
      暂无附件
    </div>
    <div v-else class="w-full">
      <div class="grid auto-rows-fr gap-4" style="grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));">
        <div
          v-for="(file, index) in files" :key="file.id"
          class="flex items-center border border-gray-100 rounded-lg bg-white p-3 shadow-sm transition-all duration-200 ease-in-out hover:border-gray-200 hover:shadow-md"
        >
          <div class="mr-3 text-2xl text-primary">
            <c-icon-paper-clip-outlined v-if="file.fileType === 0" />
            <c-icon-picture-outlined v-else-if="file.fileType === 1" />
            <c-icon-file-markdown-outlined v-else-if="file.fileType === 2" />
            <c-icon-file-zip-outlined v-else-if="file.fileType === 3" />
            <c-icon-play-square-outlined v-else-if="file.fileType === 4" />
            <c-icon-customer-service-outlined v-else-if="file.fileType === 5" />
            <c-icon-file-ppt-outlined v-else-if="file.fileType === 6" />
          </div>
          <div class="min-w-0 flex-1">
            <div class="mb-1 truncate text-sm text-gray-700">
              <span class="inline-block truncate" :title="file.originalName || ''">{{ formatFileName(file.originalName) }}</span>
            </div>
            <div class="text-xs text-gray-400">{{ formatFileSize(file.length) }}</div>
          </div>
          <div class="flex items-center gap-1">
            <a-tooltip title="预览">
              <a-button type="text" class="rounded-full transition-colors hover:bg-gray-100 !p-1" @click="previewFile(file)">
                <template #icon><c-icon-eye-outlined /></template>
              </a-button>
            </a-tooltip>
            <a-tooltip title="下载">
              <a-button type="text" class="rounded-full transition-colors hover:bg-gray-100 !p-1" @click="downloadFile(file)">
                <template #icon><c-icon-download-outlined /></template>
              </a-button>
            </a-tooltip>
            <a-tooltip v-if="showRemove" title="移除">
              <a-button type="text" class="rounded-full text-red-500 transition-colors hover:bg-red-50 !p-1 hover:text-red-600" @click="handleRemove(file, index)">
                <template #icon><c-icon-delete-outlined /></template>
              </a-button>
            </a-tooltip>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { UploadFileInfo } from '@/api/models'
import { FileType } from '@/api/models'
import { joinFilePath } from '@/components/FileManager/uilt'
import { message } from 'ant-design-vue'

type GUID = string

defineProps<{
  files?: UploadFileInfo[] | undefined | null
  showRemove?: boolean
}>()

const emit = defineEmits<{
  (e: 'remove', id: GUID, data: UploadFileInfo, index: number): void
}>()

// 格式化文件名，保留前段和后缀
function formatFileName(fileName: string | null | undefined): string {
  if (!fileName) {
    return '未命名文件'
  }

  const lastDotIndex = fileName.lastIndexOf('.')
  if (lastDotIndex === -1) {
    return fileName
  }

  const name = fileName.slice(0, lastDotIndex)
  const ext = fileName.slice(lastDotIndex)

  if (name.length <= 10) {
    return fileName
  }

  return `${name.slice(0, 5)}...${name.slice(-5)}${ext}`
}

// 处理移除
function handleRemove(file: UploadFileInfo, index: number) {
  if (file.id)
    emit('remove', file.id, file, index)
}

// 格式化文件大小
function formatFileSize(size: number): string {
  if (size === 0)
    return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(size) / Math.log(k))
  return `${(size / (k ** i)).toFixed(2)} ${sizes[i]}`
}

// 预览文件
function previewFile(file: UploadFileInfo) {
  const fileUrl = joinFilePath(file as UploadFileInfo)
  if (!fileUrl) {
    message.error('文件路径无效')
    return
  }

  // 根据文件类型决定预览方式
  switch (file.fileType) {
    case FileType.图片:
      window.open(fileUrl, '_blank')
      break
    case FileType.文档:
    case FileType.幻灯片:
      // 使用在线预览服务，如 Microsoft Office Online Viewer
      window.open(fileUrl, '_blank')
      break
    case FileType.视频:
    case FileType.音频:
      window.open(fileUrl, '_blank')
      break
    default:
      message.info('该文件类型暂不支持预览')
  }
}

// 下载文件
function downloadFile(file: UploadFileInfo) {
  const fileUrl = joinFilePath(file as UploadFileInfo)
  if (!fileUrl) {
    message.error('文件路径无效')
    return
  }

  const link = document.createElement('a')
  link.href = fileUrl
  link.download = file.originalName || file.fileName || 'download'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}
</script>

<style lang="less" scoped>
.view-file {
  .empty-tip {
    color: #999;
    text-align: center;
    padding: 20px 0;
  }

  .file-list {
    .file-item {
      display: flex;
      align-items: center;
      padding: 12px;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .file-icon {
        font-size: 24px;
        margin-right: 12px;
        color: #1890ff;
      }

      .file-info {
        flex: 1;
        min-width: 0;

        .file-name {
          font-size: 14px;
          color: #333;
          margin-bottom: 4px;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .file-size {
          font-size: 12px;
          color: #999;
        }
      }

      .file-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
}
</style>
