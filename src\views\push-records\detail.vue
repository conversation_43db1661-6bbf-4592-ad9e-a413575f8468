<template>
  <div class="w-100% flex-1 space-y-4">
    <a-card :title="`合作单位 —— ${statistics.deptName}`" :bordered="false" style="width: 100%">
      <div class="grid grid-cols-3 mb-4">
        <div>推送要求：微信群内通知</div>
        <div>
          合同时间：{{ dateTime(statistics.contractStartTime, 'YYYY-MM-DD') }} 至 {{ dateTime(statistics.contractEndTime, 'YYYY-MM-DD') }}
        </div>
        <div class="ml-4">
          合同状态：
          <span :class="statusColorMap[getContractStatus(statistics.contractStartTime!, statistics.contractEndTime!)]">{{ getContractStatus(statistics.contractStartTime!, statistics.contractEndTime!) }}</span>
        </div>
      </div>

      <a-divider />

      <!-- 第二行：用户信息 -->
      <div class="grid grid-cols-3 mb-6 gap-x-8">
        <div v-for="(item, index) in users" :key="item.id" class="text-sm space-y-2">
          <div>用户{{ index + 1 }}：{{ item.name }}</div>
          <a-checkbox-group v-model:value="item.deptPushChannels" class="space-y-2" style="width: 100%" @change="userChange($event, item.id)">
            <div class="w-full">
              用户{{ index + 1 }}电话：{{ item.phoneNumber }}
              <a-checkbox :value="DeptPushChannel.电话" />
            </div>
            <div class="w-full">
              用户{{ index + 1 }}微信：13112341234
              <a-checkbox :value="DeptPushChannel.微信" />
            </div>
            <div class="w-full">
              用户{{ index + 1 }}邮箱：{{ item.email }}
              <a-checkbox :value="DeptPushChannel.邮箱" />
            </div>
          </a-checkbox-group>
        </div>
      </div>

      <a-divider />

      <!-- 第三行：推送日期 + 查询按钮 -->
      <div class="mb-4 flex items-center gap-2">
        <span>推送日期：</span>
        <c-range-picker v-model:start-time="params.startTime" v-model:end-time="params.endTime" style="width: 280px" />

        <a-button type="link" @click="getDate('近7天')">近7天</a-button>
        <a-button type="link" @click="getDate('近1个月')">近1个月</a-button>
        <a-button
          type="link" @click="() => {
            params.isContract = true
            proTableRef?.search()
          }"
        >
          合同期内
        </a-button>
        <a-button type="primary" @click="proTableRef?.search()">查询</a-button>
        <a-button @click="onReset">重置</a-button>
      </div>

      <!-- 第四行：统计信息 -->
      <div class="flex flex-wrap gap-x-6 text-base text-info">
        <div>机器推送（条）：{{ statistics.autoPushCount }}</div>
        <div>人工复核推送（条）：{{ statistics.peoplePushCount }}</div>
        <div>日报（篇）：{{ statistics.dayReportCount }}</div>
        <div>月报（篇）：{{ statistics.moonReportCount }}</div>
        <div>专题（项）：{{ statistics.specialSubjectCount }}</div>
        <div>事件跟踪（次）：{{ statistics.eventCount }}</div>
      </div>
    </a-card>
    <a-card :title="`推送记录 ${totals} `" :bordered="false" style="width: 100%">
      <template #extra><a-button type="primary">新增推送</a-button></template>
      <c-pro-table
        ref="proTableRef" size="small"
        :row-key="(record) => record.id"
        :columns="columns"
        :api="api.DeptPushLogs.GetPushLogAsync"
        immediate
        :get-params="params"
        @after-fetch="afterFetch"
      />
    </a-card>
  </div>
</template>

<script lang='ts' setup>
import type { UserViewModel } from '@/api/models'
import * as api from '@/api'
import { DeptPushChannel, PushLogStatisticsByDept } from '@/api/models'
import { Guid } from '@/utils/GUID'
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import { getContractStatus } from './utils'

definePage({
  meta: {
    title: '推送记录详情',
    icon: 'ReadOutlined',
    hidden: true,
  },
})

const statusColorMap: Record<string, string> = {
  未开始: 'text-gray-500',
  正常: 'text-success',
  即将过期: 'text-warning',
  已过期: 'text-error',
}

const params = ref<{
  departmentId: GUID
  startTime?: Date | null
  endTime?: Date | null
  /** 合同期内 */
  isContract: boolean
}>({
  departmentId: Guid.empty,
  startTime: null,
  endTime: null,
  isContract: null,
})

const proTableRef = useTemplateRef('proTableRef')

const totals = ref(0)

const columns = ref([
  {
    title: '推送摘要',
    dataIndex: 'entityType',
  },
  { title: '分类', dataIndex: 'responseMessage' },
  { title: '推送对象及渠道', dataIndex: '[createdUser,name]' },
  { title: '推送时间', dataIndex: 'createdTime', dateFormat: 'YYYY-MM-DD HH:mm:ss' },
])

function onReset() {
  params.value = {
    departmentId: Guid.empty,
    startTime: null,
    endTime: null,
    isContract: null,
  }
  proTableRef.value?.search()
}

function afterFetch(e: { totals: number }) {
  totals.value = e.totals
}

const users = ref<UserViewModel[]>([])

async function userChange(values: DeptPushChannel[], userId: string) {
  await api.DeptPushLogs.SavePushUser_PostAsync({ userId }, values)
  message.success('设置成功')
}

const statistics = ref(new PushLogStatisticsByDept())

async function getData() {
  users.value = await api.DeptPushLogs.GetPushUserAsync({ departmentId: params.value.departmentId })
  statistics.value = await api.DeptPushLogs.GetPushLogStatisticsByDept_PostAsync({ departmentId: params.value.departmentId })
}

function getDate(type: string) {
  const now = dayjs()
  if (type === '近7天') {
    params.value.startTime = now.subtract(6, 'day').startOf('day') // 含今天，一共7天
    params.value.endTime = now.endOf('day')
  }
  else if (type === '近1个月') {
    params.value.startTime = now.subtract(29, 'day').startOf('day') // 含今天，共30天
    params.value.endTime = now.endOf('day')
  }
  proTableRef.value?.search()
}

const route = useRoute()

onMounted(() => {
  if (route.query?.id)
    console.log('id', route.query?.id)
  params.value.departmentId = route.query?.id as string

  getData()
})
</script>

<style scoped lang="less">
:deep(.c2-table-striped) td {
  background-color: @colorPrimaryBg;
}

:deep(.ant-table-thead tr th) {
  background: @colorPrimaryBgHover !important;
}
:deep(.table-main) {
  margin-top: 0 !important;
  border: none !important;
  padding: 0 !important;
}
</style>
