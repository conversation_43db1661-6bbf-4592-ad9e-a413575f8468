<template>
  <!-- <div :class="{ 'edit-form': !readOnly }">
    <c-form ref="formRef" :read-only="readOnly" :model="formState" name="basic" autocomplete="off">
      <a-descriptions
        bordered title="基本信息" :label-style="{ width: '140px' }" :content-style="{ width: '300px' }"
        :column="{ md: 4, xxl: 8 }" size="small"
      >
        <a-descriptions-item label="事件名称" :span="4" class="required">
          <a-form-item name="name" :rules="[{ required: true, message: '请输入事件标题内容!' }]">
            <c-input v-model:value="formState.name!" placeholder="请输入事件标题内容" allow-clear show-count :maxlength="100" />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="信息标题" :span="4" class="required">
          <a-form-item name="title" :rules="[{ required: true, message: '请输入信息标题内容!' }]">
            <c-input v-model:value="formState.title!" placeholder="请输入信息标题内容" allow-clear show-count :maxlength="100" />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="信息正文" :span="4" class="required">
          <a-form-item name="content" :rules="[{ required: true, message: '请输入信息正文内容!' }]">
            <c-textarea
              v-model:value="formState.content!" placeholder="请输入信息正文内容" allow-clear show-count
              :maxlength="5000"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="事发时间" :span="2" class="required">
          <a-form-item name="time" :rules="[{ required: true, message: '请输入时间!' }]">
            <c-date-picker
              v-model:value="formState.time!" :show-time="{ format: 'HH:mm' }" format="YYYY-MM-DD HH:mm"
              style="width: 100%;"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="事件等级" :span="2">
          <a-form-item name="level">
            <c-standard
              v-model:value="formState.level" el="select" s-key="event_level" show-search
              :field-names="{ label: 'label', value: 'label' }" placeholder="选择事件等级"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="一级分类" :span="2" class="required">
          <a-form-item name="category" :rules="[{ required: true, message: '请选择一级分类!' }]">
            <c-standard
              v-model:value="formState.category" el="select" s-key="event_department_type" show-search
              :field-names="{ label: 'label', value: 'label' }" placeholder="选择一级分类"
              @change="() => { formState.subCategory = null }"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="二级分类" :span="2">
          <c-standard
            v-model:value="formState.subCategory" s-key="event_department_type" show-search
            :field-names="{ label: 'label', value: 'value', options: 'children' }"
          >
            <template #el="{ loading, options }">
              <a-form-item
                v-if="options.find(v => v.label === formState.category)?.children?.length"
                name="subCategory"
              >
                <c-select
                  v-model:value="formState.subCategory!" :loading="loading"
                  :options="options.find(v => v.label === formState.category)?.children ?? []"
                />
              </a-form-item>
            </template>
          </c-standard>
        </a-descriptions-item>
        <a-descriptions-item label="联系人" :span="2" class="required">
          <a-form-item name="contacts" :rules="[{ required: true, message: '请输入联系人!' }]">
            <c-input v-model:value="formState.contacts!" placeholder="请输入联系人" allow-clear show-count :maxlength="50" />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="联系电话" :span="2" class="required">
          <a-form-item name="contactsPhone" :rules="[{ required: true, message: '请输入联系电话!' }]">
            <c-input
              v-model:value="formState.contactsPhone!" placeholder="请输入联系电话" allow-clear show-count
              :maxlength="50"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="审签人" :span="4" class="required">
          <a-form-item name="reviewer" :rules="[{ required: true, message: '请输入审签人!' }]">
            <c-input v-model:value="formState.reviewer!" placeholder="请输入审签人姓名" allow-clear show-count :maxlength="50" />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="附件" :span="4" class="required">
          <a-form-item name="attachment" :rules="[{ required: true, message: '请上传附件!' }]">
            <Upload v-model:value="formState.attachment" v-model:file-list="attachmentFileView" />
          </a-form-item>
        </a-descriptions-item>
      </a-descriptions>
      <div class="h-4" />
      <a-descriptions
        title="其他信息" bordered :label-style="{ width: '140px' }" :content-style="{ width: '300px' }"
        :column="{ md: 4, xxl: 8 }" size="small"
      >
        <a-descriptions-item label="是否紧急" :span="2" class="required">
          <a-form-item name="isItUrgent" :rules="[{ required: true, message: '请选择!' }]">
            <c-boolean-select v-model:value="formState.isItUrgent" placeholder="请选择" />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="是否已向省级党委政府报告" :span="2" class="required">
          <a-form-item name="isItReported" :rules="[{ required: true, message: '请选择!' }]">
            <c-boolean-select v-model:value="formState.isItReported" placeholder="请选择" />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="报告时间" :span="2" class="required">
          <a-form-item name="reportedTime" :rules="[{ required: true, message: '请选择!' }]">
            <c-date-picker
              v-model:value="formState.reportedTime!" :show-time="{ format: 'HH:mm' }"
              format="YYYY-MM-DD HH:mm" style="width: 100%;"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="涉及学段" :span="2" class="required">
          <a-form-item name="period" :rules="[{ required: true, message: '请选择!' }]">
            <c-standard
              :value="formState.period?.split(',')"
              el="select"
              s-key="event_period" show-search mode="multiple"
              :field-names="{ label: 'label', value: 'label' }"
              placeholder="选择涉及学段" @change=" v => v.length > 0 ? formState.period = v.join(',') : formState.period = null"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="是否涉及师生" :span="8" class="required">
          <a-form-item name="isTeachersAndStudent" :rules="[{ required: true, message: '请选择!' }]">
            <c-enum-radio
              v-model:value="formState.isTeachersAndStudent" :enum="VerificationStatus" placeholder="请输入"
              @change="teachersChange"
            />
          </a-form-item>
        </a-descriptions-item>
        <template v-if="formState.isTeachersAndStudent === VerificationStatus.是">
          <template v-for="(teacher, index) in formState.teachersAndStudentData" :key="index">
            <a-descriptions-item :span="2">
              <template #label>
                <div>
                  <a-button
                    type="primary" shape="circle" ghost size="small" danger :icon="h(MinusOutlined)"
                    @click="formState.teachersAndStudentData?.splice(index, 1)"
                  />
                  <span class="ml-1">师生类型</span>
                </div>
              </template>
              <a-form-item :rules="[{ required: true, message: '请选择!' }]">
                <c-enum-radio v-model:value="teacher.type" :enum="TeacherStudentType" placeholder="请输入" />
              </a-form-item>
            </a-descriptions-item>
            <a-descriptions-item label="姓名" :span="2">
              <a-form-item>
                <c-input v-model:value="teacher.name!" placeholder="姓名" allow-clear />
              </a-form-item>
            </a-descriptions-item>
            <a-descriptions-item label="身份证号码" :span="2">
              <a-form-item>
                <c-input v-model:value="teacher.idCard!" placeholder="身份证号码" allow-clear />
              </a-form-item>
            </a-descriptions-item>
            <a-descriptions-item label="院校名称" :span="2">
              <a-form-item>
                <C2TreeSelect v-model:value="teacher.schoolId!" :api="api.DepartmentManage.GetAllDepartmentsAsync" />
              </a-form-item>
            </a-descriptions-item>
          </template>
          <a-descriptions-item :span="8">
            <a-button
              type="primary" style="width: 100%" ghost :icon="h(PlusOutlined)"
              @click="formState.teachersAndStudentData?.push(new EventTeacherStudent())"
            >
              新增
            </a-button>
          </a-descriptions-item>
        </template>
        <a-descriptions-item label="是否涉及学校(机构)" :span="2" class="required">
          <a-form-item name="isItInvolveSchools" :rules="[{ required: true, message: '请选择!' }]">
            <c-enum-radio v-model:value="formState.isItInvolveSchools" :enum="VerificationStatus" placeholder="请输入" />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="学校(机构)" :span="2">
          <a-form-item name="schoolId">
            <C2TreeSelect v-model:value="formState.schoolId!" :api="api.DepartmentManage.GetAllDepartmentsAsync" />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="所在城市" :span="2">
          <a-form-item name="city">
            <c-standard
              v-model:value="formState.city" el="select" s-key="county" show-search
              :field-names="{ label: 'label', value: 'label' }" placeholder="选择所在城市"
              @change="() => { formState.counties = null }"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="所属区县" :span="2">
          <c-standard
            v-model:value="formState.counties" s-key="county" show-search
          >
            <template #el="{ loading, options }">
              <a-form-item v-if="options.find(v => v.label === formState.city)?.children?.length" name="counties">
                <c-select
                  v-model:value="formState.counties!" :loading="loading"
                  :field-names="{ label: 'label', value: 'label', options: 'children' }"
                  :options="options.find(v => v.label === formState.city)?.children ?? []"
                />
              </a-form-item>
            </template>
          </c-standard>
        </a-descriptions-item>
        <a-descriptions-item label="事件发生地点" :span="4">
          <a-form-item name="address1">
            <c-input
              v-model:value="formState.address1!" placeholder="请输入事件发生地点" allow-clear show-count
              :maxlength="50"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="是否引发舆情" :span="2" class="required">
          <a-form-item name="isPublicOpinion" :rules="[{ required: true, message: '请选择!' }]">
            <c-enum-radio v-model:value="formState.isPublicOpinion" :enum="VerificationStatus" placeholder="请输入" />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="舆情访问链接" :span="2">
          <a-form-item name="publicOpinionLink">
            <c-input
              v-model:value="formState.publicOpinionLink!" placeholder="请输入舆情访问链接，格式为：http://或https://开头" allow-clear show-count
              :maxlength="500"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="舆情态势描述（当前）" :span="4">
          <a-form-item name="publicOpinionSituation">
            <c-textarea
              v-model:value="formState.publicOpinionSituation!" placeholder="请输入舆情态势描述" allow-clear show-count
              :maxlength="1000"
            />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="是否涉及伤亡" :span="2" class="required">
          <a-form-item name="isDeath" :rules="[{ required: true, message: '请选择!' }]">
            <c-enum-radio v-model:value="formState.isDeath" :enum="VerificationStatus" placeholder="请输入" />
          </a-form-item>
        </a-descriptions-item>
        <a-descriptions-item label="是否聚集" :span="2" class="required">
          <a-form-item name="isGatherACrowd" :rules="[{ required: true, message: '请选择!' }]">
            <c-enum-radio v-model:value="formState.isGatherACrowd" :enum="VerificationStatus" placeholder="请输入" />
          </a-form-item>
        </a-descriptions-item>
      </a-descriptions>
    </c-form>
  </div> -->
</template>

<script lang='ts' setup>
// import type { UploadFileInfo } from '@/api/models'
// import * as api from '@/api'
// import { EventTeacherStudent, PublicEventEditModel, TeacherStudentType, VerificationStatus } from '@/api/models'
// import { MinusOutlined, PlusOutlined } from '@ant-design/icons-vue'
// import { message } from 'ant-design-vue'

// const props = defineProps<{
//   readOnly?: boolean
//   attachmentFile?: UploadFileInfo[] | null | undefined
// }>()

// const formState = defineModel<PublicEventEditModel>({
//   required: true,
//   default: () => (new PublicEventEditModel()),
// })

// const attachmentFileView = ref(props.attachmentFile)

// const formRef = useTemplateRef('formRef')

// watch(() => props.readOnly, (v) => {
//   if (v) {
//     formRef.value?.baseEl?.clearValidate()
//   }
// })

// async function onSubmit() {
//   formState.value.time = dayjs(formState.value.time!)
//   formState.value.reportedTime = dayjs(formState.value.reportedTime!)
//   formState.value.province = '广西壮族自治区'
//   return await formRef.value?.baseEl?.validate().then(() => api.EventManage.Update_PostAsync(formState.value)).catch((err: { message: any }) => {
//     if (err.message)
//       message.error(`保存失败：${err.message}`)
//     throw err
//   })
// }

// function teachersChange(val: VerificationStatus) {
//   if (val === VerificationStatus.是)
//     formState.value.teachersAndStudentData?.push(new EventTeacherStudent())
//   else formState.value.teachersAndStudentData = []
// }

// defineExpose({
//   onSubmit,
// })
</script>

<style scoped lang="less">
:deep(.ant-form-item) {
  margin-bottom: 0;
}

.edit-form {
  :deep(.ant-descriptions-item-label.required span) {
    display: flex;

    &::before {
      display: block;
      content: '*';
      color: red;
      padding-right: 4px;
    }
  }
}
</style>
