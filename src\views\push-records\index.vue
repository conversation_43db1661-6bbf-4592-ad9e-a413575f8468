<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-06-26 11:48:28
 * @LastEditors: 景 彡
-->
<template>
  <c-pro-table ref="proTableRef" size="small" :row-key="(record) => record.id" :columns="columns" :api="api.PushLogs.GetPushLogListAsync" :get-params="params" immediate :show-search="false">
    <template #header>
      <div class="flex items-center text-base">
        <div>
          <span>类型：</span>
          <a-radio-group v-model:value="params.entityType" @change="proTableRef?.search()">
            <a-radio-button :value="undefined">全部</a-radio-button>
            <a-radio-button :value="PushType.舆情">舆情</a-radio-button>
            <a-radio-button :value="PushType.事件">事件</a-radio-button>
            <a-radio-button :value="PushType.专题">专题</a-radio-button>
          </a-radio-group>
        </div>
        <div class="ml-4">
          <span>状态：</span>
          <a-radio-group v-model:value="params.status" @change="proTableRef?.search()">
            <a-radio-button :value="undefined">全部</a-radio-button>
            <a-radio-button :value="PushStatus.成功">成功</a-radio-button>
            <a-radio-button :value="PushStatus.失败">失败</a-radio-button>
          </a-radio-group>
        </div>
      </div>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'status'">
        <a-tag :color="record.status === PushStatus.成功 ? 'success' : 'error'">{{ PushStatus[record.status] }}</a-tag>
      </template>
    </template>
  </c-pro-table>
</template>

<script lang='ts' setup>
import * as api from '@/api'
import { PushStatus, PushType } from '@/api/models'

definePage({
  meta: {
    title: '教育厅推送记录',
    local: true,
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '推送管理及记录',
        local: true,
        icon: 'UnorderedListOutlined',
        order: 7,
      },
    },
  },
})

const proTableRef = useTemplateRef('proTableRef')

const params = ref<{ entityType: PushType | undefined, status: PushStatus | undefined }>({
  status: undefined,
  entityType: undefined,
})

const columns = ref([
  {
    title: '推送类型',
    dataIndex: 'entityType',
  },
  { title: '响应信息', dataIndex: 'responseMessage' },
  { title: '用户', dataIndex: '[createdUser,name]' },
  { title: '推送时间', dataIndex: 'createdTime', dateFormat: 'YYYY-MM-DD HH:mm:ss' },
  { title: '推送状态', dataIndex: 'status' },
])
</script>

<style scoped>

</style>
