<template>
  <div class="c-text">
    <div class="xl: grid grid-cols-1 w-100% bg-bg-container p-4 lg:grid-cols-[1fr_3fr]">
      <div>
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">事件处置占比</div>
        </div>
        <div class="h-300px w-full"><Echart :options="pieChartOptions" /></div>
        <div class="w-100% flex">
          <div class="flex flex-1 items-center justify-center">
            <div class="h-46px w-8px bg-[linear-gradient(180deg,rgba(98,90,255,1)_0%,rgba(60,80,455,1)_100%)]" />
            <div class="ml-4 h-100% flex flex-col justify-between">
              <div>总舆情</div>
              <div class="mt-4"> <span class="text-24px font-bold">{{ eventHandData.allCount }}</span>起</div>
            </div>
          </div>
          <div class="flex flex-1 items-center justify-center">
            <div class="h-46px w-8px bg-[linear-gradient(180deg,rgba(8,189,143,1)_0%,rgba(0,194,181,1)_100%)]" />
            <div class="ml-4 h-100% flex flex-col justify-between">
              <div>已处置</div>
              <div class="mt-4"> <span class="text-24px font-bold">{{ eventHandData.processingCount }}</span>起</div>
            </div>
          </div>
        </div>
      </div>
      <div class="ml-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
            <div class="ml-4 text-16px font-bold">事件处置趋势</div>
          </div>
          <!-- <a-select
            v-model:value="disposeOfValue"
            style="width: 120px"
          >
            <a-select-option value="year">最近一年</a-select-option>
            <a-select-option value="month">最近一个月</a-select-option>
            <a-select-option value="day">最近7天</a-select-option>
          </a-select> -->
        </div>
        <div class="h-400px w-full"><Echart :options="lineChartOptions" /></div>
      </div>
    </div>

    <div class="grid grid-cols-1 mt-4 w-100% bg-bg-container p-4 lg:grid-cols-[1fr_2fr]">
      <div class="ml-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">舆情类别统计</div>
        </div>
        <div class="h-400px w-full"><Echart :options="typeBarChartOptions" /></div>
      </div>
      <div class="ml-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">舆情趋势统计</div>
        </div>
        <div class="h-400px w-full"><Echart :options="doubleLineChartOptions" /></div>
      </div>
    </div>

    <div class="mt-4 flex items-center justify-between bg-bg-container p-4">
      <div class="ml-4 text-16px font-bold">舆情多维度统计</div>
      <div>
        时间区间：<a-range-picker v-model:value="dateValue" />
        <c-button type="primary" class="ml-2" @click="getEventCharDatasAsync">查询</c-button>
      </div>
    </div>

    <div class="grid grid-cols-1 mt-4 w-100% bg-bg-container p-4 lg:grid-cols-3">
      <div class="ml-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">重大舆情</div>
        </div>
        <div class="h-400px w-full">
          <div class="recent-increments">
            <div class="increments-box">
              <div class="event-rise-box">
                <span class="warning-number">{{ majorPublicOpinion }}</span>
                <p>起</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="ml-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">安全隐患</div>
        </div>
        <div class="h-400px w-full">
          <div class="recent-increments">
            <div class="increments-box">
              <div class="event-rise-box">
                <span class="text-7 c-warning font-bold">{{ potentialSafetyHazard }}</span>
                <p>起</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="ml-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">舆情标签</div>
        </div>
        <div class="h-400px w-full"><Echart :options="tagChartOptions" /></div>
      </div>
    </div>

    <div class="grid grid-cols-1 mt-4 w-100% lg:grid-cols-[1fr_2fr]">
      <div class="bg-bg-container p-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">单位类别舆情统计</div>
        </div>
        <div class="h-400px w-full"><Echart :options="deptTypeChartOptions" /></div>
      </div>
      <div class="ml-4 bg-bg-container p-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">专题统计</div>
        </div>
        <div class="h-400px w-full"><Echart :options="specialSubjectChartOption" /></div>
      </div>
    </div>

    <div class="mt-4 w-100% bg-bg-container p-4">
      <div class="ml-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">舆情涉及人数趋势</div>
        </div>
        <div class="h-400px w-full"><Echart :options="numberOfPeopleOption" /></div>
      </div>
    </div>

    <div class="mt-4 w-100% bg-bg-container p-4">
      <div class="ml-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">所涉单位数据</div>
        </div>
        <div class="h-400px w-full"><Echart :options="deptChartOption" /></div>
      </div>
    </div>

    <div class="grid grid-cols-1 mt-4 w-100% lg:grid-cols-2">
      <div class="bg-bg-container p-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">来源统计</div>
        </div>
        <div class="h-400px w-full"><Echart :options="sourceChartOption" /></div>
      </div>
      <div class="ml-4 bg-bg-container p-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">地域发生地统计</div>
        </div>
        <div class="h-400px w-full"><Echart :options="regionBarChartOptions" /></div>
      </div>
    </div>

    <div class="mt-4 w-100% bg-bg-container p-4">
      <div class="ml-4">
        <div class="flex items-center">
          <div class="h-22px w-5px rounded-r-10px bg-#0AB389" />
          <div class="ml-4 text-16px font-bold">专题监控</div>
        </div>
        <div class="grid grid-cols-1 w-full gap-6 pt-8 2xl:grid-cols-3 3xl:grid-cols-4 md:grid-cols-2">
          <div v-for="(item, index) in topicData" :key="index" class="overflow-hidden rounded-xl shadow-lg">
            <div class="h-2 from-[#08bd8f] to-[#50adfa] bg-gradient-to-r" />
            <div class="p-4 space-y-6">
              <div class="mb-2 flex items-center justify-between">
                <div class="py-4 text-base font-bold">{{ item.label }}</div>
                <a-tag :color="timeJudgment(item.json?.['监控开始时间'], item.json?.['监控结束时间'])?.color">{{ timeJudgment(item.json?.["监控开始时间"], item.json?.["监控结束时间"])?.status }}</a-tag>
              </div>
              <div>
                <div class="rounded-lg bg-slate-50 p-4 dark:bg-slate-800/50">
                  <div class="mb-2 flex items-center justify-between">
                    <div class="mb-2 text-sm text-[#50adfa] font-medium">专题描述</div>
                  </div>
                  <p class="text-slate-700 dark:text-slate-300">{{ item.json?.["专题描述"] }}</p>
                </div>
              </div>
              <div class="grid grid-cols-1 gap-4 sm:grid-cols-3">
                <div class="flex flex-col justify-center rounded-lg bg-[#08bd8f]/10 p-4 text-center">
                  <h3 class="mb-1 text-sm text-[#08bd8f] font-medium">舆情总数</h3>
                  <div class="text-3xl text-slate-800 font-bold dark:text-white">{{ item.json?.["舆情总数"] }}</div>
                </div>
                <div class="rounded-lg bg-[#50adfa]/10 p-4 sm:col-span-2">
                  <h3 class="mb-1 text-sm text-[#50adfa] font-medium">监控时间段</h3>
                  <div class="flex items-center justify-between">
                    <div class="rounded bg-white px-3 py-1 text-center shadow-sm dark:bg-slate-700">
                      <p class="text-xs text-slate-500 dark:text-slate-400">开始</p>
                      <p class="text-slate-800 font-medium dark:text-white">{{ item.json?.["监控开始时间"] }}</p>
                    </div>
                    <div class="mx-2 h-[2px] flex-1 from-[#08bd8f] to-[#50adfa] bg-gradient-to-r" />
                    <div class="rounded bg-white px-3 py-1 text-center shadow-sm dark:bg-slate-700">
                      <p class="text-xs text-slate-500 dark:text-slate-400">结束</p>
                      <p class="text-slate-800 font-medium dark:text-white">{{ item.json?.["监控结束时间"] }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang='ts' setup>
import * as api from '@/api'
import { EventHandlingRatioViewModel } from '@/api/models'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import Echart from './components/Echart.vue'
import { useChartData } from './useChartData'

dayjs.extend(isBetween)

definePage({
  meta: {
    title: '统计分析',
    icon: 'BarChartOutlined',
    noCache: true,
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '统计分析',
        local: true,
        icon: 'BarChartOutlined',
        order: 3,
        noCache: true,
      },
    },
  },
})

const { pieChartOptions, lineChartOptions, doubleLineChartOptions, typeBarChartOptions, regionBarChartOptions, sourceChartOption, deptChartOption, tagChartOptions, deptTypeChartOptions, majorPublicOpinion, specialSubjectChartOption, potentialSafetyHazard, numberOfPeopleOption, topicData, loadData, dimensionLoad } = useChartData()

const dateValue = ref<[Dayjs, Dayjs]>()

function getEventCharDatasAsync() {
  const start = dateValue.value?.[0].toDate()
  const end = dateValue.value?.[1].toDate()
  dimensionLoad(start, end)
}

const disposeOfValue = ref('year')

const eventHandData = ref(new EventHandlingRatioViewModel())

async function getData() {
  eventHandData.value = await api.Statistics.EventHandlingRatio_GetAsync()
  loadData(eventHandData.value, disposeOfValue.value)
}

enum TimeJudgmentEnum {
  未开始 = 0,
  跟踪中 = 1,
  已过期 = 2,
}

function timeJudgment(start: string | undefined, end: string | undefined) {
  const now = dayjs()
  const startDate = dayjs(start)
  const endDate = dayjs(end)

  if (!end) {
    return now.isAfter(startDate) || now.isSame(startDate)
      ? { status: TimeJudgmentEnum[TimeJudgmentEnum.跟踪中], color: '#52c41a' }
      : { status: TimeJudgmentEnum[TimeJudgmentEnum.未开始], color: '#1890ff' }
  }

  if (now.isBetween(startDate, endDate, null, '[]')) {
    return { status: TimeJudgmentEnum[TimeJudgmentEnum.跟踪中], color: '#52c41a' }
  }
  if (now.isBefore(startDate)) {
    return { status: TimeJudgmentEnum[TimeJudgmentEnum.未开始], color: '#1890ff' }
  }
  if (now.isAfter(endDate)) {
    return { status: TimeJudgmentEnum[TimeJudgmentEnum.已过期], color: '#999999' }
  }
  return { status: TimeJudgmentEnum[TimeJudgmentEnum.跟踪中], color: '#52c41a' }
}

onMounted(() => {
  nextTick(() => {
    getData()
    dimensionLoad(undefined, undefined)
  })
})
</script>

<style scoped>
.recent-increments {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}
.increments-box {
  position: relative;
  width: 140px;
  height: 140px;
  margin-top: 10px;
  text-align: center;
  border-radius: 50%;
  -webkit-box-shadow: 0 0 6px 0 rgba(204, 204, 204, 0.5);
  box-shadow: 0 0 6px 0 rgba(204, 204, 204, 0.5);
}
.event-rise-box {
  position: absolute;
  width: 100%;
  top: 42px;
}
.warning-number {
  color: #ff0000b0;
  font-size: 28px;
  font-weight: bold;
}
</style>
