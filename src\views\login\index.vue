<!--
 * @Description:
 * @Author: 景 彡
 * @Date: 2025-02-10 11:46:20
 * @LastEditors: 景 彡
-->
<template>
  <div
    class="login-container fixed bottom-0 top-0 h-100% w-100% flex items-center justify-center overflow-hidden overflow-hidden bg-[url(@/assets/images/bg.png)] bg-cover bg-cover bg-center bg-center bg-no-repeat"
  >
    <div>
      <div
        class="grid grid-cols-2 h-60vh min-h-600px min-w-432px w-60vw center gap-4 rounded-30px bg-bg-container max-xl:grid-cols-1 max-sm:w-90vw"
      >
        <div>
          <div>
            <div class="mb-60px text-center text-20px c-text font-bold">{{ title }}</div>
            <div>
              <a-spin tip="登录中..." :spinning="loading">
                <a-form
                  :model="formState" name="basic" :wrapper-col="{ offset: 2, span: 20 }" autocomplete="off"
                  @finish="onFinish" @finish-failed="onFinishFailed"
                >
                  <a-form-item name="username" :rules="[{ required: true, message: '请输入用户名!' }]">
                    <c-input v-model:value.trim="formState.username" style="height: 40px" placeholder="用户名">
                      <template #prefix>
                        <c-icon-user-outlined />
                      </template>
                    </c-input>
                  </a-form-item>

                  <a-form-item name="password" :rules="[{ required: true, message: '请输入密码!' }]">
                    <c-input-password v-model:value.trim="formState.password" style="height: 40px" placeholder="密码">
                      <template #prefix>
                        <c-icon-lock-outlined />
                      </template>
                    </c-input-password>
                  </a-form-item>

                  <a-form-item :wrapper-col="{ offset: 2, span: 20 }">
                    <a-button
                      type="primary" html-type="submit" style="
                    width: 100%;
                    height: 40px;
                    border-radius: 30px;
                    background: #09B388;
                  "
                    >
                      登录
                    </a-button>
                  </a-form-item>
                </a-form>
              </a-spin>
            </div>
          </div>
        </div>
        <div class="max-xl:hidden">
          <div>
            <img class="h-100% w-100%" src="../../assets/images/login-icon.png">
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { useLogin } from './hook/useLogin'

const { formState, onFinish, onFinishFailed, loading } = useLogin()

const title = import.meta.env.VITE_APP_TITLE

onMounted(() => { })
</script>

<style scoped lang="less">
.ant-spin-container {
  background-color: none;
  opacity: 0;
}

.mc-login-form {
  :deep(.ant-tabs-nav) {
    font-size: 16px;
  }

  :deep(.ant-input-affix-wrapper) {
    border-radius: 30px;
  }
}
</style>
