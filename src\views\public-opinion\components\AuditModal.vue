<template>
  <a-modal v-model:open="open" :title="title || '确定提交审核？'" :confirm-loading="confirmLoading" width="900px" @ok="onSubmit">
    <div class="mb-2 mt-4 text-base font-bold">教育厅舆情ID：</div>
    <a-input v-model:value="jytOpinionId" placeholder="请输入关联教育厅舆情id" />
    <div class="mt-4 c-error"> 若填写，将关联已有教育厅舆情；不填写则会在教育厅系统中自动创建新的舆情。</div>

    <div v-if="Object.keys(historyData).length > 0" class="mt-4">
      <a-card title="修改记录" style="width: 100%">
        <div class="space-y-2">
          <div v-for="(item, key) in historyData" :key="key">
            <div v-html="item" />
          </div>
        </div>
      </a-card>
    </div>
  </a-modal>
</template>

<script lang='ts' setup>
import type { AuditType } from '@/api/models'
import * as api from '@/api'
import { message } from 'ant-design-vue'

const props = defineProps<{
  title: string
  params: {
    id: GUID
    audit: AuditType

  }
}>()

const emit = defineEmits<{
  (e: 'submit'): void
}>()

const open = defineModel<boolean>('open', {
  required: true,
  default: false,
})

const jytOpinionId = defineModel<GUID>('jytOpinionId', {
  required: true,
  default: '',
})

const confirmLoading = ref(false)

async function onSubmit() {
  confirmLoading.value = true
  try {
    await api.OpinionManage.AuditOpinion_PostAsync({ jytOpinionId: jytOpinionId.value }, props.params)
    message.success('提交成功')
    emit('submit')
    open.value = false
    confirmLoading.value = false
  }
  catch (error: any) {
    message.error(error.message)
    confirmLoading.value = false
  }
}

const historyData = ref({})

async function getHistory() {
  historyData.value = await api.OpinionManage.GetHistoryAsync({ opId: props.params.id })
}

watch(() => props.params, () => {
  getHistory()
})
</script>
