<template>
  <c-pro-table
    ref="proTableRef" :row-class-name="(_record, index) => (index % 2 === 1 ? 'c2-table-striped' : 'c2-table-prototype')"
    :row-key="(record) => record.id" size="small"
    :columns="columns"
    :api="api.OpinionManage.GetJytListAsync"
    :show-search="false"
    :show-tool-btn="false" immediate serial-number operation :get-params="params"
  >
    <template #header>
      <div class="w-full flex justify-between">
        <a-radio-group v-model:value="params.audit" @change="proTableRef?.search()">
          <a-radio-button :value="AuditType.查询全部">全部</a-radio-button>
          <a-radio-button :value="AuditType.需要处置">待审核</a-radio-button>
          <a-radio-button :value="AuditType.处置">已上报</a-radio-button>
          <a-radio-button :value="AuditType.已读归档">已取消</a-radio-button>
        </a-radio-group>
        <a-input-search
          v-model:value="params.keyWord"
          placeholder="请输入关键词"
          style="width: 280px"
          size="large"
          @search="proTableRef?.search()"
        />
      </div>
    </template>
    <template #bodyCell="{ column, record }">
      <template v-if="column.dataIndex === 'summary'">
        <div class="flex space-x-2">
          <div v-if="record.dept?.isCooperate" class="w-42px rounded-md bg-#F0B800 text-center text-3 c-#fff">合作</div>
          <div v-if="record.topicId" class="w-42px rounded-md bg-#7948EA text-center text-3 c-#fff">专题</div>
          <div v-if="record.serious" class="w-42px rounded-md bg-#FF5252 text-center text-3 c-#fff">重大</div>
          <div v-if="record.eventId" class="w-42px rounded-md bg-#2A82E4 text-center text-3 c-#fff">事件</div>
        </div>
        <a @click="onEdit(record, true)">{{ record.summary }}</a>
      </template>
      <template v-if="column.dataIndex === 'content'">
        <div>{{ record.content }}</div>
        <div class="flex space-x-2">
          <div v-for="(img, idx) in record.images" :key="idx" class="coverBox size-30px overflow-hidden">
            <c-image
              :src="joinFilePathById(img)" alt="avatar" :preview="true"
              style="height: 30px; width:30px ; object-fit:cover"
            />
          </div>
        </div>
        <div>
          <a v-for="(url, idx) in record.url?.split(/\r?\n/)" :key="idx" :href="url" target="_blank"> {{ url }} </a>
        </div>
      </template>
      <template v-if="column.dataIndex === 'publisher'">
        <div class="flex items-center">
          <c-image
            :src="joinFilePathById(getByName(record.source!)?.iconId!)" alt="avatar" :preview="true"
            style="height: 20px; width:20px ; object-fit:cover"
          />
          <span class="ml-1">{{ record.publisher }}</span>
        </div>
        <div>id: {{ record.sourceId }}</div>
        <div class="c-text-secondary">{{ dateTime(record.published, 'YYYY-MM-DD HH:mm') }}</div>
      </template>
      <template v-if="column.dataIndex === 'createdUser'">
        <div>{{ record.createdUser?.userName }}</div>
        <span class="c-text-secondary">{{ dateTime(record.createdAt, 'YYYY-MM-DD HH:mm') }}</span>
      </template>
      <template v-if="column.dataIndex === 'dept'">
        <div class="text-center">
          <div>{{ record.dept?.name }}</div>
          <span>{{ record.address }}</span>
        </div>
      </template>
      <template v-if="column.dataIndex === 'auditStatus'">
        <div class="text-center">
          <a-tag class="mr-0!" :color="auditTypeColor(record.auditStatus)">
            {{ opinionTypeText(record.auditStatus) }}
          </a-tag>
        </div>
      </template>
    </template>
    <template #operation="{ record }">
      <div v-if="$auth([_Role.舆情监测中心领导])" class="flex flex-col">
        <a v-if="record.auditStatus === AuditType.需要处置" @click="onAudit(record, AuditType.处置)">确认上报</a>
        <a v-if="record.auditStatus === AuditType.处置" @click="onAudit(record, AuditType.处置)">重新上报</a>
        <a @click="onEdit(record, false)">编辑</a>
        <a v-if="record.auditStatus === AuditType.需要处置" @click="onAudit(record, AuditType.已读归档)"><span class="c-error">取消上报</span></a>
      </div>
    </template>
  </c-pro-table>

  <EditForm v-model:open="open" v-model="currentObj" :read-only="readOnly" @save="proTableRef?.search()" />

  <!-- 提交审核 -->
  <AuditModal v-model:open="auditOpen" v-model:jyt-opinion-id="jytOpinionId" :params="auditParams" title="确认提交到教育厅吗？" @submit="proTableRef?.search" />
</template>

<script lang='ts' setup>
import type { PublicOpinionViewModel } from '@/api/models'
import * as api from '@/api'
import { AuditType, PublicOpinionEditModel } from '@/api/models'
import { useSocialMediaCache } from '@/hooks/useSocialMediaCache'
import { $auth } from '@/permission'
import { _Role } from '@/permission/RoleName'
import { Guid } from '@/utils/GUID'
import AuditModal from '../public-opinion/components/AuditModal.vue'
import EditForm from '../public-opinion/components/EditForm.vue'

definePage({
  meta: {
    title: '上报教育厅审核',
    icon: 'FileDoneOutlined',
    local: true,
    layoutRoute: {
      meta: {
        layout: 'admin',
        title: '上报教育厅审核',
        local: true,
        icon: 'FileDoneOutlined',
        order: 7,
      },
    },
  },
})

const { getByName } = useSocialMediaCache()

const proTableRef = useTemplateRef('proTableRef')

const params = ref({
  audit: AuditType.查询全部,
  keyWord: '',
})

const columns = ref([
  {
    title: '舆情摘要',
    dataIndex: 'summary',
  },
  { title: '发帖人信息', dataIndex: 'publisher', width: 240 },
  { title: '原文内容', dataIndex: 'content' },
  { title: '舆情类别', dataIndex: 'category', width: 120 },
  { title: '涉及地区/单位', dataIndex: 'dept', width: 150 },
  { title: '录入人', dataIndex: 'createdUser', width: 160 },
  { title: '上报状态', dataIndex: 'auditStatus', width: 120, align: 'center' },
])

const auditOpen = ref(false)

const jytOpinionId = ref(Guid.empty)

const auditParams = ref({
  id: Guid.empty,
  audit: AuditType.处置,
})

function onAudit(record: PublicOpinionViewModel, audit: AuditType) {
  jytOpinionId.value = record.jytEntityId
  auditParams.value = { id: record.id, audit }
  auditOpen.value = true

  // Modal.confirm({
  //   title: text,
  //   okText: '确认',
  //   okType: 'primary',
  //   cancelText: '取消',
  //   async onOk() {
  //     await api.OpinionManage.AuditOpinion_PostAsync({}, { id, audit })
  //     proTableRef.value?.search()
  //     message.success('操作成功')
  //   },
  // })
}

const { open, readOnly, currentObj, onEdit } = useEditHook()

function useEditHook() {
  const open = ref(false)

  const readOnly = ref(false)

  const currentObj = ref(new PublicOpinionEditModel())

  function onAdd() {
    currentObj.value = new PublicOpinionEditModel()
    open.value = true
  }

  function onEdit(record: PublicOpinionViewModel, isRead: boolean) {
    currentObj.value = viewModelToEditModel(record, new PublicOpinionEditModel())
    readOnly.value = isRead
    open.value = true
  }

  return { open, readOnly, currentObj, onAdd, onEdit }
}
</script>

<style scoped lang="less">
:deep(.c2-table-striped) td {
  background-color: @colorPrimaryBg;
}

:deep(.ant-table-thead tr th) {
  background: @colorPrimaryBgHover !important;
}
:deep(.header-button-lf) {
  width: 100%;
}
</style>
