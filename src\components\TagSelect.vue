<template>
  <span v-if="getBindValue.readOnly">
    <a-tag v-for="p in modeValue" :key="p">{{ p }}</a-tag>
  </span>

  <c-standard
    v-else
    el="select" s-key="yq_tag" show-search
    :field-names="{ label: 'label', value: 'value' }"
  >
    <template #el="{ options }">
      <a-select
        v-model:value="modeValue"
        style="width: 100%"
        :options="options"
        v-bind="$attrs"
      >
        <template #dropdownRender="{ menuNode: menu }">
          <VNodes :vnodes="menu" />
          <a-divider style="margin: 4px 0" />
          <a-space style="padding: 4px 8px">
            <a-input ref="inputRef" v-model:value="name" placeholder="请输入" />
            <a-button type="text" :disabled="!name" @click.prevent="addItem(options)">
              <template #icon>
                <c-icon-plus-outlined />
              </template>
              添加
            </a-button>
          </a-space>
        </template>
      </a-select>
    </template>
  </c-standard>
</template>

<script lang='ts' setup>
import type { FormConfig } from 'ch2-components/lib/form/types'
import type { StandardItem } from 'ch2-components/lib/standard/types'
import { useInjectFormConfig } from 'ch2-components/lib/form/src/useFormConfig'
import { FieldType } from 'ch2-components/lib/standard/types'
import { ref } from 'vue'

const modeValue = defineModel('value')

const inputRef = useTemplateRef('inputRef')

const { getBindValue } = useInjectFormConfig<FormConfig>({ name: 'select', attrs: useAttrs() })

const VNodes = defineComponent({
  props: {
    vnodes: {
      type: Object,
      required: true,
    },
  },
  render() {
    return this.vnodes
  },
})

const name = ref('')

function addItem(options: StandardItem[]) {
  options.unshift({
    label: name.value,
    value: name.value,
    order: 0,
    isDefault: false,
    children: undefined,
    type: FieldType.文本,
    remark: undefined,
  })
  name.value = ''

  setTimeout(() => {
    inputRef.value?.focus()
  }, 0)
}
</script>

<style scoped>

</style>
