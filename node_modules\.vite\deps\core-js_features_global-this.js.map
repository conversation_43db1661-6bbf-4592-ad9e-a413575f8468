{"version": 3, "sources": ["../../../../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/modules/es.global-this.js", "../../../../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/modules/esnext.global-this.js", "../../../../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/es/global-this.js", "../../../../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/stable/global-this.js", "../../../../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/actual/global-this.js", "../../../../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/full/global-this.js", "../../../../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/features/global-this.js"], "sourcesContent": ["'use strict';\nvar $ = require('../internals/export');\nvar globalThis = require('../internals/global-this');\n\n// `globalThis` object\n// https://tc39.es/ecma262/#sec-globalthis\n$({ global: true, forced: globalThis.globalThis !== globalThis }, {\n  globalThis: globalThis\n});\n", "'use strict';\n// TODO: Remove from `core-js@4`\nrequire('../modules/es.global-this');\n", "'use strict';\nrequire('../modules/es.global-this');\n\nmodule.exports = require('../internals/global-this');\n", "'use strict';\nvar parent = require('../es/global-this');\n\nmodule.exports = parent;\n", "'use strict';\nvar parent = require('../stable/global-this');\n\nmodule.exports = parent;\n", "'use strict';\n// TODO: remove from `core-js@4`\nrequire('../modules/esnext.global-this');\n\nvar parent = require('../actual/global-this');\n\nmodule.exports = parent;\n", "'use strict';\nmodule.exports = require('../full/global-this');\n"], "mappings": ";;;;;;;;;AAAA;AAAA;AAAA;AACA,QAAI,IAAI;AACR,QAAI,aAAa;AAIjB,MAAE,EAAE,QAAQ,MAAM,QAAQ,WAAW,eAAe,WAAW,GAAG;AAAA,MAChE;AAAA,IACF,CAAC;AAAA;AAAA;;;ACRD;AAAA;AAAA;AAEA;AAAA;AAAA;;;ACFA,IAAAA,uBAAA;AAAA;AAAA;AACA;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACHjB,IAAAC,uBAAA;AAAA;AAAA;AACA,QAAI,SAAS;AAEb,WAAO,UAAU;AAAA;AAAA;;;ACHjB,IAAAC,uBAAA;AAAA;AAAA;AACA,QAAI,SAAS;AAEb,WAAO,UAAU;AAAA;AAAA;;;ACHjB,IAAAC,uBAAA;AAAA;AAAA;AAEA;AAEA,QAAI,SAAS;AAEb,WAAO,UAAU;AAAA;AAAA;;;ACNjB,IAAAC,uBAAA;AAAA;AACA,WAAO,UAAU;AAAA;AAAA;", "names": ["require_global_this", "require_global_this", "require_global_this", "require_global_this", "require_global_this"]}