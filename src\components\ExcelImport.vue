<!--
 * @Author: 龙兆柒 <EMAIL>
 * @Date: 2023-03-24 09:44:28
 * @LastEditors: 龙兆柒 <EMAIL>
 * @LastEditTime: 2023-04-25 16:26:38
 * @FilePath: \ch2-template-vue\src\views\components\import\ExcelImport.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--  -->
<template>
  <a-modal v-model:open="visible" :title="title" style="width: 600px" @ok="handleOk">
    <a-form>
      <a-form-item label="模板下载">
        <a-button type="primary" @click="downloadTemp">
          <DownloadOutlined />
          下载模板
        </a-button>
      </a-form-item>

      <a-form-item label="文件上传" style="width: 85%">
        <c-upload-dragger
          :accept="accept"
          :max-size="6"
          :max-count="1"
          :upload-now="true"
          :api="uploadApi"
          @remove="handleRemove"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import type { UploadProps } from 'ant-design-vue'
import { DownloadOutlined } from '@ant-design/icons-vue'
import { ref } from 'vue'

// 是否显示窗口
const visible = ref(false)
// 当前窗口标题
const title = ref()
// 上传组件限制格式
const accept = '.xlsx'
// 模板下载api
const tempApi = ref()
// 模板文件名称
const tempTitle = ref()
// 上传文件接口
const uploadApi = ref()

// const api = (params: any, formData: any) => {
//   uploadApi.value(formData);
// };

const uploadRef = ref()
const fileList = ref<UploadProps['fileList']>()

function handleOk() {
  visible.value = false
  uploadRef.value.startUpload = true
  fileList.value = []
}
// Excel 导入模板下载
function downloadTemp() {
  if (!tempApi.value)
    return
  fetch(tempApi.value)
    .then(response => response.blob())
    .then((blob) => {
      // 创建一个 <a> 元素
      const url = window.URL.createObjectURL(new Blob([blob]))
      const a = document.createElement('a')
      a.href = url
      a.download = tempTitle.value
      // 模拟点击下载链接
      a.click()
      // 释放 URL 对象
      window.URL.revokeObjectURL(url)
    })
}
function handleRemove(e: any) {
  console.log(e)
}
defineExpose({
  visible,
  title,
  tempApi,
  tempTitle,
  uploadApi,
})
</script>

<style scoped></style>
