import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'

dayjs.extend(isBetween)
dayjs.extend(isSameOrBefore)
dayjs.extend(isSameOrAfter)

export function getContractStatus(
  start: string | Dayjs,
  end: string | Dayjs,
  now: dayjs.Dayjs = dayjs(),
): '未开始' | '正常' | '即将过期' | '已过期' {
  const startDate = dayjs(start)
  const endDate = dayjs(end)

  if (now.isAfter(endDate)) {
    return '已过期'
  }

  if (endDate.diff(now, 'month', true) <= 1) {
    return '即将过期'
  }

  if (now.isSameOrAfter(startDate) && now.isBefore(endDate)) {
    return '正常'
  }

  return '未开始'
}
