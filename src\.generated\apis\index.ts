import axios, { AxiosRequestConfig, AxiosResponse } from "axios";
import {
  AIPublicOpinionAnalysis,
  ApiResult,
  AuditType,
  BaseInfo,
  BaseUserRequestLog,
  BasicInformationTypeDescription,
  CarbonCopy,
  CountTip,
  CurrentUserPasswordChangeEditModel,
  DeletedFileInfo,
  Department,
  DepartmentEditModel,
  DepartmentViewModel,
  DeptMainPush,
  DeptMainPushView,
  DeptPushChannel,
  DeptPushChannelLog,
  DeptPushLog,
  DeptPushStatistics,
  DeptPushType,
  EfCoreResourcePermission,
  EmbedFileInfo,
  EventCategoryStatisticsViewModel,
  EventHandlingRatioViewModel,
  EventReport,
  EventReportEditModel,
  EventReportViewModel,
  EventSpreadSituation,
  EventSpreadSituationEditModel,
  EventSpreadSituationViewModel,
  EventTeacherStudent,
  FileAttribution,
  FileType,
  GuidIdNameViewModel,
  HandledType,
  HotPublicOpinion,
  HotPublicOpinionAlert,
  IActionResult,
  ILimitedResource,
  IPagedEnumerable,
  IPermissionStoreCapacities,
  IResourceMetadata,
  IResourcePermission,
  IVersioned,
  IdentityRole,
  IdentityUser,
  IdentityUserLoginLog,
  IdentityUserRole,
  IncidentHandlingTrendsViewModel,
  InvalidModelApiResult,
  LimitedPermissionNode,
  LimitedResourceNode,
  LoginResultLog,
  PackedApiResult,
  PermissionType,
  Positive,
  PublicEvent,
  PublicEventCreateModel,
  PublicEventViewModel,
  PublicOpinion,
  PublicOpinionEditModel,
  PublicOpinionSubmission,
  PublicOpinionSubmissionAuditType,
  PublicOpinionSubmissionEditModel,
  PublicOpinionSubmissionListItem,
  PublicOpinionSubmissionStatistics,
  PublicOpinionSubmissionViewModel,
  PublicOpinionTopic,
  PublicOpinionTopicEditModel,
  PublicOpinionTopicViewModel,
  PublicOpinionViewModel,
  PushLog,
  PushLogStatisticsByDept,
  PushStatus,
  PushTarget,
  PushType,
  ReadingStatus,
  RegisteringValidationModel,
  RequestType,
  ResourceGrant,
  ResourceMetadata,
  ResourcePermission,
  ResourceType,
  ResponseType,
  RiskLevel,
  Role,
  StandardItem,
  StatisticalTimeType,
  StatisticsViewModel,
  SubmissionQueryRequest,
  SystemInfo,
  TagManage,
  TagManageView,
  TagType,
  TeacherStudentType,
  ToDayBriefing,
  UploadFileInfo,
  UploadFileInfoResult,
  User,
  UserBaseViewModel,
  UserCreateModel,
  UserEditModel,
  UserExpirationEditModel,
  UserLoginLog,
  UserPasswordChangeEditModel,
  UserRegisterEditModel,
  UserRequestLog,
  UserRole,
  UserRoleViewModel,
  UserViewModel,
  VerificationStatus,
  WebSource,
} from "../models";

export class apiOptions {
  static async request<TData, TResult>(
    options: AxiosRequestConfig<TData>
  ): Promise<TResult> {
    return axios.request<TData, AxiosResponse<TResult>>(options) as TResult;
  }
}

export async function requestPackedApi<TData, TResult>(
  options: AxiosRequestConfig<TData>
) {
  const data = await apiOptions.request<TData, PackedApiResult<TResult>>(
    options
  );
  if (!data.success) throw new Error(data.data as string);
  return data.data as TResult;
}

export class Ai {
  /**
   * GetContent_PostAsync /api/Ai/GetContent
   *
   */
  static async GetContent_PostAsync(
    data: { context: string },
    options?: AxiosRequestConfig
  ): Promise<AIPublicOpinionAnalysis> {
    const formData = new FormData();
    formData.append("context", data.context as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/Ai/GetContent`,
      data: formData,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class BaseInfoManage {
  /**
              * GetSystemBaseTypeAsync /api/BaseInfoManage/GetSystemBaseType
              * 系统基础类型
默认不允许
              */
  static async GetSystemBaseTypeAsync(
    options?: AxiosRequestConfig
  ): Promise<BasicInformationTypeDescription[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/GetSystemBaseType`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetTypeDescriptionAsync /api/BaseInfoManage/GetTypeDescription
   * 获取参数允许的类型
   */
  static async GetTypeDescriptionAsync(
    options?: AxiosRequestConfig
  ): Promise<BasicInformationTypeDescription[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/GetTypeDescription`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * VerifyData_GetAsync /api/BaseInfoManage/VerifyData
   * 验证数据是否正确
   */
  static async VerifyData_GetAsync(
    params: {
      /**数据类型*/ type?: string;
      /**数据*/
      data?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/VerifyData`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * TestData_GetAsync /api/BaseInfoManage/TestData
   * 验证数据是否正确
   */
  static async TestData_GetAsync(
    params: {
      /**数据类型*/ type?: string;
      /**数据*/
      data?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<string> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/TestData`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * VerifyType_GetAsync /api/BaseInfoManage/VerifyType
   * 验证类型是否正确
   */
  static async VerifyType_GetAsync(
    params: {
      /**数据类型*/ type?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/VerifyType`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetPagedAsync /api/BaseInfoManage/GetPaged
   * 获取所有基础信息(隐藏除外)
   */
  static async GetPagedAsync(
    params: { limit?: number; offset?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<BaseInfo>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/GetPaged`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetInfoAsync /api/BaseInfoManage/GetInfo
   * 基础信息内容
   */
  static async GetInfoAsync(
    params: { key?: string },
    options?: AxiosRequestConfig
  ): Promise<BaseInfo> {
    return requestPackedApi({
      method: "GET",
      url: `/api/BaseInfoManage/GetInfo`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AddTypeDescription_PostAsync /api/BaseInfoManage/AddTypeDescription
   * 添加类型
   */
  static async AddTypeDescription_PostAsync(
    data: BasicInformationTypeDescription,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/BaseInfoManage/AddTypeDescription`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Save_PostAsync /api/BaseInfoManage/Save
   * 基础信息内容
   */
  static async Save_PostAsync(
    data: BaseInfo,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/BaseInfoManage/Save`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Crypto {
  /**
   * GetShortTokenAsync /api/Crypto/GetShortToken
   *
   */
  static async GetShortTokenAsync(
    options?: AxiosRequestConfig
  ): Promise<string> {
    return apiOptions.request({
      method: "GET",
      url: `/api/Crypto/GetShortToken`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetRsaPublicKeyAsync /api/Crypto/GetRsaPublicKey
   *
   */
  static async GetRsaPublicKeyAsync(
    options?: AxiosRequestConfig
  ): Promise<string> {
    return apiOptions.request({
      method: "GET",
      url: `/api/Crypto/GetRsaPublicKey`,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class CurrentUser {
  /**
   * Me_GetAsync /api/CurrentUser/Me
   * 用户信息
   */
  static async Me_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/CurrentUser/Me`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CurrentUserLoginLogList_GetAsync /api/CurrentUser/CurrentUserLoginLogList
   * 获取当前用户的登录日志
   */
  static async CurrentUserLoginLogList_GetAsync(
    params: { offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserLoginLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/CurrentUser/CurrentUserLoginLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserModel_PostAsync /api/CurrentUser/EditUserModel
   * 修改用户信息
   */
  static async EditUserModel_PostAsync(
    data: UserEditModel,
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/CurrentUser/EditUserModel`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserPassword_PostAsync /api/CurrentUser/EditUserPassword
   * 修改用户密码
   */
  static async EditUserPassword_PostAsync(
    data: CurrentUserPasswordChangeEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/CurrentUser/EditUserPassword`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class DepartmentManage {
  /**
   * GetDepartmentByIdAsync /api/DepartmentManage/GetDepartmentById
   * 根据部门 Id 获取部门（包含其联系人及直接子部门）
   */
  static async GetDepartmentByIdAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<DepartmentViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DepartmentManage/GetDepartmentById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetDepartmentSimpleByIdAsync /api/DepartmentManage/GetDepartmentSimpleById
   * 根据部门 Id 获取部门
   */
  static async GetDepartmentSimpleByIdAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<DepartmentViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DepartmentManage/GetDepartmentSimpleById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetAllDepartmentsAsync /api/DepartmentManage/GetAllDepartments
   * 获取所有部门（可以根据需要扩展递归加载子孙部门）
   */
  static async GetAllDepartmentsAsync(
    params: { isCooperate?: boolean },
    options?: AxiosRequestConfig
  ): Promise<DepartmentViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DepartmentManage/GetAllDepartments`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CreateDepartment_PostAsync /api/DepartmentManage/CreateDepartment
   * 创建一个部门（可包含联系人信息及设置父部门）
   */
  static async CreateDepartment_PostAsync(
    data: DepartmentEditModel,
    options?: AxiosRequestConfig
  ): Promise<Department> {
    return requestPackedApi({
      method: "POST",
      url: `/api/DepartmentManage/CreateDepartment`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UpdateDepartment_PostAsync /api/DepartmentManage/UpdateDepartment
   * 更新部门信息（例如修改名称、联系人、父部门等）
   */
  static async UpdateDepartment_PostAsync(
    data: DepartmentEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/DepartmentManage/UpdateDepartment`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * DeleteDepartment_PostAsync /api/DepartmentManage/DeleteDepartment
   * 删除部门（递归删除其所有子部门）
   */
  static async DeleteDepartment_PostAsync(
    params: { departmentId: GUID },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/DepartmentManage/DeleteDepartment`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class DeptPushLogs {
  /**
   * GetDeptAsync /api/DeptPushLogs/GetDept
   * 查询合作单位推送统计列表  ，
   */
  static async GetDeptAsync(
    params: {
      deptName?: string;
      entityType?: DeptPushType;
      limit?: number;
      offset?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<DeptMainPushView>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DeptPushLogs/GetDept`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetPushStatisticsAsync /api/DeptPushLogs/GetPushStatistics
   * 获取合作单位推送统计信息
   */
  static async GetPushStatisticsAsync(
    options?: AxiosRequestConfig
  ): Promise<DeptPushStatistics> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DeptPushLogs/GetPushStatistics`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetPushUserAsync /api/DeptPushLogs/GetPushUser
   * 查询单位中的用户
   */
  static async GetPushUserAsync(
    params: { departmentId: GUID },
    options?: AxiosRequestConfig
  ): Promise<UserViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DeptPushLogs/GetPushUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUserPushStatusAsync /api/DeptPushLogs/GetUserPushStatus
   * 单位推送提醒 查询指定数据的推送情况（单位该条信息（推送类型）的推送情况）
   */
  static async GetUserPushStatusAsync(
    params: { departmentId?: GUID; entityId: GUID; entityType?: DeptPushType },
    options?: AxiosRequestConfig
  ): Promise<DeptMainPush[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DeptPushLogs/GetUserPushStatus`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetPushLogAsync /api/DeptPushLogs/GetPushLog
   * 单位推送记录
   */
  static async GetPushLogAsync(
    params: {
      departmentId: GUID;
      startTime?: Date;
      endTime?: Date;
      /**合同期内*/
      isContract: boolean;
      limit?: number;
      offset?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<DeptMainPush>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DeptPushLogs/GetPushLog`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetInternalPushNotificationsAsync /api/DeptPushLogs/GetInternalPushNotifications
   * 合作单位 站内推送通知（微信、邮件、短信关联通知
   */
  static async GetInternalPushNotificationsAsync(
    params: {
      deptPushType?: DeptPushType;
      status?: PushStatus;
      readingStatus?: ReadingStatus;
      limit?: number;
      offset?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<DeptPushLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/DeptPushLogs/GetInternalPushNotifications`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SavePushUser_PostAsync /api/DeptPushLogs/SavePushUser
   * 修改用户的推送渠道
   */
  static async SavePushUser_PostAsync(
    params: { userId: GUID },
    data: DeptPushChannel[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/DeptPushLogs/SavePushUser`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetPushLogStatisticsByDept_PostAsync /api/DeptPushLogs/GetPushLogStatisticsByDept
   * 获取指定单位的统计信息
   */
  static async GetPushLogStatisticsByDept_PostAsync(
    params: { departmentId: GUID },
    options?: AxiosRequestConfig
  ): Promise<PushLogStatisticsByDept> {
    return requestPackedApi({
      method: "POST",
      url: `/api/DeptPushLogs/GetPushLogStatisticsByDept`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetInternalPushNotificationReadingStatus_PostAsync /api/DeptPushLogs/SetInternalPushNotificationReadingStatus
   * 设置站内推送通知已读状态
   */
  static async SetInternalPushNotificationReadingStatus_PostAsync(
    params: { deptPushLogId: GUID },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/DeptPushLogs/SetInternalPushNotificationReadingStatus`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class EventManage {
  /**
   * GetListAsync /api/EventManage/GetList
   * 查询事件
   */
  static async GetListAsync(
    params: {
      title?: string;
      name?: string;
      /**是否上报教育厅*/
      isReportJyt?: boolean;
      /**分页限制*/
      limit?: number;
      /**分页开始*/
      offset?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<PublicEventViewModel>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/EventManage/GetList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetAsync /api/EventManage/Get
   * 获取详情
   */
  static async GetAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<PublicEventViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/EventManage/Get`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetHistoryAsync /api/EventManage/GetHistory
   * 查询上报教育厅 上次上报事件对比 （只返回了不一致的字段）
   */
  static async GetHistoryAsync(
    params: { opId: GUID },
    options?: AxiosRequestConfig
  ): Promise<Record<string, string>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/EventManage/GetHistory`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Create_PostAsync /api/EventManage/Create
   * 添加事件
   */
  static async Create_PostAsync(
    data: PublicEventCreateModel,
    options?: AxiosRequestConfig
  ): Promise<PublicEventViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/Create`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Update_PostAsync /api/EventManage/Update
   * Update 操作（记录历史数据并更新）
   */
  static async Update_PostAsync(
    data: PublicEventCreateModel,
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/Update`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Delete_PostAsync /api/EventManage/Delete
   * Delete 操作
   */
  static async Delete_PostAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/Delete`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AuditOpinion_PostAsync /api/EventManage/AuditOpinion
   * 事件上报教育
   */
  static async AuditOpinion_PostAsync(
    params: { jytEntityId?: GUID },
    data: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<string> {
    const formData = new FormData();
    formData.append("id", data.id as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/AuditOpinion`,
      data: formData,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ReportEventReport_PostAsync /api/EventManage/ReportEventReport
   * 上报续报
   */
  static async ReportEventReport_PostAsync(
    params: { jytEntityId?: GUID },
    data: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<string> {
    const formData = new FormData();
    formData.append("id", data.id as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/ReportEventReport`,
      data: formData,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ReportSpreadSituation_PostAsync /api/EventManage/ReportSpreadSituation
   * 传播态势
   */
  static async ReportSpreadSituation_PostAsync(
    params: { jytEntityId?: GUID },
    data: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<string> {
    const formData = new FormData();
    formData.append("id", data.id as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/ReportSpreadSituation`,
      data: formData,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Report_PostAsync /api/EventManage/Report
   * 事件续报
   */
  static async Report_PostAsync(
    data: EventReportEditModel,
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/Report`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveReport_PostAsync /api/EventManage/RemoveReport
   * 移除事件续报
   */
  static async RemoveReport_PostAsync(
    data: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    const formData = new FormData();
    formData.append("id", data.id as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/RemoveReport`,
      data: formData,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EventSpreadSituation_PostAsync /api/EventManage/EventSpreadSituation
   * 传播态势
   */
  static async EventSpreadSituation_PostAsync(
    data: EventSpreadSituationEditModel,
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/EventSpreadSituation`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveEventSpreadSituation_PostAsync /api/EventManage/RemoveEventSpreadSituation
   * 移除传播态势
   */
  static async RemoveEventSpreadSituation_PostAsync(
    data: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    const formData = new FormData();
    formData.append("id", data.id as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/RemoveEventSpreadSituation`,
      data: formData,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AddCarbonCopy_PostAsync /api/EventManage/AddCarbonCopy
   * 添加关联单位
   */
  static async AddCarbonCopy_PostAsync(
    params: { carbonCopyText?: string; eventId: GUID },
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/AddCarbonCopy`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveCarbonCopy_PostAsync /api/EventManage/RemoveCarbonCopy
   * 删除关联单位
   */
  static async RemoveCarbonCopy_PostAsync(
    data: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    const formData = new FormData();
    formData.append("id", data.id as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/EventManage/RemoveCarbonCopy`,
      data: formData,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class FileManage {
  /**
   * GetFileUrlAsync /api/FileManage/GetFileUrl
   * 文件直链获取
   */
  static async GetFileUrlAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<string> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FileManage/GetFileUrl`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetFilesAsync /api/FileManage/GetFiles
   * 获取文件列表
   */
  static async GetFilesAsync(
    params: {
      type?: FileType;
      attribution?: FileAttribution;
      fileName?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UploadFileInfo>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FileManage/GetFiles`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetRemoveFilesAsync /api/FileManage/GetRemoveFiles
   * 获取回收站文件列表
   */
  static async GetRemoveFilesAsync(
    params: {
      type?: FileType;
      attribution?: FileAttribution;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<DeletedFileInfo>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/FileManage/GetRemoveFiles`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UploadPortalFile_PostAsync /api/FileManage/UploadPortalFile
   * 文件上传-门户站点文章用
   */
  static async UploadPortalFile_PostAsync(
    data: { files: string[] },
    options?: AxiosRequestConfig
  ): Promise<UploadFileInfoResult[]> {
    const formData = new FormData();
    formData.append("files", data.files as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/FileManage/UploadPortalFile`,
      data: formData,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UploadCertifiedFile_PostAsync /api/FileManage/UploadCertifiedFile
   * 文件上传
   */
  static async UploadCertifiedFile_PostAsync(
    data: { files: string[] },
    options?: AxiosRequestConfig
  ): Promise<UploadFileInfoResult[]> {
    const formData = new FormData();
    formData.append("files", data.files as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/FileManage/UploadCertifiedFile`,
      data: formData,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveFile_PostAsync /api/FileManage/RemoveFile
   * 删除文件
   */
  static async RemoveFile_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/FileManage/RemoveFile`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveCompletelyFile_PostAsync /api/FileManage/RemoveCompletelyFile
   * 彻底删除文件
   */
  static async RemoveCompletelyFile_PostAsync(
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/FileManage/RemoveCompletelyFile`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class HotPublicOpinionAlerts {
  /**
   * HotPublicOpinionAlerts_GetAsync /api/HotPublicOpinionAlerts/HotPublicOpinionAlerts
   * 获取预警信息列表
   */
  static async HotPublicOpinionAlerts_GetAsync(
    params: { keyWord?: string; startTime?: Date; endTime?: Date },
    options?: AxiosRequestConfig
  ): Promise<HotPublicOpinionAlert[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/HotPublicOpinionAlerts/HotPublicOpinionAlerts`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * HotPublicOpinions_GetAsync /api/HotPublicOpinionAlerts/HotPublicOpinions
   * 获取热点舆情列表
   */
  static async HotPublicOpinions_GetAsync(
    params: {
      hotPublicOpinionAlertId: GUID;
      keyWord?: string;
      isPositive?: boolean;
      startTime?: Date;
      endTime?: Date;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<HotPublicOpinion>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/HotPublicOpinionAlerts/HotPublicOpinions`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Save_PostAsync /api/HotPublicOpinionAlerts/Save
   * 保存预警信息
   */
  static async Save_PostAsync(
    data: HotPublicOpinionAlert,
    options?: AxiosRequestConfig
  ): Promise<HotPublicOpinionAlert> {
    return requestPackedApi({
      method: "POST",
      url: `/api/HotPublicOpinionAlerts/Save`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Delete_PostAsync /api/HotPublicOpinionAlerts/Delete
   * 删除预警信息
   */
  static async Delete_PostAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/HotPublicOpinionAlerts/Delete`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * DeleteHotPublicOpinion_PostAsync /api/HotPublicOpinionAlerts/DeleteHotPublicOpinion
   * 删除热点舆情
   */
  static async DeleteHotPublicOpinion_PostAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/HotPublicOpinionAlerts/DeleteHotPublicOpinion`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SaveHotPublicOpinion_PostAsync /api/HotPublicOpinionAlerts/SaveHotPublicOpinion
   * 保存热点舆情
   */
  static async SaveHotPublicOpinion_PostAsync(
    data: HotPublicOpinion,
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/HotPublicOpinionAlerts/SaveHotPublicOpinion`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Limits {
  /**
   * GetStoreCapacitiesAsync /api/Limits/GetStoreCapacities
   *
   */
  static async GetStoreCapacitiesAsync(
    options?: AxiosRequestConfig
  ): Promise<IPermissionStoreCapacities> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/GetStoreCapacities`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * List_GetAsync /api/Limits/List
   *
   */
  static async List_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<ILimitedResource[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/List`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Tree_GetAsync /api/Limits/Tree
   *
   */
  static async Tree_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<LimitedResourceNode[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/Tree`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetByRolenameAsync /api/Limits/GetByRolename
   *
   */
  static async GetByRolenameAsync(
    params: { roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<IResourcePermission> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/GetByRolename`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetByUserIdAsync /api/Limits/GetByUserId
   *
   */
  static async GetByUserIdAsync(
    params: { userId?: string },
    options?: AxiosRequestConfig
  ): Promise<IResourcePermission> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/GetByUserId`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Permissions_GetAsync /api/Limits/Permissions
   *
   */
  static async Permissions_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<IVersioned<LimitedPermissionNode[]>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/Permissions`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetMetadataAsync /api/Limits/GetMetadata
   *
   */
  static async GetMetadataAsync(
    params: { id?: string },
    options?: AxiosRequestConfig
  ): Promise<IResourceMetadata> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Limits/GetMetadata`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetByRolename_PostAsync /api/Limits/SetByRolename
   *
   */
  static async SetByRolename_PostAsync(
    params: { roleName?: string },
    data: ResourcePermission,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Limits/SetByRolename`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetByUserId_PostAsync /api/Limits/SetByUserId
   *
   */
  static async SetByUserId_PostAsync(
    params: { userId?: string },
    data: ResourcePermission,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Limits/SetByUserId`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetMetadata_PostAsync /api/Limits/SetMetadata
   *
   */
  static async SetMetadata_PostAsync(
    params: { id?: string },
    data: ResourceMetadata,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/Limits/SetMetadata`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class OpinionManage {
  /**
   * GetAsync /api/OpinionManage/Get
   * Get by Id
   */
  static async GetAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<PublicOpinionViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/OpinionManage/Get`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetListAsync /api/OpinionManage/GetList
   * 查询舆情
   */
  static async GetListAsync(
    params: {
      /**内容*/ content?: string;
      /**摘要*/
      summary?: string;
      /**标签，多个查询方式 使用“;”分割*/
      tag?: string;
      /**一级分类*/
      mainCategory?: string;
      /**分类*/
      category?: string;
      /**上报教育厅*/
      isJyt?: boolean;
      /**，每日简报*/
      toDayBriefing?: ToDayBriefing;
      /**正面报道*/
      positive?: Positive;
      /**是否隐患舆情*/
      isHiddenDanger?: boolean;
      /**发帖人*/
      publisher?: string;
      /**地址*/
      address?: string;
      /**来源*/
      source?: string;
      /**专题ID*/
      topicId?: GUID;
      /**重大舆情*/
      serious?: boolean;
      /**发生开始时间*/
      publisherStart?: Date;
      /**发生结束时间*/
      publisherEnd?: Date;
      /**创建人id*/
      createdBy?: string;
      /**创建开始时间*/
      createdStart?: Date;
      /**创建结束时间*/
      createdEnd?: Date;
      /**风险等级*/
      riskLevel?: RiskLevel;
      /**涉及部门*/
      department?: string;
      /**单位类型 标准dept-type*/
      departmentType?: string;
      /**所属区县*/
      counties?: string;
      /**事发校内外*/
      address1?: string;
      /**事故类型*/
      accidentType?: string;
      /**事故原因*/
      accidentReason?: string;
      /**隐患类别*/
      trafficCategory?: string;
      /**涉及人数*/
      involved?: number;
      /**死亡人数*/
      death?: number;
      /**受伤人数*/
      injured?: number;
      /**审核状态*/
      audit?: AuditType;
      handledStatus?: HandledType;
      /**分页限制*/
      limit?: number;
      /**分页开始*/
      offset?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<PublicOpinionViewModel>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/OpinionManage/GetList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetJytListAsync /api/OpinionManage/GetJytList
   * 查询上报教育厅
   */
  static async GetJytListAsync(
    params: {
      keyWord?: string;
      /**审核状态*/
      audit?: AuditType;
      /**分页限制*/
      limit?: number;
      /**分页开始*/
      offset?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<PublicOpinionViewModel>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/OpinionManage/GetJytList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetHistoryAsync /api/OpinionManage/GetHistory
   * 查询上报教育厅 上次上报舆情对比 （只返回了不一致的字段）
   */
  static async GetHistoryAsync(
    params: { opId: GUID },
    options?: AxiosRequestConfig
  ): Promise<Record<string, string>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/OpinionManage/GetHistory`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ExportSimpleDailyReport_GetAsync /api/OpinionManage/ExportSimpleDailyReport
   * 简报导出（默认昨天中午12:00至今日12:00）
   */
  static async ExportSimpleDailyReport_GetAsync(
    params: { startTime?: Date; endTime?: Date },
    options?: AxiosRequestConfig
  ): Promise<Blob> {
    return request({
      method: "GET",
      url: `/api/OpinionManage/ExportSimpleDailyReport`,
      params,
      responseType: "blob",
      ...(options || {}),
    });
  }
  /**
   * ExportSimpleDailyReportByDayBriefing_GetAsync /api/OpinionManage/ExportSimpleDailyReportByDayBriefing
   * 涉及教师、高校 舆情信息导出（默认昨天中午12:00至今日12:00）
   */
  static async ExportSimpleDailyReportByDayBriefing_GetAsync(
    params: { toDayBriefing?: ToDayBriefing; startTime?: Date; endTime?: Date },
    options?: AxiosRequestConfig
  ): Promise<Blob> {
    return request({
      method: "GET",
      url: `/api/OpinionManage/ExportSimpleDailyReportByDayBriefing`,
      params,
      responseType: "blob",
      ...(options || {}),
    });
  }
  /**
   * CopySimpleDailyReport_GetAsync /api/OpinionManage/CopySimpleDailyReport
   * 复制简报
   */
  static async CopySimpleDailyReport_GetAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<string> {
    return requestPackedApi({
      method: "GET",
      url: `/api/OpinionManage/CopySimpleDailyReport`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Create_PostAsync /api/OpinionManage/Create
   * 添加舆情
   */
  static async Create_PostAsync(
    data: PublicOpinionEditModel,
    options?: AxiosRequestConfig
  ): Promise<PublicOpinionViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/OpinionManage/Create`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Update_PostAsync /api/OpinionManage/Update
   * Update 操作（记录历史数据并更新）
   */
  static async Update_PostAsync(
    data: PublicOpinionEditModel,
    options?: AxiosRequestConfig
  ): Promise<PublicOpinionViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/OpinionManage/Update`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Delete_PostAsync /api/OpinionManage/Delete
   * Delete 操作
   */
  static async Delete_PostAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/OpinionManage/Delete`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AuditOpinion_PostAsync /api/OpinionManage/AuditOpinion
   * 领导审核舆情上报教育厅,取消上报为 未读状态
   */
  static async AuditOpinion_PostAsync(
    params: {
      /**关联教育厅舆情id，不传则会在教育厅中创建一个新的舆情，传则更新教育厅的该舆情*/
      jytOpinionId?: GUID;
    },
    data: { id: GUID; audit: AuditType },
    options?: AxiosRequestConfig
  ): Promise<string> {
    const formData = new FormData();
    formData.append("id", data.id as any);
    formData.append("audit", data.audit as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/OpinionManage/AuditOpinion`,
      data: formData,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CooperateDeptAudit_PostAsync /api/OpinionManage/CooperateDeptAudit
   * 合作单位处置/已读 舆情
   */
  static async CooperateDeptAudit_PostAsync(
    data: {
      id: GUID;
      audit: HandledType;
      handledText: string;
      handledAttachmentIds: string;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    const formData = new FormData();
    formData.append("id", data.id as any);
    formData.append("audit", data.audit as any);
    formData.append("handledText", data.handledText as any);
    formData.append("handledAttachmentIds", data.handledAttachmentIds as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/OpinionManage/CooperateDeptAudit`,
      data: formData,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AddOpinion2Event_PostAsync /api/OpinionManage/AddOpinion2Event
   * 将舆情添加到事件
   */
  static async AddOpinion2Event_PostAsync(
    data: { opinionId: GUID; eventId: GUID; forcibly: boolean },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    const formData = new FormData();
    formData.append("opinionId", data.opinionId as any);
    formData.append("eventId", data.eventId as any);
    formData.append("forcibly", data.forcibly as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/OpinionManage/AddOpinion2Event`,
      data: formData,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SendPublicOpinionsToCooperateDept_PostAsync /api/OpinionManage/SendPublicOpinionsToCooperateDept
   * 发送舆情提醒推送合作单位
   */
  static async SendPublicOpinionsToCooperateDept_PostAsync(
    params: {
      /**推送摘要*/ abstractText?: string;
      /**舆情id*/
      publicOpinionId: GUID;
      /**是否追加发送*/
      force?: boolean;
    },
    data: PushTarget[],
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/OpinionManage/SendPublicOpinionsToCooperateDept`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SendDailyNewspaperToCooperateDept_PostAsync /api/OpinionManage/SendDailyNewspaperToCooperateDept
   * 发送日报给合作单位
   */
  static async SendDailyNewspaperToCooperateDept_PostAsync(
    params: { abstractText?: string },
    data: PushTarget[],
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/OpinionManage/SendDailyNewspaperToCooperateDept`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ExportDailyNewspaperWordByDept_PostAsync /api/OpinionManage/ExportDailyNewspaperWordByDept
   * 导出合作单位日报,默认所有有效期内的单位（默认昨天中午12:00至今日12:00）
   */
  static async ExportDailyNewspaperWordByDept_PostAsync(
    params: { startTime?: Date; endTime?: Date },
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<Blob> {
    return request({
      method: "POST",
      url: `/api/OpinionManage/ExportDailyNewspaperWordByDept`,
      data,
      params,
      responseType: "blob",
      ...(options || {}),
    });
  }
  /**
   * ExportDailyNewspaperWord_PostAsync /api/OpinionManage/ExportDailyNewspaperWord
   * 导出汇总日报（默认昨天中午12:00至今日12:00）
   */
  static async ExportDailyNewspaperWord_PostAsync(
    params: { startTime?: Date; endTime?: Date },
    options?: AxiosRequestConfig
  ): Promise<Blob> {
    return request({
      method: "POST",
      url: `/api/OpinionManage/ExportDailyNewspaperWord`,
      params,
      responseType: "blob",
      ...(options || {}),
    });
  }
}
export class OpinionTopicManage {
  /**
   * GetAsync /api/OpinionTopicManage/Get
   * Get by Id
   */
  static async GetAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<PublicOpinionTopicViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/OpinionTopicManage/Get`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveOpinion2Topic_GetAsync /api/OpinionTopicManage/RemoveOpinion2Topic
   * 将舆情从专题中移除
   */
  static async RemoveOpinion2Topic_GetAsync(
    params: { opinionId: GUID },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "GET",
      url: `/api/OpinionTopicManage/RemoveOpinion2Topic`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetListAsync /api/OpinionTopicManage/GetList
   * 查询
   */
  static async GetListAsync(
    params: {
      /**标题*/ title?: string;
      /**简介*/
      summary?: string;
      /**是否有效*/
      isEffective?: boolean;
      /**分页限制*/
      limit?: number;
      /**分页开始*/
      offset?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<PublicOpinionTopic>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/OpinionTopicManage/GetList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Create_PostAsync /api/OpinionTopicManage/Create
   * 添加舆情
   */
  static async Create_PostAsync(
    data: PublicOpinionTopicEditModel,
    options?: AxiosRequestConfig
  ): Promise<PublicOpinionTopicEditModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/OpinionTopicManage/Create`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Update_PostAsync /api/OpinionTopicManage/Update
   * Update 操作（记录历史数据并更新）
   */
  static async Update_PostAsync(
    data: PublicOpinionTopicEditModel,
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/OpinionTopicManage/Update`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Delete_PostAsync /api/OpinionTopicManage/Delete
   * Delete 操作
   */
  static async Delete_PostAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/OpinionTopicManage/Delete`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AddOpinion2Topic_PostAsync /api/OpinionTopicManage/AddOpinion2Topic
   * 将舆情添加到事件
   */
  static async AddOpinion2Topic_PostAsync(
    data: { opinionId: GUID; topicId: GUID; forcibly: boolean },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    const formData = new FormData();
    formData.append("opinionId", data.opinionId as any);
    formData.append("topicId", data.topicId as any);
    formData.append("forcibly", data.forcibly as any);
    return requestPackedApi({
      method: "POST",
      url: `/api/OpinionTopicManage/AddOpinion2Topic`,
      data: formData,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Public {
  /**
   * GetEventTypeAsync /api/Public/GetEventType
   * 获取事件分类数据
   */
  static async GetEventTypeAsync(
    options?: AxiosRequestConfig
  ): Promise<StandardItem[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Public/GetEventType`,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class PublicOpinionSubmissions {
  /**
   * GetByIdAsync /api/PublicOpinionSubmissions/GetById
   * 获取舆情提交详情
   */
  static async GetByIdAsync(
    params: { id: GUID },
    options?: AxiosRequestConfig
  ): Promise<PublicOpinionSubmissionViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/PublicOpinionSubmissions/GetById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SubmitPublicOpinion_PostAsync /api/PublicOpinionSubmissions/SubmitPublicOpinion
   * 推送舆情数据
   */
  static async SubmitPublicOpinion_PostAsync(
    data: PublicOpinionSubmissionEditModel[],
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PublicOpinionSubmissions/SubmitPublicOpinion`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetSubmissionList_PostAsync /api/PublicOpinionSubmissions/GetSubmissionList
   * 获取舆情提交列表（分页、可筛选）
   */
  static async GetSubmissionList_PostAsync(
    params: { limit?: number; offset?: number },
    data: SubmissionQueryRequest,
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<PublicOpinionSubmissionListItem>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PublicOpinionSubmissions/GetSubmissionList`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AuditSubmission_PostAsync /api/PublicOpinionSubmissions/AuditSubmission
   * 审核接口
   */
  static async AuditSubmission_PostAsync(
    params: { id: GUID; auditStatus?: PublicOpinionSubmissionAuditType },
    data: PublicOpinionEditModel,
    options?: AxiosRequestConfig
  ): Promise<PublicOpinionViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PublicOpinionSubmissions/AuditSubmission`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetStatistics_PostAsync /api/PublicOpinionSubmissions/GetStatistics
   * 获取左边的统计信息
   */
  static async GetStatistics_PostAsync(
    params: { auditStatus?: PublicOpinionSubmissionAuditType },
    options?: AxiosRequestConfig
  ): Promise<PublicOpinionSubmissionStatistics> {
    return requestPackedApi({
      method: "POST",
      url: `/api/PublicOpinionSubmissions/GetStatistics`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class PushLogs {
  /**
   * GetPushLogListAsync /api/PushLogs/GetPushLogList
   * 查询推送记录
   */
  static async GetPushLogListAsync(
    params: {
      /**推送类型（事件、舆情、专题）*/ entityType?: PushType;
      /**推送状态（成功、失败）*/
      status?: PushStatus;
      /**分页大小（最大100）*/
      limit?: number;
      /**分页起始位置*/
      offset?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<PushLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/PushLogs/GetPushLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * DeletePushLog_PostAsync /api/PushLogs/DeletePushLog
   * 删除推送记录
   */
  static async DeletePushLog_PostAsync(
    params: {
      /**推送记录ID*/ id: GUID;
    },
    options?: AxiosRequestConfig
  ): Promise<Blob> {
    return request({
      method: "POST",
      url: `/api/PushLogs/DeletePushLog`,
      params,
      responseType: "blob",
      ...(options || {}),
    });
  }
}
export class RolesManage {
  /**
   * RoleList_PostAsync /api/RolesManage/RoleList
   * 角色列表
   */
  static async RoleList_PostAsync(
    params: { roleName?: string; offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<Role>> {
    return requestPackedApi({
      method: "POST",
      url: `/api/RolesManage/RoleList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
              * RoleAll_PostAsync /api/RolesManage/RoleAll
              * 角色列表
完整
              */
  static async RoleAll_PostAsync(
    params: { roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<Role[]> {
    return requestPackedApi({
      method: "POST",
      url: `/api/RolesManage/RoleAll`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * SetUserRole_PostAsync /api/RolesManage/SetUserRole
   * 设置用户角色
   */
  static async SetUserRole_PostAsync(
    params: { userId: GUID; roleId: GUID; expirationTime?: Date },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/RolesManage/SetUserRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RemoveUserRole_PostAsync /api/RolesManage/RemoveUserRole
   * 删除用户角色
   */
  static async RemoveUserRole_PostAsync(
    params: { userId: GUID; roleId: GUID },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/RolesManage/RemoveUserRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CreateRole_PostAsync /api/RolesManage/CreateRole
   * 创建角色
   */
  static async CreateRole_PostAsync(
    params: { roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<Role> {
    return requestPackedApi({
      method: "POST",
      url: `/api/RolesManage/CreateRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Delete_PostAsync /api/RolesManage/Delete
   * 删除角色
   */
  static async Delete_PostAsync(
    params: { roleId: GUID },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/RolesManage/Delete`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ModifyRole_PostAsync /api/RolesManage/ModifyRole
   * 修改角色
   */
  static async ModifyRole_PostAsync(
    params: { roleId: GUID; roleName?: string },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/RolesManage/ModifyRole`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ModifyRoleMenu_PostAsync /api/RolesManage/ModifyRoleMenu
   * 修改角色菜单
   */
  static async ModifyRoleMenu_PostAsync(
    params: { roleId: GUID },
    data: string[],
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/RolesManage/ModifyRoleMenu`,
      data,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class Statistics {
  /**
   * EventHandlingRatio_GetAsync /api/Statistics/EventHandlingRatio
   * 事件占比
   */
  static async EventHandlingRatio_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<EventHandlingRatioViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Statistics/EventHandlingRatio`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * IncidentHandlingTrends_GetAsync /api/Statistics/IncidentHandlingTrends
   * 事件处置趋势
   */
  static async IncidentHandlingTrends_GetAsync(
    params: {
      /**统计时间类型*/ type?: StatisticalTimeType;
      count: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IncidentHandlingTrendsViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Statistics/IncidentHandlingTrends`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AllEventCategoryStatistics_GetAsync /api/Statistics/AllEventCategoryStatistics
   * 全部的舆情统计数据
   */
  static async AllEventCategoryStatistics_GetAsync(
    options?: AxiosRequestConfig
  ): Promise<EventCategoryStatisticsViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Statistics/AllEventCategoryStatistics`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EventCategoryStatistics_GetAsync /api/Statistics/EventCategoryStatistics
   * 舆情分类统计
   */
  static async EventCategoryStatistics_GetAsync(
    params: { type?: StatisticalTimeType; count: number },
    options?: AxiosRequestConfig
  ): Promise<Record<Date, EventCategoryStatisticsViewModel[]>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Statistics/EventCategoryStatistics`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetHomeAsync /api/Statistics/GetHome
   * 首页统计
   */
  static async GetHomeAsync(
    options?: AxiosRequestConfig
  ): Promise<StatisticsViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Statistics/GetHome`,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetOpinionsDimensionAsync /api/Statistics/GetOpinionsDimension
   * 舆情多维度统计
   */
  static async GetOpinionsDimensionAsync(
    params: { start?: Date; end?: Date },
    options?: AxiosRequestConfig
  ): Promise<CountTip[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/Statistics/GetOpinionsDimension`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class SystemBaseInfo {
  /**
   * GetAsync /api/SystemBaseInfo/Get
   * 获取站点基本信息
   */
  static async GetAsync(options?: AxiosRequestConfig): Promise<SystemInfo> {
    return requestPackedApi({
      method: "GET",
      url: `/api/SystemBaseInfo/Get`,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class TagManages {
  /**
   * GetTagTreeAsync /api/TagManages/GetTagTree
   * 获取舆情分类树
   */
  static async GetTagTreeAsync(
    params: { firstTag?: string; tagType?: TagType },
    options?: AxiosRequestConfig
  ): Promise<TagManageView[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/TagManages/GetTagTree`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * FindById_GetAsync /api/TagManages/FindById
   * 删除标签（如存在子标签则禁止删除）
   */
  static async FindById_GetAsync(
    params: {
      /**标签ID*/ id: GUID;
    },
    options?: AxiosRequestConfig
  ): Promise<TagManage> {
    return requestPackedApi({
      method: "GET",
      url: `/api/TagManages/FindById`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * AddTag_PostAsync /api/TagManages/AddTag
   * 添加标签
   */
  static async AddTag_PostAsync(
    data: TagManage,
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/TagManages/AddTag`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * DeleteTag_PostAsync /api/TagManages/DeleteTag
   * 删除标签（如存在子标签则禁止删除）
   */
  static async DeleteTag_PostAsync(
    params: {
      /**标签ID*/ id: GUID;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/TagManages/DeleteTag`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Save_PostAsync /api/TagManages/Save
   * 保存
   */
  static async Save_PostAsync(
    data: TagManage,
    options?: AxiosRequestConfig
  ): Promise<TagManage> {
    return requestPackedApi({
      method: "POST",
      url: `/api/TagManages/Save`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class UserManage {
  /**
   * GetUserListAsync /api/UserManage/GetUserList
   * 返回用户列表
   */
  static async GetUserListAsync(
    params: {
      /**角色Id*/ roleId?: GUID;
      /**用户名*/
      userName?: string;
      /**邮箱*/
      email?: string;
      /**电话*/
      phoneNumber?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserViewModel>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/GetUserList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUserViewListAsync /api/UserManage/GetUserViewList
   * 返回用户列表
   */
  static async GetUserViewListAsync(
    params: {
      /**角色Id*/ roleId?: GUID;
      /**用户名*/
      userName?: string;
      /**邮箱*/
      email?: string;
      /**电话*/
      phoneNumber?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<GuidIdNameViewModel>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/GetUserViewList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUserViewListRefAsync /api/UserManage/GetUserViewListRef
   *
   */
  static async GetUserViewListRefAsync(
    params: {
      roleId?: GUID;
      userName?: string;
      email?: string;
      phoneNumber?: string;
    },
    options?: AxiosRequestConfig
  ): Promise<GuidIdNameViewModel[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/GetUserViewListRef`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * GetUserViewModelAsync /api/UserManage/GetUserViewModel
   * 用户信息
   */
  static async GetUserViewModelAsync(
    params: {
      /**用户ID*/ userId: GUID;
    },
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/GetUserViewModel`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UserLoginLogList_GetAsync /api/UserManage/UserLoginLogList
   * 获取用户的登录日志
   */
  static async UserLoginLogList_GetAsync(
    params: { userId: GUID; offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserLoginLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/UserLoginLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * LoginLogList_GetAsync /api/UserManage/LoginLogList
   * 获取所有用户的登录日志
   */
  static async LoginLogList_GetAsync(
    params: { offset?: number; limit?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserLoginLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/LoginLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * RequestLogList_GetAsync /api/UserManage/RequestLogList
   * 获取所有用户的请求日志
   */
  static async RequestLogList_GetAsync(
    params: {
      /**用户行为*/ des?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserRequestLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/RequestLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UserRequestLogList_GetAsync /api/UserManage/UserRequestLogList
   * 获取用户的请求日志
   */
  static async UserRequestLogList_GetAsync(
    params: {
      /**用户Id*/ userId: GUID;
      /**用户行为*/
      des?: string;
      offset?: number;
      limit?: number;
    },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<UserRequestLog>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/UserManage/UserRequestLogList`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * LockUser_GetAsync /api/UserManage/LockUser
   * 禁用用户
   */
  static async LockUser_GetAsync(
    params: { userId: GUID },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "GET",
      url: `/api/UserManage/LockUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * UnLockUser_GetAsync /api/UserManage/UnLockUser
   * 取消禁用用户
   */
  static async UnLockUser_GetAsync(
    params: {
      /**用户Id*/ userId: GUID;
    },
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "GET",
      url: `/api/UserManage/UnLockUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserModel_PostAsync /api/UserManage/EditUserModel
   * 修改用户信息
   */
  static async EditUserModel_PostAsync(
    data: UserEditModel,
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/UserManage/EditUserModel`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * DeleteUser_PostAsync /api/UserManage/DeleteUser
   * 删除用户
   */
  static async DeleteUser_PostAsync(
    params: {
      /**用户Id*/ userId: GUID;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/UserManage/DeleteUser`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserPassword_PostAsync /api/UserManage/EditUserPassword
   * 修改用户的密码
   */
  static async EditUserPassword_PostAsync(
    data: UserPasswordChangeEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/UserManage/EditUserPassword`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserExpiration_PostAsync /api/UserManage/EditUserExpiration
   * 修改用户过期时间
   */
  static async EditUserExpiration_PostAsync(
    data: UserExpirationEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/UserManage/EditUserExpiration`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * CreateUser_PostAsync /api/UserManage/CreateUser
   * 创建用户
   */
  static async CreateUser_PostAsync(
    data: UserCreateModel,
    options?: AxiosRequestConfig
  ): Promise<UserViewModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/UserManage/CreateUser`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * EditUserModifyPasswordEnd_PostAsync /api/UserManage/EditUserModifyPasswordEnd
   * 修改用户密码过期时间
   */
  static async EditUserModifyPasswordEnd_PostAsync(
    data: UserExpirationEditModel,
    options?: AxiosRequestConfig
  ): Promise<ApiResult> {
    return apiOptions.request({
      method: "POST",
      url: `/api/UserManage/EditUserModifyPasswordEnd`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class UserRegistration {
  /**
   * Register_PostAsync /api/UserRegistration/Register
   * 注册提交
   */
  static async Register_PostAsync(
    data: UserRegisterEditModel,
    options?: AxiosRequestConfig
  ): Promise<RegisteringValidationModel> {
    return requestPackedApi({
      method: "POST",
      url: `/api/UserRegistration/Register`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
export class WebSources {
  /**
   * List_GetAsync /api/WebSources/List
   * 来源查询
   */
  static async List_GetAsync(
    params: { domain?: string; name?: string },
    options?: AxiosRequestConfig
  ): Promise<WebSource[]> {
    return requestPackedApi({
      method: "GET",
      url: `/api/WebSources/List`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * ListPage_GetAsync /api/WebSources/ListPage
   * 来源查询
   */
  static async ListPage_GetAsync(
    params: { domain?: string; name?: string; limit?: number; offset?: number },
    options?: AxiosRequestConfig
  ): Promise<IPagedEnumerable<WebSource>> {
    return requestPackedApi({
      method: "GET",
      url: `/api/WebSources/ListPage`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Delete_PostAsync /api/WebSources/Delete
   * 删除
   */
  static async Delete_PostAsync(
    params: {
      /**标签ID*/ id: GUID;
    },
    options?: AxiosRequestConfig
  ): Promise<boolean> {
    return requestPackedApi({
      method: "POST",
      url: `/api/WebSources/Delete`,
      params,
      responseType: "json",
      ...(options || {}),
    });
  }
  /**
   * Save_PostAsync /api/WebSources/Save
   * 保存
   */
  static async Save_PostAsync(
    data: WebSource,
    options?: AxiosRequestConfig
  ): Promise<WebSource> {
    return requestPackedApi({
      method: "POST",
      url: `/api/WebSources/Save`,
      data,
      responseType: "json",
      ...(options || {}),
    });
  }
}
/**
 * Files_GetAsync /files/{id}
 * 跳转文件
 */ export async function Files_GetAsync(
  params: { id: GUID },
  options?: AxiosRequestConfig
): Promise<Blob> {
  return request({
    method: "GET",
    url: `/files/${params.id}`,
    params,
    responseType: "json",
    ...(options || {}),
  });
}
