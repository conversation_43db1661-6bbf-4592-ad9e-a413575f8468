import {
  require_export,
  require_global_this
} from "./chunk-H7BXG7YW.js";
import {
  __commonJS
} from "./chunk-OL46QLBJ.js";

// ../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/modules/es.global-this.js
var require_es_global_this = __commonJS({
  "../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/modules/es.global-this.js"() {
    "use strict";
    var $ = require_export();
    var globalThis = require_global_this();
    $({ global: true, forced: globalThis.globalThis !== globalThis }, {
      globalThis
    });
  }
});

// ../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/modules/esnext.global-this.js
var require_esnext_global_this = __commonJS({
  "../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/modules/esnext.global-this.js"() {
    "use strict";
    require_es_global_this();
  }
});

// ../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/es/global-this.js
var require_global_this2 = __commonJS({
  "../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/es/global-this.js"(exports, module) {
    "use strict";
    require_es_global_this();
    module.exports = require_global_this();
  }
});

// ../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/stable/global-this.js
var require_global_this3 = __commonJS({
  "../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/stable/global-this.js"(exports, module) {
    "use strict";
    var parent = require_global_this2();
    module.exports = parent;
  }
});

// ../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/actual/global-this.js
var require_global_this4 = __commonJS({
  "../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/actual/global-this.js"(exports, module) {
    "use strict";
    var parent = require_global_this3();
    module.exports = parent;
  }
});

// ../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/full/global-this.js
var require_global_this5 = __commonJS({
  "../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/full/global-this.js"(exports, module) {
    "use strict";
    require_esnext_global_this();
    var parent = require_global_this4();
    module.exports = parent;
  }
});

// ../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/features/global-this.js
var require_global_this6 = __commonJS({
  "../../node_modules/.pnpm/core-js@3.42.0/node_modules/core-js/features/global-this.js"(exports, module) {
    module.exports = require_global_this5();
  }
});
export default require_global_this6();
//# sourceMappingURL=core-js_features_global-this.js.map
