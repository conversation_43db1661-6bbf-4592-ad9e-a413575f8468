<template>
  <span v-if="getBindValue.readOnly">
    <template v-if="Array.isArray(modeValue)">
      <a-tag
        v-for="p in modeValue"
        :key="p"
        class="mb-2 mr-2 transition-all duration-300 hover:scale-105"
      >
        {{ p }}
      </a-tag>
    </template>

    <div v-else class="rounded-lg bg-gray-50 p-2">
      <div class="font-medium">{{ groupValue }}/{{ modeValue }}</div>
      <div class="mt-2 rounded bg-orange-50 p-2 text-sm text-gray-500">{{ remark }}</div>
    </div>
  </span>

  <div v-else class="group-select-wrapper">
    <span class="mb-2 block text-base font-medium">{{ groupValue }}</span>
    <a-select
      v-model:value="modeValue"
      style="width: 100%"
      :options="options"
      v-bind="$attrs"
      :field-names="{ label: 'label', value: 'value', options: 'children' }"
      option-label-prop="label"
      :dropdown-match-select-width="false"
      class="custom-select"
      @change="changeValue"
    >
      <template #option="{ label, remark }">
        <div class="option-item">
          <div class="font-medium">{{ label }}</div>
          <div class="mt-1 text-sm text-gray-500">{{ remark }}</div>
        </div>
      </template>
    </a-select>

    <div class="mt-2 rounded bg-orange-50 p-2 text-sm text-gray-500 transition-all duration-300">
      {{ remark }}
    </div>
  </div>
</template>

<script lang='ts' setup>
import type { FormConfig } from 'ch2-components/lib/form/types'
import type { StandardItem, StandardModel } from 'ch2-components/lib/standard/types'
import * as api from '@/api'
import { useInjectFormConfig } from 'ch2-components/lib/form/src/useFormConfig'

const modeValue = defineModel('value')

const groupValue = defineModel('group')

const { getBindValue } = useInjectFormConfig<FormConfig>({ name: 'select', attrs: useAttrs() })

const options = ref<StandardItem[]>([])

const compressor = new StandardCompressor()

const remark = computed(() => {
  return options.value.flatMap(v => v.children).find(item => item.value === modeValue.value)?.remark ?? ''
})

function changeValue(v: any, data: any) {
  groupValue.value = data.parent.value
}

function addParentReference(nodes: any, parent = null) {
  return nodes.map((node: any) => {
    node.parent = parent
    if (Array.isArray(node.children)) {
      node.children = addParentReference(node.children, node)
    }
    return node
  })
}

async function getOptions() {
  const { getData, setData } = useCache<StandardModel>(`standard-event_type`)
  let data = getData()
  if (data == null) {
    const res = await api.BaseInfoManage.GetInfoAsync({ key: `standard-event_type` }).then((res) => {
      const data = {
        id: res.id as any,
        key: res.key!,
        remark: res.description!,
        standardItems: compressor.decompress(res.data) as StandardItem[],
      } as StandardModel
      return data
    })
    setData(res)
    data = res
  }
  options.value = addParentReference(data.standardItems as any)
  console.log('%c [ options.value ]-86', 'font-size:13px; background:pink; color:#bf2c9f;', options.value)
}

onMounted(() => {
  getOptions()
})
</script>

<style scoped>
.group-select-wrapper {
  @apply w-full;
}

.custom-select {
  @apply transition-all duration-300;
}

.custom-select:hover {
  @apply shadow-md;
}

.option-item {
  @apply p-2 hover:bg-gray-50 transition-colors duration-200;
}

:deep(.ant-select-dropdown) {
  @apply shadow-lg rounded-lg;
}

:deep(.ant-select-item) {
  @apply transition-colors duration-200;
}

:deep(.ant-select-item-option-selected) {
  @apply bg-blue-50;
}
</style>
