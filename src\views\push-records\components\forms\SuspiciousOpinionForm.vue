<template>
  <div class="suspicious-opinion-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="选择舆情" name="publicOpinionId" required>
        <a-select
          v-model:value="formData.publicOpinionId"
          placeholder="请选择要推送的舆情"
          show-search
          :filter-option="false"
          :loading="opinionLoading"
          @search="searchOpinions"
          @change="onOpinionChange"
        >
          <a-select-option
            v-for="opinion in opinionList"
            :key="opinion.id"
            :value="opinion.id"
          >
            {{ opinion.summary }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="推送摘要" name="abstractText">
        <a-textarea
          v-model:value="formData.abstractText"
          placeholder="请输入推送摘要（可选）"
          :rows="3"
          :maxlength="200"
          show-count
        />
      </a-form-item>

      <a-form-item label="推送用户" name="pushTargets" required>
        <div class="space-y-2">
          <div v-for="user in userList" :key="user.id" class="flex items-center space-x-2">
            <a-checkbox
              :checked="isUserSelected(user.id!)"
              @change="(e) => onUserSelect(user.id!, e.target.checked)"
            >
              {{ user.name }}
            </a-checkbox>
            <div v-if="isUserSelected(user.id!)" class="flex space-x-2">
              <a-checkbox
                :checked="isChannelSelected(user.id!, DeptPushChannel.微信)"
                @change="(e) => onChannelSelect(user.id!, DeptPushChannel.微信, e.target.checked)"
              >
                微信
              </a-checkbox>
              <a-checkbox
                :checked="isChannelSelected(user.id!, DeptPushChannel.邮箱)"
                @change="(e) => onChannelSelect(user.id!, DeptPushChannel.邮箱, e.target.checked)"
              >
                邮箱
              </a-checkbox>
              <a-checkbox
                :checked="isChannelSelected(user.id!, DeptPushChannel.电话)"
                @change="(e) => onChannelSelect(user.id!, DeptPushChannel.电话, e.target.checked)"
              >
                电话
              </a-checkbox>
            </div>
          </div>
        </div>
      </a-form-item>

      <a-form-item>
        <a-checkbox v-model:checked="formData.force">
          强制推送（即使已推送过）
        </a-checkbox>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import type { DeptMainPushView, PublicOpinionViewModel, PushTarget, UserViewModel } from '@/api/models'
import * as api from '@/api'
import { DeptPushChannel } from '@/api/models'

const props = defineProps<{
  deptRecord?: DeptMainPushView
}>()

const emit = defineEmits<{
  (e: 'ready'): void
}>()

const formRef = ref()
const opinionLoading = ref(false)
const opinionList = ref<PublicOpinionViewModel[]>([])
const userList = ref<UserViewModel[]>([])

const formData = ref({
  publicOpinionId: '',
  abstractText: '',
  pushTargets: [] as PushTarget[],
  force: false,
})

const rules = {
  publicOpinionId: [
    { required: true, message: '请选择要推送的舆情', trigger: 'change' },
  ],
  pushTargets: [
    { required: true, message: '请选择推送用户', trigger: 'change' },
  ],
}

// 搜索舆情
async function searchOpinions(keyword: string) {
  if (!keyword)
    return

  try {
    opinionLoading.value = true
    const result = await api.OpinionManage.GetAsync({
      summary: keyword,
      limit: 20,
      offset: 0,
    })
    opinionList.value = result.items || []
  }
  catch (error) {
    console.error('搜索舆情失败:', error)
  }
  finally {
    opinionLoading.value = false
  }
}

// 舆情选择变化
function onOpinionChange(value: string) {
  const selectedOpinion = opinionList.value.find(op => op.id === value)
  if (selectedOpinion) {
    formData.value.abstractText = selectedOpinion.summary || ''
  }
}

// 获取用户列表
async function getUserList() {
  if (!props.deptRecord?.deptId)
    return

  try {
    userList.value = await api.DeptPushLogs.GetPushUserAsync({
      departmentId: props.deptRecord.deptId,
    })
  }
  catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 检查用户是否被选中
function isUserSelected(userId: string): boolean {
  return formData.value.pushTargets.some(target => target.pushUserId === userId)
}

// 检查渠道是否被选中
function isChannelSelected(userId: string, channel: DeptPushChannel): boolean {
  const target = formData.value.pushTargets.find(t => t.pushUserId === userId)
  return target?.channels?.includes(channel) || false
}

// 用户选择变化
function onUserSelect(userId: string, checked: boolean) {
  if (checked) {
    if (!isUserSelected(userId)) {
      formData.value.pushTargets.push({
        pushUserId: userId,
        channels: [DeptPushChannel.微信],
      })
    }
  }
  else {
    formData.value.pushTargets = formData.value.pushTargets.filter(
      target => target.pushUserId !== userId,
    )
  }
}

// 渠道选择变化
function onChannelSelect(userId: string, channel: DeptPushChannel, checked: boolean) {
  const target = formData.value.pushTargets.find(t => t.pushUserId === userId)
  if (!target)
    return

  if (!target.channels) {
    target.channels = []
  }

  if (checked) {
    if (!target.channels.includes(channel)) {
      target.channels.push(channel)
    }
  }
  else {
    target.channels = target.channels.filter(c => c !== channel)
  }
}

// 提交表单
async function submit() {
  await formRef.value?.validate()

  if (formData.value.pushTargets.length === 0) {
    throw new Error('请选择推送用户')
  }

  // 调用推送API
  await api.OpinionManage.SendPublicOpinionsToCooperateDept_PostAsync(
    {
      abstractText: formData.value.abstractText,
      publicOpinionId: formData.value.publicOpinionId,
      force: formData.value.force,
    },
    formData.value.pushTargets,
  )
}

// 初始化
onMounted(() => {
  getUserList()
  emit('ready')
})

defineExpose({
  submit,
})
</script>

<style scoped lang="less">
.suspicious-opinion-form {
  .space-y-2 > * + * {
    margin-top: 8px;
  }

  .space-x-2 > * + * {
    margin-left: 8px;
  }
}
</style>
