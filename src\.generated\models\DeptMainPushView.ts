import { PushStatus } from "./PushStatus";
export class DeptMainPushView {
  /**单位名*/
  deptName?: string | null | undefined = null;
  /**单位id*/
  deptId: GUID = "00000000-0000-0000-0000-000000000000";
  /**今日推送舆情*/
  toDayPubliceOpinionCount: number = 0;
  /**七日内推送舆情*/
  sevenDaysCount: number = 0;
  /**是否推送日报*/
  isPushToDayReport: PushStatus = 0;
  /**推送要求*/
  remark?: string | null | undefined = null;
  /**合作开始时间 只给舆情组角色看*/
  contractStartTime?: Dayjs | null | undefined = null;
  /**合作结束期限 只给舆情组角色看*/
  contractEndTime?: Dayjs | null | undefined = null;
  /**自动推送*/
  autoPush: boolean = false;
}
