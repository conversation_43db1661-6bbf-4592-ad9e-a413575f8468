import { PushLog } from "./PushLog";
import { Department } from "./Department";
import { RiskLevel } from "./RiskLevel";
import { User } from "./User";
import { AuditType } from "./AuditType";
import { HandledType } from "./HandledType";
import { PublicEvent } from "./PublicEvent";
import { Positive } from "./Positive";
import { ToDayBriefing } from "./ToDayBriefing";
import { PublicOpinionTopic } from "./PublicOpinionTopic";
import { PublicOpinionSubmission } from "./PublicOpinionSubmission";
export class PublicOpinion {
  jytEntityId?: GUID = null;
  pushLog?: PushLog[] | null | undefined = [];
  /**记录相关内容或描述*/
  content?: string | null | undefined = null;
  /**多个以回车分割*/
  url?: string | null | undefined = null;
  summary?: string | null | undefined = null;
  /**（采用逗号分隔字符串）*/
  tags?: string | null | undefined = null;
  mainCategory?: string | null | undefined = null;
  category?: string | null | undefined = null;
  department?: string | null | undefined = null;
  deptId?: GUID = null;
  /**部门表*/
  dept?: Department | null | undefined = null;
  source?: string | null | undefined = null;
  sourceId?: string | null | undefined = null;
  publisher?: string | null | undefined = null;
  published?: Dayjs | null | undefined = null;
  riskLevel: RiskLevel = 0;
  createdBy?: GUID = null;
  /**用户*/
  createdUser?: User | null | undefined = null;
  createdAt: Dayjs = dayjs();
  modifiedBy?: GUID = null;
  /**用户*/
  modifiedUser?: User | null | undefined = null;
  modifiedAt?: Dayjs | null | undefined = null;
  auditBy?: GUID = null;
  /**用户*/
  auditor?: User | null | undefined = null;
  auditAt?: Dayjs | null | undefined = null;
  auditStatus: AuditType = 0;
  handledBy?: GUID = null;
  /**用户*/
  handledUser?: User | null | undefined = null;
  handledAt?: Dayjs | null | undefined = null;
  handledStatus?: HandledType | null | undefined = null;
  handledText?: string | null | undefined = null;
  handledAttachmentIds?: Array<GUID> = [];
  version: number = 0;
  eventId?: GUID = null;
  event?: PublicEvent | null | undefined = null;
  serious: boolean = false;
  positive: Positive = 0;
  involved: number = 0;
  death: number = 0;
  injured: number = 0;
  trafficCategory?: string | null | undefined = null;
  counties?: string | null | undefined = null;
  address?: string | null | undefined = null;
  address1?: string | null | undefined = null;
  redundancy1?: string | null | undefined = null;
  redundancy2?: string | null | undefined = null;
  redundancy3?: string | null | undefined = null;
  toDayBriefing?: ToDayBriefing[] | null | undefined = [];
  isHiddenDanger?: boolean | null | undefined = null;
  topicId?: GUID = null;
  topic?: PublicOpinionTopic | null | undefined = null;
  images?: string[] | null | undefined = [];
  /**舆情秘书推送数据记录表*/
  submission?: PublicOpinionSubmission | null | undefined = null;
  submissionId?: GUID = null;
  /**主键*/
  id: GUID = "00000000-0000-0000-0000-000000000000";
}
