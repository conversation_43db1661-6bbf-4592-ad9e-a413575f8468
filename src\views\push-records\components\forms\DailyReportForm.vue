<template>
  <div class="daily-report-form">
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      layout="vertical"
    >
      <a-form-item label="日报日期" name="reportDate" required>
        <a-date-picker
          v-model:value="formData.reportDate"
          placeholder="请选择日报日期"
          style="width: 100%"
          :disabled-date="disabledDate"
        />
      </a-form-item>

      <a-form-item label="推送摘要" name="abstractText">
        <a-textarea
          v-model:value="formData.abstractText"
          placeholder="请输入推送摘要（可选）"
          :rows="3"
          :maxlength="200"
          show-count
        />
      </a-form-item>

      <a-form-item label="推送用户" name="pushTargets" required>
        <div class="space-y-2">
          <div v-for="user in userList" :key="user.id" class="flex items-center space-x-2">
            <a-checkbox
              :checked="isUserSelected(user.id!)"
              @change="(e) => onUserSelect(user.id!, e.target.checked)"
            >
              {{ user.name }}
            </a-checkbox>
            <div v-if="isUserSelected(user.id!)" class="flex space-x-2">
              <a-checkbox
                :checked="isChannelSelected(user.id!, DeptPushChannel.微信)"
                @change="(e) => onChannelSelect(user.id!, DeptPushChannel.微信, e.target.checked)"
              >
                微信
              </a-checkbox>
              <a-checkbox
                :checked="isChannelSelected(user.id!, DeptPushChannel.邮箱)"
                @change="(e) => onChannelSelect(user.id!, DeptPushChannel.邮箱, e.target.checked)"
              >
                邮箱
              </a-checkbox>
              <a-checkbox
                :checked="isChannelSelected(user.id!, DeptPushChannel.电话)"
                @change="(e) => onChannelSelect(user.id!, DeptPushChannel.电话, e.target.checked)"
              >
                电话
              </a-checkbox>
            </div>
          </div>
        </div>
      </a-form-item>

      <a-form-item>
        <a-checkbox v-model:checked="formData.force">
          强制推送（即使已推送过）
        </a-checkbox>
      </a-form-item>
    </a-form>
  </div>
</template>

<script lang="ts" setup>
import type { DeptMainPushView, PushTarget, UserViewModel } from '@/api/models'
import type { Dayjs } from 'dayjs'
import * as api from '@/api'
import { DeptPushChannel } from '@/api/models'
import dayjs from 'dayjs'

const props = defineProps<{
  deptRecord?: DeptMainPushView
}>()

const emit = defineEmits<{
  (e: 'ready'): void
}>()

const formRef = ref()
const userList = ref<UserViewModel[]>([])

const formData = ref({
  reportDate: dayjs() as Dayjs,
  abstractText: '',
  pushTargets: [] as PushTarget[],
  force: false,
})

const rules = {
  reportDate: [
    { required: true, message: '请选择日报日期', trigger: 'change' },
  ],
  pushTargets: [
    { required: true, message: '请选择推送用户', trigger: 'change' },
  ],
}

// 禁用未来日期
function disabledDate(current: Dayjs) {
  return current && current > dayjs().endOf('day')
}

// 获取用户列表
async function getUserList() {
  if (!props.deptRecord?.deptId)
    return

  try {
    userList.value = await api.DeptPushLogs.GetPushUserAsync({
      departmentId: props.deptRecord.deptId,
    })
  }
  catch (error) {
    console.error('获取用户列表失败:', error)
  }
}

// 检查用户是否被选中
function isUserSelected(userId: string): boolean {
  return formData.value.pushTargets.some(target => target.pushUserId === userId)
}

// 检查渠道是否被选中
function isChannelSelected(userId: string, channel: DeptPushChannel): boolean {
  const target = formData.value.pushTargets.find(t => t.pushUserId === userId)
  return target?.channels?.includes(channel) || false
}

// 用户选择变化
function onUserSelect(userId: string, checked: boolean) {
  if (checked) {
    if (!isUserSelected(userId)) {
      formData.value.pushTargets.push({
        pushUserId: userId,
        channels: [DeptPushChannel.微信],
      })
    }
  }
  else {
    formData.value.pushTargets = formData.value.pushTargets.filter(
      target => target.pushUserId !== userId,
    )
  }
}

// 渠道选择变化
function onChannelSelect(userId: string, channel: DeptPushChannel, checked: boolean) {
  const target = formData.value.pushTargets.find(t => t.pushUserId === userId)
  if (!target)
    return

  if (!target.channels) {
    target.channels = []
  }

  if (checked) {
    if (!target.channels.includes(channel)) {
      target.channels.push(channel)
    }
  }
  else {
    target.channels = target.channels.filter(c => c !== channel)
  }
}

// 提交表单
async function submit() {
  await formRef.value?.validate()

  if (formData.value.pushTargets.length === 0) {
    throw new Error('请选择推送用户')
  }

  // 调用日报推送API
  await api.OpinionManage.SendDailyNewspaperToCooperateDept_PostAsync(
    {
      abstractText: formData.value.abstractText,
    },
    formData.value.pushTargets,
  )
}

// 初始化
onMounted(() => {
  getUserList()
  emit('ready')
})

defineExpose({
  submit,
})
</script>

<style scoped lang="less">
.daily-report-form {
  .space-y-2 > * + * {
    margin-top: 8px;
  }

  .space-x-2 > * + * {
    margin-left: 8px;
  }
}
</style>
