import { IdentityUserRole } from "./IdentityUserRole";
import { User } from "./User";
import { Role } from "./Role";
/**用户角色表*/
export class UserRole extends IdentityUserRole<string> {
  /**用户*/
  user?: User | null | undefined = null;
  /**角色表*/
  role?: Role | null | undefined = null;
  /**创建时间*/
  created: Dayjs = dayjs();
  /**修改时间*/
  modified?: Dayjs | null | undefined = null;
  /**创建者*/
  createdBy?: string | null | undefined = null;
  /**修改者*/
  modifiedBy?: string | null | undefined = null;
}
