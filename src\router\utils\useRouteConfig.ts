/*
 * @Description: 当前访问路由持久化
 * @Author: luckymiaow
 * @Date: 2022-09-02 10:08:45
 * @LastEditors: luckymiaow
 */

import type { UserRoleViewModel } from '@/api/models'

import type { RouteNamedMap } from 'vue-router/auto-routes'

import { reactive } from 'vue'

/* 首页 */
export const HomeRoute = reactive<{ path: keyof RouteNamedMap }>({
  path: '/home',
})

export const LoginRoute = reactive<{ path: keyof RouteNamedMap }>({
  path: '/login/',
})

export function setHomeRoute(data: { path: keyof RouteNamedMap }) {
  HomeRoute.path = data.path
}

export function setHomeRouteByRoles(_roles: UserRoleViewModel[]) {
  if (_roles[0]) {
    HomeRoute.path = _roles[0]?.menu?.[0] as any || '/home'
  }
}

(function isMobileDevice() {
  // const screenWidth = window.innerWidth;
  // if (!/<PERSON><PERSON>|Android|iPhone|iPad|iPod|Opera Mini|IEMobile|WPDesktop/i.test(navigator.userAgent) && screenWidth > 500)
})()
